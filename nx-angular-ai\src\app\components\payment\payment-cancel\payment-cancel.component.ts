import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { NavbarComponent } from '../../landing/navbar/navbar.component';
import { FooterComponent } from '../../landing/footer/footer.component';

@Component({
  selector: 'app-payment-cancel',
  standalone: true,
  imports: [CommonModule, RouterModule, NavbarComponent, FooterComponent],
  template: `
    <div class="min-h-screen bg-gray-50 flex flex-col">
      <app-navbar></app-navbar>
      
      <div class="flex-grow flex items-center justify-center">
        <div class="max-w-md w-full bg-white shadow-lg rounded-lg p-8 mx-4">
          <div class="text-center">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100">
              <svg class="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </div>
            <h2 class="mt-4 text-2xl font-bold text-gray-900">Payment Cancelled</h2>
            <p class="mt-2 text-gray-600">
              Your payment was cancelled or failed to process. No charges have been made to your account.
            </p>
            <div class="mt-6 space-y-3">
              <button 
                (click)="retryPayment()" 
                class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Try Again
              </button>
              <button 
                (click)="goHome()" 
                class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Go to Home
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <app-footer></app-footer>
    </div>
  `,
})
export class PaymentCancelComponent {
  constructor(private router: Router) {}

  retryPayment(): void {
    this.router.navigate(['/pricing']);
  }

  goHome(): void {
    this.router.navigate(['/']);
  }
}
