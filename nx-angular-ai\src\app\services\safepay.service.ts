import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface PaymentPlan {
  name: string;
  amount: number;
  currency: string;
  interval: string;
}

export interface SafepayPaymentRequest {
  amount: number;
  currency: string;
  order_id: string;
  description: string;
  success_url: string;
  cancel_url: string;
  customer: {
    email: string;
    phone?: string;
    first_name?: string;
    last_name?: string;
  };
  metadata?: any;
}

export interface SafepayPaymentResponse {
  token: string;
  checkout_url: string;
  order_id: string;
  status: string;
}

@Injectable({
  providedIn: 'root'
})
export class SafepayService {
  private readonly SAFEPAY_API_URL = 'https://api.getsafepay.pk/v1';
  private readonly SAFEPAY_API_KEY = 'your-safepay-api-key'; // Replace with your actual API key

  constructor(private http: HttpClient) {}

  createPayment(plan: PaymentPlan): void {
    // Generate unique order ID
    const orderId = this.generateOrderId();
    
    const paymentRequest: SafepayPaymentRequest = {
      amount: plan.amount * 100, // SafePay expects amount in cents
      currency: plan.currency,
      order_id: orderId,
      description: `${plan.name} - ${plan.interval} subscription`,
      success_url: `${window.location.origin}/payment/success`,
      cancel_url: `${window.location.origin}/payment/cancel`,
      customer: {
        email: '<EMAIL>', // This should come from authenticated user
        first_name: 'User',
        last_name: 'Name'
      },
      metadata: {
        plan_type: plan.name,
        interval: plan.interval,
        user_id: 'current-user-id' // This should come from authenticated user
      }
    };

    this.initiatePayment(paymentRequest).subscribe({
      next: (response) => {
        // Redirect to SafePay checkout
        window.location.href = response.checkout_url;
      },
      error: (error) => {
        console.error('Payment initiation failed:', error);
        alert('Payment initiation failed. Please try again.');
      }
    });
  }

  private initiatePayment(paymentRequest: SafepayPaymentRequest): Observable<SafepayPaymentResponse> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${this.SAFEPAY_API_KEY}`,
      'Content-Type': 'application/json'
    });

    return this.http.post<SafepayPaymentResponse>(
      `${this.SAFEPAY_API_URL}/payments`,
      paymentRequest,
      { headers }
    );
  }

  verifyPayment(token: string): Observable<any> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${this.SAFEPAY_API_KEY}`,
      'Content-Type': 'application/json'
    });

    return this.http.get<any>(
      `${this.SAFEPAY_API_URL}/payments/${token}`,
      { headers }
    );
  }

  createRecurringPayment(plan: PaymentPlan): Observable<any> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${this.SAFEPAY_API_KEY}`,
      'Content-Type': 'application/json'
    });

    const recurringRequest = {
      amount: plan.amount * 100,
      currency: plan.currency,
      interval: plan.interval,
      description: `${plan.name} - Recurring ${plan.interval} subscription`,
      customer: {
        email: '<EMAIL>', // This should come from authenticated user
        first_name: 'User',
        last_name: 'Name'
      }
    };

    return this.http.post<any>(
      `${this.SAFEPAY_API_URL}/subscriptions`,
      recurringRequest,
      { headers }
    );
  }

  cancelSubscription(subscriptionId: string): Observable<any> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${this.SAFEPAY_API_KEY}`,
      'Content-Type': 'application/json'
    });

    return this.http.delete<any>(
      `${this.SAFEPAY_API_URL}/subscriptions/${subscriptionId}`,
      { headers }
    );
  }

  private generateOrderId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 15);
    return `order_${timestamp}_${random}`;
  }

  // Webhook handler for payment status updates
  handleWebhook(webhookData: any): void {
    switch (webhookData.event) {
      case 'payment.success':
        this.handlePaymentSuccess(webhookData.data);
        break;
      case 'payment.failed':
        this.handlePaymentFailed(webhookData.data);
        break;
      case 'subscription.created':
        this.handleSubscriptionCreated(webhookData.data);
        break;
      case 'subscription.cancelled':
        this.handleSubscriptionCancelled(webhookData.data);
        break;
      default:
        console.log('Unhandled webhook event:', webhookData.event);
    }
  }

  private handlePaymentSuccess(paymentData: any): void {
    console.log('Payment successful:', paymentData);
    // Update user subscription status
    // Send confirmation email
    // Redirect to success page
  }

  private handlePaymentFailed(paymentData: any): void {
    console.log('Payment failed:', paymentData);
    // Handle payment failure
    // Send failure notification
  }

  private handleSubscriptionCreated(subscriptionData: any): void {
    console.log('Subscription created:', subscriptionData);
    // Activate user subscription
    // Send welcome email
  }

  private handleSubscriptionCancelled(subscriptionData: any): void {
    console.log('Subscription cancelled:', subscriptionData);
    // Deactivate user subscription
    // Send cancellation confirmation
  }
}
