import { Route } from '@angular/router';
import { SignInComponent } from './components/auth/sign-in/sign-in.component';
import { SignUpComponent } from './components/auth/sign-up/sign-up.component';
import { ChatLayoutComponent } from './components/chat-layout/chat-layout.component';
import { LandingComponent } from './components/landing/landing.component';
import { PricingComponent } from './components/pricing/pricing.component';
import { AboutComponent } from './components/about/about.component';
import { authGuard, publicGuard } from './guards/auth.guard';

export const appRoutes: Route[] = [
  {
    path: '',
    component: LandingComponent
  },
  {
    path: 'pricing',
    component: PricingComponent
  },
  {
    path: 'about',
    component: AboutComponent
  },
  {
    path: 'sign-in',
    component: SignInComponent,
    canActivate: [publicGuard]
  },
  {
    path: 'sign-up',
    component: SignUpComponent,
    canActivate: [publicGuard]
  },
  {
    path: 'chat-layout',
    component: ChatLayoutComponent,
    canActivate: [authGuard]
  },
  {
    path: '**',
    redirectTo: '/'
  }
];
