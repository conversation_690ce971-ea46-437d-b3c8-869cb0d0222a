version: '3.8'

services:
  be-psychology-chat:
    build:
      context: .
      dockerfile: apps/psychology-chat/Dockerfile
    container_name: be_psychology_chat
    restart: always
    environment:
      - POSTGRES_HOST=host.docker.internal
      - POSTGRES_PORT=5432
      - POSTGRES_USER=service_user
      - POSTGRES_PASSWORD=Str0ng_S3rvic3_P@ssw0rd_2025!
      - POSTGRES_DB=psy_chat
      - JWT_SECRET=JK1zU0z2U7SiAo6
    ports:
      - "3003:3003"
    volumes:
      - .:/app
    networks:
      - psychology_chat_network
    extra_hosts:
      - "host.docker.internal:host-gateway"
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: "2GB"

networks:
  psychology_chat_network:
    driver: bridge
