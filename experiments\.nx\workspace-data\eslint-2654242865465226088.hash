{"1707690355647152622": {"apps/psychology-chat": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/psychology-chat"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.js", "{projectRoot}/eslint.config.js", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7288873528898617857": {"apps/psychology-chat-e2e": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/psychology-chat-e2e"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.js", "{projectRoot}/eslint.config.js", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12255708508329156068": {}, "3185308175221774653": {"libs/be-chat": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/be-chat"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.js", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "10472870790910246757": {"libs/be-database": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/be-database"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.js", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "6504129618623765756": {"libs/be-auth": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/be-auth"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.js", "{projectRoot}/eslint.config.js", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5137135976906092895": {}}