ARG POSTGRES_HOST
ARG POSTGRES_PORT
ARG POSTGRES_USER
ARG POSTGRES_PASSWORD
ARG POSTGRES_DB

# Use the specified Node.js version
FROM node:20.17.0

# Set the working directory inside the container
WORKDIR /usr/src/app

# Copy the package.json and package-lock.json files
COPY package*.json ./

# Install dependencies
RUN npm install --no-optional

# Copy the rest of the application code
COPY . .

# Build the service
RUN npm run build-be-psy-chat

# Expose the port your service runs on (adjust if necessary)
EXPOSE 3003

# Set the environment variables
ENV POSTGRES_HOST=$POSTGRES_HOST
ENV POSTGRES_PORT=$POSTGRES_PORT
ENV POSTGRES_USER=$POSTGRES_USER
ENV POSTGRES_PASSWORD=$POSTGRES_PASSWORD
ENV POSTGRES_DB=$POSTGRES_DB

# Start the service
CMD ["node", "dist/apps/psychology-chat/main.js"]
