[1m[33mComponent HMR has been enabled.[39m[22m
[1m[33mIf you encounter application reload issues, you can manually reload the page to bypass HMR and/or disable this feature with the `--no-hmr` command line option.[39m[22m
[1m[33mPlease consider reporting any issues you encounter here: https://github.com/angular/angular-cli/issues[39m[22m
[1m[33m[39m[22m
[33m❯[39m Building...
[32m✔[39m Building...
[37m[1mInitial chunk files[22m[2m | [22m[1mNames[22m        [2m | [22m [1mRaw size[22m[39m
[37m[32mmain.js[39m[37m            [2m | [22m[2mmain[22m         [2m | [22m[36m177.63 kB[39m[37m[2m | [22m[39m
[37m[32mpolyfills.js[39m[37m       [2m | [22m[2mpolyfills[22m    [2m | [22m [36m90.20 kB[39m[37m[2m | [22m[39m
[37m[32mstyles.css[39m[37m         [2m | [22m[2mstyles[22m       [2m | [22m [36m27.75 kB[39m[37m[2m | [22m[39m
[37m[39m
[37m[1m [22m                  [2m | [22m[1mInitial total[22m[2m | [22m[1m295.59 kB[22m[39m
[37m[39m
[37mApplication bundle generation complete. [4.499 seconds][39m
[37m[39m
[37mWatch mode enabled. Watching for file changes...[39m
[37mNOTE: Raw file sizes do not reflect development server per-request transformations.[39m

[0m[7m[1m[31m NX [39m[22m[27m[0m  [31mProxy configuration file D:\project\nx-angular-ai\proxy.conf.json contains parse errors:[39m

[7, 15] InvalidSymbol
[7, 27] ValueExpected
[7, 29] InvalidSymbol
[7, 32] PropertyNameExpected
[7, 32] ValueExpected
[7, 34] InvalidSymbol
[7, 48] PropertyNameExpected
[8, 7] InvalidSymbol
[8, 38] ValueExpected
[8, 40] InvalidSymbol
[11, 1] EndOfFileExpected
Pass --verbose to see the stacktrace.

