import { z } from 'zod';
import { createZodDto } from '@anatine/zod-nestjs';

// Define Zod Schema for AddMessageDto
export const AddMessageSchema = z.object({
  role: z.enum(['user', 'assistant']),
  content: z.string().min(1, 'Content is required'),
  timestamp: z.string().datetime().optional(),
  isComplete: z.boolean().default(true),
  thinking: z.string().optional(),
});

// Convert Zod Schema into NestJS DTO for Validation
export class AddMessageDto extends createZodDto(AddMessageSchema) {}

// Infer TypeScript Type from Zod Schema
export type AddMessageDtoType = z.infer<typeof AddMessageSchema>;
