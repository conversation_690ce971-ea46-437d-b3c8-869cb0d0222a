[NX Daemon Server] - 2025-07-07T08:49:14.479Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\df8e34c1cf49d3f47b5e\d.sock
[NX Daemon Server] - 2025-07-07T08:49:14.485Z - [WATCHER]: Subscribed to changes within: D:\project\experiments (native)
[NX Daemon Server] - 2025-07-07T08:49:14.533Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-07T08:49:14.534Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-07T08:49:14.541Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-07T08:49:14.544Z - [REQUEST]: Responding to the client. Shutdown initiated
[NX Daemon Server] - 2025-07-07T08:49:14.560Z - Done responding to the client Shutdown initiated
[NX Daemon Server] - 2025-07-07T08:49:14.560Z - Handled FORCE_SHUTDOWN. Handling time: 0. Response time: 16.
[NX Daemon Server] - 2025-07-07T08:49:14.561Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-07T08:49:14.561Z - [WATCHER]: Stopping the watcher for D:\project\experiments (sources)
[NX Daemon Server] - 2025-07-07T08:49:14.562Z - [WATCHER]: Stopping the watcher for D:\project\experiments (outputs)
[NX Daemon Server] - 2025-07-07T08:49:14.562Z - Server stopped because: "Request to shutdown"
[NX Daemon Server] - 2025-07-07T08:51:10.693Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\df8e34c1cf49d3f47b5e\d.sock
[NX Daemon Server] - 2025-07-07T08:51:10.697Z - [WATCHER]: Subscribed to changes within: D:\project\experiments (native)
[NX Daemon Server] - 2025-07-07T08:51:10.706Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-07T08:51:10.709Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-07T08:51:10.710Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-07T08:51:10.713Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-07T08:51:11.864Z - Time taken for 'Load Nx Plugin: D:\project\experiments\node_modules\nx\src\plugins\js' 1144.3262ms
[NX Daemon Server] - 2025-07-07T08:51:11.936Z - Time taken for 'loadDefaultNxPlugins' 1217.0465ms
[NX Daemon Server] - 2025-07-07T08:51:12.018Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 1302.3625ms
[NX Daemon Server] - 2025-07-07T08:51:12.229Z - Time taken for 'Load Nx Plugin: @nx/webpack/plugin' 1511.5710000000001ms
[NX Daemon Server] - 2025-07-07T08:51:12.455Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-07T08:51:12.455Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-07T08:51:12.455Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-07T08:51:12.496Z - Time taken for 'loadSpecifiedNxPlugins' 1737.2226ms
[NX Daemon Server] - 2025-07-07T08:51:12.498Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-07T08:51:12.498Z - Established a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-07T08:51:12.499Z - Established a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-07T08:51:12.499Z - Established a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-07T08:51:12.499Z - Closed a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-07T08:51:12.499Z - Closed a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-07T08:51:12.548Z - [REQUEST]: Responding to the client. handleGlob
[NX Daemon Server] - 2025-07-07T08:51:12.586Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:12.595Z - Done responding to the client handleGlob
[NX Daemon Server] - 2025-07-07T08:51:12.595Z - Handled GLOB. Handling time: 1. Response time: 47.
[NX Daemon Server] - 2025-07-07T08:51:12.595Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:12.595Z - Handled HASH_GLOB. Handling time: 35. Response time: 9.
[NX Daemon Server] - 2025-07-07T08:51:13.047Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.047Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.047Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-07T08:51:13.050Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.051Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.051Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:13.054Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.055Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.055Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:13.059Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.060Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.061Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:13.063Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.064Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.064Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:13.066Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.067Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-07T08:51:13.067Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:13.107Z - Time taken for 'build-project-configs' 596.3586ms
[NX Daemon Server] - 2025-07-07T08:51:13.165Z - [SYNC]: collect registered sync generators
[NX Daemon Server] - 2025-07-07T08:51:13.169Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-07T08:51:13.171Z - Time taken for 'total for creating and serializing project graph' 2454.1137ms
[NX Daemon Server] - 2025-07-07T08:51:13.173Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-07T08:51:13.173Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2455. Response time: 6.
[NX Daemon Server] - 2025-07-07T08:51:13.189Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-07T08:51:13.189Z - Time taken for 'preTasksExecution' 2.1044999999999163ms
[NX Daemon Server] - 2025-07-07T08:51:13.189Z - Done responding to the client handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-07T08:51:13.190Z - Handled PRE_TASKS_EXECUTION. Handling time: 3. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:13.308Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-07T08:51:13.309Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-07T08:51:13.309Z - Handled HASH_TASKS. Handling time: 34. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:13.332Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-07T08:51:13.333Z - Done responding to the client handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-07T08:51:13.333Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 2. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:23.401Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-07T08:51:23.472Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-07T08:51:23.472Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-07T08:51:23.473Z - Handled RECORD_OUTPUTS_HASH. Handling time: 5. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:24.247Z - Established a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-07T08:51:24.256Z - Closed a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-07T08:51:24.257Z - Established a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-07T08:51:24.260Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-07T08:51:24.261Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-07T08:51:24.263Z - Time taken for 'total for creating and serializing project graph' 0.7312000000001717ms
[NX Daemon Server] - 2025-07-07T08:51:24.264Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-07T08:51:24.265Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX Daemon Server] - 2025-07-07T08:51:24.274Z - Established a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-07T08:51:25.218Z - Established a connection. Number of open connections: 6
[NX Daemon Server] - 2025-07-07T08:51:25.219Z - Closed a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-07T08:51:25.221Z - Established a connection. Number of open connections: 6
[NX Daemon Server] - 2025-07-07T08:51:25.226Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-07T08:51:25.227Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-07T08:51:25.228Z - Time taken for 'total for creating and serializing project graph' 0.5485000000007858ms
[NX Daemon Server] - 2025-07-07T08:51:25.230Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-07T08:51:25.231Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX Daemon Server] - 2025-07-07T08:51:25.248Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-07T08:51:25.248Z - Time taken for 'preTasksExecution' 0.42189999999936845ms
[NX Daemon Server] - 2025-07-07T08:51:25.248Z - Done responding to the client handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-07T08:51:25.248Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-07T08:51:25.332Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-07T08:51:25.333Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-07T08:51:25.333Z - Handled HASH_TASKS. Handling time: 16. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:25.362Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-07T08:51:25.362Z - Done responding to the client handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-07T08:51:25.362Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 0. Response time: 0.
[NX Daemon Server] - 2025-07-07T08:51:25.375Z - [REQUEST]: Responding to the client. outputsHashesMatch
[NX Daemon Server] - 2025-07-07T08:51:25.375Z - Done responding to the client outputsHashesMatch
[NX Daemon Server] - 2025-07-07T08:51:25.375Z - Handled OUTPUTS_HASHES_MATCH. Handling time: 4. Response time: 0.
[NX Daemon Server] - 2025-07-07T08:51:25.381Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-07T08:51:25.382Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-07T08:51:25.382Z - Handled RECORD_OUTPUTS_HASH. Handling time: 3. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:25.389Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX Daemon Server] - 2025-07-07T08:51:25.389Z - Done responding to the client handleRecordTaskRuns
[NX Daemon Server] - 2025-07-07T08:51:25.389Z - Handled RECORD_TASK_RUNS. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-07T08:51:25.392Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX Daemon Server] - 2025-07-07T08:51:25.393Z - Done responding to the client handleGetFlakyTasks
[NX Daemon Server] - 2025-07-07T08:51:25.393Z - Handled GET_FLAKY_TASKS. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:25.401Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX Daemon Server] - 2025-07-07T08:51:25.401Z - Time taken for 'postTasksExecution' 0.6401000000005297ms
[NX Daemon Server] - 2025-07-07T08:51:25.401Z - Done responding to the client handleRunPostTasksExecution
[NX Daemon Server] - 2025-07-07T08:51:25.401Z - Handled POST_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-07T08:51:25.539Z - Closed a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-07T08:51:54.017Z - Established a connection. Number of open connections: 6
[NX Daemon Server] - 2025-07-07T08:51:54.018Z - Closed a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-07T08:51:54.022Z - Established a connection. Number of open connections: 6
[NX Daemon Server] - 2025-07-07T08:51:54.043Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-07T08:51:54.044Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-07T08:51:54.046Z - Time taken for 'total for creating and serializing project graph' 0.6617999999944004ms
[NX Daemon Server] - 2025-07-07T08:51:54.048Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-07T08:51:54.048Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX Daemon Server] - 2025-07-07T08:51:54.063Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-07T08:51:54.063Z - Time taken for 'preTasksExecution' 0.39849999999569263ms
[NX Daemon Server] - 2025-07-07T08:51:54.064Z - Done responding to the client handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-07T08:51:54.064Z - Handled PRE_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:54.154Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-07T08:51:54.155Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-07T08:51:54.155Z - Handled HASH_TASKS. Handling time: 18. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:54.181Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-07T08:51:54.181Z - Done responding to the client handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-07T08:51:54.182Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:54.198Z - [REQUEST]: Responding to the client. outputsHashesMatch
[NX Daemon Server] - 2025-07-07T08:51:54.199Z - Done responding to the client outputsHashesMatch
[NX Daemon Server] - 2025-07-07T08:51:54.199Z - Handled OUTPUTS_HASHES_MATCH. Handling time: 4. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:54.209Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-07T08:51:54.210Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-07T08:51:54.210Z - Handled RECORD_OUTPUTS_HASH. Handling time: 7. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:54.217Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX Daemon Server] - 2025-07-07T08:51:54.218Z - Done responding to the client handleRecordTaskRuns
[NX Daemon Server] - 2025-07-07T08:51:54.218Z - Handled RECORD_TASK_RUNS. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:54.221Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX Daemon Server] - 2025-07-07T08:51:54.222Z - Done responding to the client handleGetFlakyTasks
[NX Daemon Server] - 2025-07-07T08:51:54.222Z - Handled GET_FLAKY_TASKS. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:54.232Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX Daemon Server] - 2025-07-07T08:51:54.232Z - Time taken for 'postTasksExecution' 0.39270000000396976ms
[NX Daemon Server] - 2025-07-07T08:51:54.233Z - Done responding to the client handleRunPostTasksExecution
[NX Daemon Server] - 2025-07-07T08:51:54.233Z - Handled POST_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-07T08:51:54.239Z - Closed a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-08T07:35:22.309Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\df8e34c1cf49d3f47b5e\d.sock
[NX Daemon Server] - 2025-07-08T07:35:22.320Z - [WATCHER]: Subscribed to changes within: D:\project\experiments (native)
[NX Daemon Server] - 2025-07-08T07:35:22.322Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-08T07:35:22.323Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-08T07:35:22.327Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-08T07:35:22.329Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-08T07:35:23.407Z - Time taken for 'Load Nx Plugin: D:\project\experiments\node_modules\nx\src\plugins\package-json' 1064.9161ms
[NX Daemon Server] - 2025-07-08T07:35:23.429Z - Time taken for 'Load Nx Plugin: D:\project\experiments\node_modules\nx\src\plugins\js' 1088.5375ms
[NX Daemon Server] - 2025-07-08T07:35:23.446Z - Time taken for 'loadDefaultNxPlugins' 1105.7638ms
[NX Daemon Server] - 2025-07-08T07:35:23.530Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 1194.2915ms
[NX Daemon Server] - 2025-07-08T07:35:23.725Z - Time taken for 'Load Nx Plugin: @nx/playwright/plugin' 1393.2746000000002ms
[NX Daemon Server] - 2025-07-08T07:35:23.782Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-08T07:35:23.782Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-08T07:35:23.782Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-08T07:35:23.803Z - Time taken for 'loadSpecifiedNxPlugins' 1447.9029ms
[NX Daemon Server] - 2025-07-08T07:35:23.804Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-08T07:35:23.805Z - Established a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-08T07:35:23.805Z - Closed a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-08T07:35:23.805Z - Established a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-08T07:35:23.808Z - [REQUEST]: Responding to the client. handleGlob
[NX Daemon Server] - 2025-07-08T07:35:23.809Z - Closed a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-08T07:35:23.809Z - Established a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-08T07:35:23.809Z - Done responding to the client handleGlob
[NX Daemon Server] - 2025-07-08T07:35:23.810Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX Daemon Server] - 2025-07-08T07:35:23.812Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T07:35:23.812Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T07:35:23.813Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-08T07:35:24.354Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T07:35:24.354Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T07:35:24.354Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-08T07:35:24.357Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T07:35:24.357Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T07:35:24.357Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-08T07:35:24.360Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T07:35:24.361Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T07:35:24.361Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-08T07:35:24.363Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T07:35:24.364Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T07:35:24.364Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-08T07:35:24.368Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T07:35:24.368Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T07:35:24.368Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-08T07:35:24.371Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T07:35:24.371Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T07:35:24.371Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-08T07:35:24.412Z - Time taken for 'build-project-configs' 592.4245000000001ms
[NX Daemon Server] - 2025-07-08T07:35:24.475Z - [SYNC]: collect registered sync generators
[NX Daemon Server] - 2025-07-08T07:35:24.476Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-08T07:35:24.478Z - Time taken for 'total for creating and serializing project graph' 2147.0396ms
[NX Daemon Server] - 2025-07-08T07:35:24.480Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-08T07:35:24.480Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2147. Response time: 4.
[NX Daemon Server] - 2025-07-08T07:35:24.495Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-08T07:35:24.495Z - Time taken for 'preTasksExecution' 1.274499999999989ms
[NX Daemon Server] - 2025-07-08T07:35:24.495Z - Done responding to the client handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-08T07:35:24.496Z - Handled PRE_TASKS_EXECUTION. Handling time: 2. Response time: 1.
[NX Daemon Server] - 2025-07-08T07:35:24.596Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-08T07:35:24.596Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-08T07:35:24.596Z - Handled HASH_TASKS. Handling time: 31. Response time: 0.
[NX Daemon Server] - 2025-07-08T07:35:24.623Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-08T07:35:24.623Z - Done responding to the client handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-08T07:35:24.623Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 3. Response time: 1.
[NX Daemon Server] - 2025-07-08T07:35:24.639Z - [REQUEST]: Responding to the client. outputsHashesMatch
[NX Daemon Server] - 2025-07-08T07:35:24.639Z - Done responding to the client outputsHashesMatch
[NX Daemon Server] - 2025-07-08T07:35:24.639Z - Handled OUTPUTS_HASHES_MATCH. Handling time: 4. Response time: 0.
[NX Daemon Server] - 2025-07-08T07:35:24.659Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-08T07:35:24.659Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-08T07:35:24.659Z - Handled RECORD_OUTPUTS_HASH. Handling time: 5. Response time: 0.
[NX Daemon Server] - 2025-07-08T07:35:24.693Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-08T07:35:25.308Z - Established a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-08T07:35:25.317Z - Closed a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-08T07:35:25.318Z - Established a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-08T07:35:25.322Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-08T07:35:25.322Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-08T07:35:25.324Z - Time taken for 'total for creating and serializing project graph' 0.6182000000003427ms
[NX Daemon Server] - 2025-07-08T07:35:25.325Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-08T07:35:25.325Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX Daemon Server] - 2025-07-08T07:35:25.333Z - Established a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-08T07:35:26.027Z - Established a connection. Number of open connections: 6
[NX Daemon Server] - 2025-07-08T07:35:26.027Z - Closed a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-08T07:35:26.028Z - Established a connection. Number of open connections: 6
[NX Daemon Server] - 2025-07-08T07:35:26.037Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-08T07:35:26.037Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-08T07:35:26.039Z - Time taken for 'total for creating and serializing project graph' 0.7683999999999287ms
[NX Daemon Server] - 2025-07-08T07:35:26.042Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-08T07:35:26.042Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 5.
[NX Daemon Server] - 2025-07-08T07:35:26.056Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-08T07:35:26.056Z - Time taken for 'preTasksExecution' 0.35279999999966094ms
[NX Daemon Server] - 2025-07-08T07:35:26.056Z - Done responding to the client handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-08T07:35:26.056Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-08T07:35:26.125Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-08T07:35:26.125Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-08T07:35:26.125Z - Handled HASH_TASKS. Handling time: 16. Response time: 0.
[NX Daemon Server] - 2025-07-08T07:35:26.148Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-08T07:35:26.149Z - Done responding to the client handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-08T07:35:26.149Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 2. Response time: 1.
[NX Daemon Server] - 2025-07-08T07:35:26.165Z - [REQUEST]: Responding to the client. outputsHashesMatch
[NX Daemon Server] - 2025-07-08T07:35:26.165Z - Done responding to the client outputsHashesMatch
[NX Daemon Server] - 2025-07-08T07:35:26.165Z - Handled OUTPUTS_HASHES_MATCH. Handling time: 6. Response time: 0.
[NX Daemon Server] - 2025-07-08T07:35:26.172Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-08T07:35:26.172Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-08T07:35:26.173Z - Handled RECORD_OUTPUTS_HASH. Handling time: 3. Response time: 0.
[NX Daemon Server] - 2025-07-08T07:35:26.179Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX Daemon Server] - 2025-07-08T07:35:26.179Z - Done responding to the client handleRecordTaskRuns
[NX Daemon Server] - 2025-07-08T07:35:26.180Z - Handled RECORD_TASK_RUNS. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-08T07:35:26.183Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX Daemon Server] - 2025-07-08T07:35:26.184Z - Done responding to the client handleGetFlakyTasks
[NX Daemon Server] - 2025-07-08T07:35:26.184Z - Handled GET_FLAKY_TASKS. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-08T07:35:26.190Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX Daemon Server] - 2025-07-08T07:35:26.190Z - Time taken for 'postTasksExecution' 0.5940000000000509ms
[NX Daemon Server] - 2025-07-08T07:35:26.190Z - Done responding to the client handleRunPostTasksExecution
[NX Daemon Server] - 2025-07-08T07:35:26.190Z - Handled POST_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-08T07:35:26.193Z - Closed a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-08T07:36:02.277Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-08T07:36:51.905Z - Closed a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-08T07:36:51.935Z - Closed a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-08T07:36:51.940Z - Closed a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-08T08:34:51.073Z - Established a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-08T08:34:51.089Z - Closed a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-08T08:34:51.091Z - Established a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-08T08:34:51.109Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-08T08:34:51.114Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-08T08:34:51.121Z - Time taken for 'total for creating and serializing project graph' 5.665999999735504ms
[NX Daemon Server] - 2025-07-08T08:34:51.123Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-08T08:34:51.123Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 6. Response time: 9.
[NX Daemon Server] - 2025-07-08T08:34:51.136Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-08T08:34:51.136Z - Time taken for 'preTasksExecution' 2.23119999980554ms
[NX Daemon Server] - 2025-07-08T08:34:51.137Z - Done responding to the client handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-08T08:34:51.137Z - Handled PRE_TASKS_EXECUTION. Handling time: 3. Response time: 1.
[NX Daemon Server] - 2025-07-08T08:34:51.294Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-08T08:34:51.295Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-08T08:34:51.295Z - Handled HASH_TASKS. Handling time: 49. Response time: 1.
[NX Daemon Server] - 2025-07-08T08:34:51.323Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-08T08:34:51.323Z - Done responding to the client handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-08T08:34:51.323Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 2. Response time: 0.
[NX Daemon Server] - 2025-07-08T08:34:51.345Z - [REQUEST]: Responding to the client. outputsHashesMatch
[NX Daemon Server] - 2025-07-08T08:34:51.345Z - Done responding to the client outputsHashesMatch
[NX Daemon Server] - 2025-07-08T08:34:51.345Z - Handled OUTPUTS_HASHES_MATCH. Handling time: 12. Response time: 0.
[NX Daemon Server] - 2025-07-08T08:34:51.352Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-08T08:34:51.352Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-08T08:34:51.352Z - Handled RECORD_OUTPUTS_HASH. Handling time: 4. Response time: 0.
[NX Daemon Server] - 2025-07-08T08:34:51.957Z - Established a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-08T08:34:51.959Z - Closed a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-08T08:34:51.963Z - Established a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-08T08:34:51.968Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-08T08:34:51.970Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-08T08:34:51.971Z - Time taken for 'total for creating and serializing project graph' 1.1049000001512468ms
[NX Daemon Server] - 2025-07-08T08:34:51.973Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-08T08:34:51.973Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 3.
[NX Daemon Server] - 2025-07-08T08:34:51.979Z - Established a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-08T08:34:52.577Z - Established a connection. Number of open connections: 6
[NX Daemon Server] - 2025-07-08T08:34:52.577Z - Closed a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-08T08:34:52.578Z - Established a connection. Number of open connections: 6
[NX Daemon Server] - 2025-07-08T08:34:52.583Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-08T08:34:52.583Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-08T08:34:52.585Z - Time taken for 'total for creating and serializing project graph' 0.4893000000156462ms
[NX Daemon Server] - 2025-07-08T08:34:52.588Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-08T08:34:52.588Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 5.
[NX Daemon Server] - 2025-07-08T08:34:52.606Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-08T08:34:52.607Z - Time taken for 'preTasksExecution' 0.3780999998562038ms
[NX Daemon Server] - 2025-07-08T08:34:52.607Z - Done responding to the client handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-08T08:34:52.607Z - Handled PRE_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-08T08:34:52.680Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-08T08:34:52.681Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-08T08:34:52.681Z - Handled HASH_TASKS. Handling time: 15. Response time: 1.
[NX Daemon Server] - 2025-07-08T08:34:52.701Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-08T08:34:52.701Z - Done responding to the client handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-08T08:34:52.701Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-08T08:34:52.712Z - [REQUEST]: Responding to the client. outputsHashesMatch
[NX Daemon Server] - 2025-07-08T08:34:52.713Z - Done responding to the client outputsHashesMatch
[NX Daemon Server] - 2025-07-08T08:34:52.713Z - Handled OUTPUTS_HASHES_MATCH. Handling time: 3. Response time: 1.
[NX Daemon Server] - 2025-07-08T08:34:52.719Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-08T08:34:52.719Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-08T08:34:52.719Z - Handled RECORD_OUTPUTS_HASH. Handling time: 4. Response time: 0.
[NX Daemon Server] - 2025-07-08T08:34:52.725Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX Daemon Server] - 2025-07-08T08:34:52.726Z - Done responding to the client handleRecordTaskRuns
[NX Daemon Server] - 2025-07-08T08:34:52.726Z - Handled RECORD_TASK_RUNS. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-08T08:34:52.728Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX Daemon Server] - 2025-07-08T08:34:52.728Z - Done responding to the client handleGetFlakyTasks
[NX Daemon Server] - 2025-07-08T08:34:52.728Z - Handled GET_FLAKY_TASKS. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-08T08:34:52.733Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX Daemon Server] - 2025-07-08T08:34:52.734Z - Time taken for 'postTasksExecution' 0.4358999999240041ms
[NX Daemon Server] - 2025-07-08T08:34:52.735Z - Done responding to the client handleRunPostTasksExecution
[NX Daemon Server] - 2025-07-08T08:34:52.735Z - Handled POST_TASKS_EXECUTION. Handling time: 0. Response time: 2.
[NX Daemon Server] - 2025-07-08T08:34:52.742Z - Closed a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-08T08:35:25.576Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-08T08:39:23.824Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-08T08:39:24.097Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-08T08:39:24.267Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-08T08:39:46.503Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-08T08:39:46.507Z - [WATCHER]: package-lock.json was modified
[NX Daemon Server] - 2025-07-08T08:39:46.510Z - Time taken for 'changed-projects' 0.6598000000230968ms
[NX Daemon Server] - 2025-07-08T08:39:46.611Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-08T08:39:46.611Z - [REQUEST]: package-lock.json
[NX Daemon Server] - 2025-07-08T08:39:46.611Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-08T08:39:46.623Z - Time taken for 'hash changed files from watcher' 1.071500000078231ms
[NX Daemon Server] - 2025-07-08T08:39:46.861Z - [REQUEST]: Responding to the client. handleGlob
[NX Daemon Server] - 2025-07-08T08:39:46.861Z - Done responding to the client handleGlob
[NX Daemon Server] - 2025-07-08T08:39:46.862Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-08T08:39:46.869Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T08:39:46.874Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T08:39:46.874Z - Handled HASH_GLOB. Handling time: 2. Response time: 5.
[NX Daemon Server] - 2025-07-08T08:39:46.876Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T08:39:46.877Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T08:39:46.877Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-08T08:39:46.879Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T08:39:46.879Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T08:39:46.880Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-08T08:39:46.882Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T08:39:46.882Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T08:39:46.882Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-08T08:39:46.884Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T08:39:46.885Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T08:39:46.885Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-08T08:39:46.887Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T08:39:46.888Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T08:39:46.888Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-08T08:39:46.890Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T08:39:46.890Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T08:39:46.890Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-08T08:39:46.909Z - Time taken for 'build-project-configs' 284.6735000000335ms
[NX Daemon Server] - 2025-07-08T08:39:46.966Z - [SYNC]: collect registered sync generators
[NX Daemon Server] - 2025-07-08T08:39:46.968Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX Daemon Server] - 2025-07-08T08:39:46.968Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX Daemon Server] - 2025-07-08T08:39:46.969Z - Time taken for 'total execution time for createProjectGraph()' 50.41459999978542ms
[NX Daemon Server] - 2025-07-08T08:40:02.339Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-08T08:54:35.995Z - [WATCHER]: package-lock.json was modified
[NX Daemon Server] - 2025-07-08T08:54:35.995Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-08T08:54:35.996Z - Time taken for 'changed-projects' 0.25320000015199184ms
[NX Daemon Server] - 2025-07-08T08:54:36.196Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-08T08:54:36.196Z - [REQUEST]: package-lock.json
[NX Daemon Server] - 2025-07-08T08:54:36.196Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-08T08:54:36.207Z - [REQUEST]: Responding to the client. handleGlob
[NX Daemon Server] - 2025-07-08T08:54:36.207Z - Time taken for 'hash changed files from watcher' 0.6233999999240041ms
[NX Daemon Server] - 2025-07-08T08:54:36.208Z - Done responding to the client handleGlob
[NX Daemon Server] - 2025-07-08T08:54:36.208Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-08T08:54:36.211Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T08:54:36.212Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T08:54:36.212Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-08T08:54:36.214Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T08:54:36.215Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T08:54:36.215Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-08T08:54:36.229Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T08:54:36.230Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T08:54:36.230Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-08T08:54:36.232Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T08:54:36.237Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T08:54:36.237Z - Handled HASH_GLOB. Handling time: 0. Response time: 5.
[NX Daemon Server] - 2025-07-08T08:54:36.239Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T08:54:36.239Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T08:54:36.239Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-08T08:54:36.241Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T08:54:36.242Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T08:54:36.242Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-08T08:54:36.243Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T08:54:36.244Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T08:54:36.244Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-08T08:54:36.257Z - Time taken for 'build-project-configs' 51.00249999947846ms
[NX Daemon Server] - 2025-07-08T08:54:36.307Z - [SYNC]: collect registered sync generators
[NX Daemon Server] - 2025-07-08T08:54:36.308Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX Daemon Server] - 2025-07-08T08:54:36.308Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX Daemon Server] - 2025-07-08T08:54:36.308Z - Time taken for 'total execution time for createProjectGraph()' 43.0663999998942ms
[NX Daemon Server] - 2025-07-08T08:54:47.982Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-08T09:06:35.842Z - [WATCHER]: run-app.sh was modified
[NX Daemon Server] - 2025-07-08T09:06:35.842Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-08T09:06:35.843Z - Time taken for 'changed-projects' 0.07500000018626451ms
[NX Daemon Server] - 2025-07-08T09:06:36.157Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-08T09:06:36.158Z - [WATCHER]: run-app.sh was modified
[NX Daemon Server] - 2025-07-08T09:06:36.159Z - Time taken for 'changed-projects' 0.07110000029206276ms
[NX Daemon Server] - 2025-07-08T09:06:36.244Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-08T09:06:36.244Z - [REQUEST]: run-app.sh
[NX Daemon Server] - 2025-07-08T09:06:36.244Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-08T09:06:36.283Z - [REQUEST]: Responding to the client. handleGlob
[NX Daemon Server] - 2025-07-08T09:06:36.287Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T09:06:36.288Z - Time taken for 'hash changed files from watcher' 0.8660000003874302ms
[NX Daemon Server] - 2025-07-08T09:06:36.288Z - Done responding to the client handleGlob
[NX Daemon Server] - 2025-07-08T09:06:36.289Z - Handled GLOB. Handling time: 4. Response time: 6.
[NX Daemon Server] - 2025-07-08T09:06:36.292Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T09:06:36.293Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T09:06:36.293Z - Handled HASH_GLOB. Handling time: 1. Response time: 6.
[NX Daemon Server] - 2025-07-08T09:06:36.295Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T09:06:36.295Z - Handled HASH_GLOB. Handling time: 2. Response time: 3.
[NX Daemon Server] - 2025-07-08T09:06:36.298Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T09:06:36.298Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T09:06:36.298Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-08T09:06:36.300Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T09:06:36.301Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T09:06:36.301Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-08T09:06:36.303Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T09:06:36.309Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T09:06:36.309Z - Handled HASH_GLOB. Handling time: 1. Response time: 6.
[NX Daemon Server] - 2025-07-08T09:06:36.311Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T09:06:36.311Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T09:06:36.311Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-08T09:06:36.361Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T09:06:36.361Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T09:06:36.361Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-08T09:06:39.128Z - Time taken for 'build-project-configs' 2872.313599999994ms
[NX Daemon Server] - 2025-07-08T09:06:39.190Z - [SYNC]: collect registered sync generators
[NX Daemon Server] - 2025-07-08T09:06:39.191Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX Daemon Server] - 2025-07-08T09:06:39.191Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX Daemon Server] - 2025-07-08T09:06:39.191Z - Time taken for 'total execution time for createProjectGraph()' 39.022599999792874ms
[NX Daemon Server] - 2025-07-08T09:12:21.824Z - Closed a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-08T09:12:21.866Z - Closed a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-08T09:12:21.892Z - Closed a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-08T09:12:38.843Z - Established a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-08T09:12:38.843Z - Closed a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-08T09:12:38.846Z - Established a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-08T09:12:38.850Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-08T09:12:38.851Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-08T09:12:38.852Z - Time taken for 'total for creating and serializing project graph' 0.5770999994128942ms
[NX Daemon Server] - 2025-07-08T09:12:38.857Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-08T09:12:38.857Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 6.
[NX Daemon Server] - 2025-07-08T09:12:38.876Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-08T09:12:38.876Z - Time taken for 'preTasksExecution' 0.46109999995678663ms
[NX Daemon Server] - 2025-07-08T09:12:38.876Z - Done responding to the client handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-08T09:12:38.876Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-08T09:12:38.982Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-08T09:12:38.983Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-08T09:12:38.983Z - Handled HASH_TASKS. Handling time: 35. Response time: 1.
[NX Daemon Server] - 2025-07-08T09:12:39.020Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-08T09:12:39.021Z - Done responding to the client handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-08T09:12:39.021Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-08T09:12:39.050Z - [REQUEST]: Responding to the client. outputsHashesMatch
[NX Daemon Server] - 2025-07-08T09:12:39.051Z - Done responding to the client outputsHashesMatch
[NX Daemon Server] - 2025-07-08T09:12:39.051Z - Handled OUTPUTS_HASHES_MATCH. Handling time: 4. Response time: 1.
[NX Daemon Server] - 2025-07-08T09:12:39.059Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-08T09:12:39.059Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-08T09:12:39.059Z - Handled RECORD_OUTPUTS_HASH. Handling time: 4. Response time: 0.
[NX Daemon Server] - 2025-07-08T09:12:40.135Z - Established a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-08T09:12:40.143Z - Closed a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-08T09:12:40.144Z - Established a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-08T09:12:40.148Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-08T09:12:40.148Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-08T09:12:40.150Z - Time taken for 'total for creating and serializing project graph' 0.758899999782443ms
[NX Daemon Server] - 2025-07-08T09:12:40.152Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-08T09:12:40.152Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 4.
[NX Daemon Server] - 2025-07-08T09:12:40.161Z - Established a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-08T09:12:40.857Z - Established a connection. Number of open connections: 6
[NX Daemon Server] - 2025-07-08T09:12:40.858Z - Closed a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-08T09:12:40.858Z - Established a connection. Number of open connections: 6
[NX Daemon Server] - 2025-07-08T09:12:40.866Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-08T09:12:40.867Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-08T09:12:40.868Z - Time taken for 'total for creating and serializing project graph' 0.548200000077486ms
[NX Daemon Server] - 2025-07-08T09:12:40.873Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-08T09:12:40.874Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 7.
[NX Daemon Server] - 2025-07-08T09:12:40.884Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-08T09:12:40.884Z - Time taken for 'preTasksExecution' 0.2812999999150634ms
[NX Daemon Server] - 2025-07-08T09:12:40.884Z - Done responding to the client handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-08T09:12:40.884Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-08T09:12:40.958Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-08T09:12:40.958Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-08T09:12:40.958Z - Handled HASH_TASKS. Handling time: 16. Response time: 0.
[NX Daemon Server] - 2025-07-08T09:12:40.983Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-08T09:12:40.983Z - Done responding to the client handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-08T09:12:40.983Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 0. Response time: 0.
[NX Daemon Server] - 2025-07-08T09:12:41.005Z - [REQUEST]: Responding to the client. outputsHashesMatch
[NX Daemon Server] - 2025-07-08T09:12:41.005Z - Done responding to the client outputsHashesMatch
[NX Daemon Server] - 2025-07-08T09:12:41.005Z - Handled OUTPUTS_HASHES_MATCH. Handling time: 6. Response time: 0.
[NX Daemon Server] - 2025-07-08T09:12:41.010Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-08T09:12:41.011Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-08T09:12:41.011Z - Handled RECORD_OUTPUTS_HASH. Handling time: 3. Response time: 1.
[NX Daemon Server] - 2025-07-08T09:12:41.023Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX Daemon Server] - 2025-07-08T09:12:41.024Z - Done responding to the client handleRecordTaskRuns
[NX Daemon Server] - 2025-07-08T09:12:41.024Z - Handled RECORD_TASK_RUNS. Handling time: 7. Response time: 1.
[NX Daemon Server] - 2025-07-08T09:12:41.025Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX Daemon Server] - 2025-07-08T09:12:41.025Z - Done responding to the client handleGetFlakyTasks
[NX Daemon Server] - 2025-07-08T09:12:41.025Z - Handled GET_FLAKY_TASKS. Handling time: 0. Response time: 0.
[NX Daemon Server] - 2025-07-08T09:12:41.030Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX Daemon Server] - 2025-07-08T09:12:41.030Z - Time taken for 'postTasksExecution' 0.41839999984949827ms
[NX Daemon Server] - 2025-07-08T09:12:41.031Z - Done responding to the client handleRunPostTasksExecution
[NX Daemon Server] - 2025-07-08T09:12:41.031Z - Handled POST_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-08T09:12:41.034Z - Closed a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-08T09:13:20.922Z - [WATCHER]: package-lock.json was modified
[NX Daemon Server] - 2025-07-08T09:13:20.980Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-08T09:13:20.980Z - Time taken for 'changed-projects' 56.77419999986887ms
[NX Daemon Server] - 2025-07-08T09:13:21.025Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-08T09:13:21.025Z - [REQUEST]: package-lock.json
[NX Daemon Server] - 2025-07-08T09:13:21.025Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-08T09:13:21.035Z - [REQUEST]: Responding to the client. handleGlob
[NX Daemon Server] - 2025-07-08T09:13:21.035Z - Time taken for 'hash changed files from watcher' 0.7510000001639128ms
[NX Daemon Server] - 2025-07-08T09:13:21.039Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T09:13:21.039Z - Done responding to the client handleGlob
[NX Daemon Server] - 2025-07-08T09:13:21.039Z - Handled GLOB. Handling time: 1. Response time: 4.
[NX Daemon Server] - 2025-07-08T09:13:21.041Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T09:13:21.041Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T09:13:21.041Z - Handled HASH_GLOB. Handling time: 1. Response time: 2.
[NX Daemon Server] - 2025-07-08T09:13:21.042Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T09:13:21.042Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-08T09:13:21.043Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T09:13:21.044Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T09:13:21.044Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-08T09:13:21.046Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T09:13:21.046Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T09:13:21.046Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-08T09:13:21.048Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T09:13:21.048Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T09:13:21.049Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-08T09:13:21.050Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T09:13:21.051Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T09:13:21.051Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-08T09:13:21.053Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-08T09:13:21.058Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-08T09:13:21.058Z - Handled HASH_GLOB. Handling time: 1. Response time: 5.
[NX Daemon Server] - 2025-07-08T09:13:21.069Z - Time taken for 'build-project-configs' 35.268899999558926ms
[NX Daemon Server] - 2025-07-08T09:13:21.123Z - [SYNC]: collect registered sync generators
[NX Daemon Server] - 2025-07-08T09:13:21.124Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX Daemon Server] - 2025-07-08T09:13:21.124Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX Daemon Server] - 2025-07-08T09:13:21.124Z - Time taken for 'total execution time for createProjectGraph()' 44.59470000024885ms
[NX Daemon Server] - 2025-07-08T09:13:30.813Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-08T11:54:43.192Z - Closed a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-08T11:54:43.207Z - Closed a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-08T11:54:43.241Z - Closed a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-09T06:44:16.082Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\df8e34c1cf49d3f47b5e\d.sock
[NX Daemon Server] - 2025-07-09T06:44:16.091Z - [WATCHER]: Subscribed to changes within: D:\project\experiments (native)
[NX Daemon Server] - 2025-07-09T06:44:16.094Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-09T06:44:16.096Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-09T06:44:16.098Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-09T06:44:16.101Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-09T06:44:17.059Z - Time taken for 'Load Nx Plugin: D:\project\experiments\node_modules\nx\src\plugins\package-json' 948.1090999999999ms
[NX Daemon Server] - 2025-07-09T06:44:17.122Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 1016.9051ms
[NX Daemon Server] - 2025-07-09T06:44:17.129Z - Time taken for 'Load Nx Plugin: D:\project\experiments\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 1018.1513ms
[NX Daemon Server] - 2025-07-09T06:44:17.159Z - Time taken for 'loadDefaultNxPlugins' 1049.7223ms
[NX Daemon Server] - 2025-07-09T06:44:17.403Z - Time taken for 'Load Nx Plugin: @nx/playwright/plugin' 1301.0592ms
[NX Daemon Server] - 2025-07-09T06:44:17.413Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-09T06:44:17.414Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-09T06:44:17.414Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-09T06:44:17.443Z - Time taken for 'loadSpecifiedNxPlugins' 1310.0843ms
[NX Daemon Server] - 2025-07-09T06:44:17.444Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-09T06:44:17.445Z - Established a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-09T06:44:17.445Z - Established a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-09T06:44:17.445Z - Established a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-09T06:44:17.446Z - Closed a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-09T06:44:17.446Z - Closed a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-09T06:44:17.450Z - [REQUEST]: Responding to the client. handleGlob
[NX Daemon Server] - 2025-07-09T06:44:17.463Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-09T06:44:17.466Z - Done responding to the client handleGlob
[NX Daemon Server] - 2025-07-09T06:44:17.466Z - Handled GLOB. Handling time: 3. Response time: 16.
[NX Daemon Server] - 2025-07-09T06:44:17.472Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-09T06:44:17.472Z - Handled HASH_GLOB. Handling time: 11. Response time: 9.
[NX Daemon Server] - 2025-07-09T06:44:17.994Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-09T06:44:17.994Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-09T06:44:17.994Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-09T06:44:17.997Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-09T06:44:17.997Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-09T06:44:17.997Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-09T06:44:17.999Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-09T06:44:18.000Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-09T06:44:18.000Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-09T06:44:18.003Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-09T06:44:18.003Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-09T06:44:18.003Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-09T06:44:18.005Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-09T06:44:18.005Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-09T06:44:18.005Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-09T06:44:18.007Z - [REQUEST]: Responding to the client. handleHashGlob
[NX Daemon Server] - 2025-07-09T06:44:18.007Z - Done responding to the client handleHashGlob
[NX Daemon Server] - 2025-07-09T06:44:18.008Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-09T06:44:18.050Z - Time taken for 'build-project-configs' 590.9018999999998ms
[NX Daemon Server] - 2025-07-09T06:44:18.109Z - [SYNC]: collect registered sync generators
[NX Daemon Server] - 2025-07-09T06:44:18.111Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-09T06:44:18.112Z - Time taken for 'total for creating and serializing project graph' 2010.3521ms
[NX Daemon Server] - 2025-07-09T06:44:18.115Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-09T06:44:18.115Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2011. Response time: 4.
[NX Daemon Server] - 2025-07-09T06:44:18.125Z - Closed a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-09T06:44:35.938Z - Established a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-09T06:44:35.938Z - Closed a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-09T06:44:35.940Z - Established a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-09T06:44:35.948Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-09T06:44:35.949Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-09T06:44:35.950Z - Time taken for 'total for creating and serializing project graph' 0.5747000000010303ms
[NX Daemon Server] - 2025-07-09T06:44:35.952Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-09T06:44:35.953Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX Daemon Server] - 2025-07-09T06:44:35.961Z - Closed a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-09T06:53:00.520Z - Established a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-09T06:53:00.520Z - Closed a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-09T06:53:00.521Z - Established a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-09T06:53:00.531Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-09T06:53:00.532Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-09T06:53:00.533Z - Time taken for 'total for creating and serializing project graph' 0.6317000000271946ms
[NX Daemon Server] - 2025-07-09T06:53:00.536Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-09T06:53:00.536Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX Daemon Server] - 2025-07-09T06:53:00.547Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-09T06:53:00.547Z - Time taken for 'preTasksExecution' 1.1803000000072643ms
[NX Daemon Server] - 2025-07-09T06:53:00.547Z - Done responding to the client handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-09T06:53:00.547Z - Handled PRE_TASKS_EXECUTION. Handling time: 2. Response time: 0.
[NX Daemon Server] - 2025-07-09T06:53:00.666Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-09T06:53:00.667Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-09T06:53:00.667Z - Handled HASH_TASKS. Handling time: 43. Response time: 1.
[NX Daemon Server] - 2025-07-09T06:53:00.698Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-09T06:53:00.698Z - Done responding to the client handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-09T06:53:00.698Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 3. Response time: 0.
[NX Daemon Server] - 2025-07-09T06:53:00.715Z - [REQUEST]: Responding to the client. outputsHashesMatch
[NX Daemon Server] - 2025-07-09T06:53:00.715Z - Done responding to the client outputsHashesMatch
[NX Daemon Server] - 2025-07-09T06:53:00.715Z - Handled OUTPUTS_HASHES_MATCH. Handling time: 8. Response time: 0.
[NX Daemon Server] - 2025-07-09T06:53:00.745Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-09T06:53:00.745Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-09T06:53:00.745Z - Handled RECORD_OUTPUTS_HASH. Handling time: 10. Response time: 0.
[NX Daemon Server] - 2025-07-09T06:53:00.771Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-09T06:53:02.015Z - Established a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-09T06:53:02.015Z - Closed a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-09T06:53:02.016Z - Established a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-09T06:53:02.022Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-09T06:53:02.022Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-09T06:53:02.024Z - Time taken for 'total for creating and serializing project graph' 0.5354999999981374ms
[NX Daemon Server] - 2025-07-09T06:53:02.026Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-09T06:53:02.026Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 4.
[NX Daemon Server] - 2025-07-09T06:53:02.033Z - Established a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-09T06:53:02.654Z - Established a connection. Number of open connections: 6
[NX Daemon Server] - 2025-07-09T06:53:02.655Z - Closed a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-09T06:53:02.656Z - Established a connection. Number of open connections: 6
[NX Daemon Server] - 2025-07-09T06:53:02.664Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-09T06:53:02.664Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-09T06:53:02.666Z - Time taken for 'total for creating and serializing project graph' 0.6080000000074506ms
[NX Daemon Server] - 2025-07-09T06:53:02.668Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-09T06:53:02.668Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 4.
[NX Daemon Server] - 2025-07-09T06:53:02.681Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-09T06:53:02.682Z - Time taken for 'preTasksExecution' 0.47510000003967434ms
[NX Daemon Server] - 2025-07-09T06:53:02.682Z - Done responding to the client handleRunPreTasksExecution
[NX Daemon Server] - 2025-07-09T06:53:02.682Z - Handled PRE_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-09T06:53:02.742Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-09T06:53:02.743Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-09T06:53:02.743Z - Handled HASH_TASKS. Handling time: 13. Response time: 1.
[NX Daemon Server] - 2025-07-09T06:53:02.761Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-09T06:53:02.762Z - Done responding to the client handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-07-09T06:53:02.762Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-09T06:53:02.772Z - [REQUEST]: Responding to the client. outputsHashesMatch
[NX Daemon Server] - 2025-07-09T06:53:02.773Z - Done responding to the client outputsHashesMatch
[NX Daemon Server] - 2025-07-09T06:53:02.773Z - Handled OUTPUTS_HASHES_MATCH. Handling time: 2. Response time: 1.
[NX Daemon Server] - 2025-07-09T06:53:02.780Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-09T06:53:02.781Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-09T06:53:02.781Z - Handled RECORD_OUTPUTS_HASH. Handling time: 5. Response time: 1.
[NX Daemon Server] - 2025-07-09T06:53:02.787Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX Daemon Server] - 2025-07-09T06:53:02.788Z - Done responding to the client handleRecordTaskRuns
[NX Daemon Server] - 2025-07-09T06:53:02.788Z - Handled RECORD_TASK_RUNS. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-09T06:53:02.790Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX Daemon Server] - 2025-07-09T06:53:02.790Z - Done responding to the client handleGetFlakyTasks
[NX Daemon Server] - 2025-07-09T06:53:02.790Z - Handled GET_FLAKY_TASKS. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-09T06:53:02.797Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX Daemon Server] - 2025-07-09T06:53:02.798Z - Time taken for 'postTasksExecution' 0.7243000000016764ms
[NX Daemon Server] - 2025-07-09T06:53:02.798Z - Done responding to the client handleRunPostTasksExecution
[NX Daemon Server] - 2025-07-09T06:53:02.798Z - Handled POST_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-09T06:53:02.802Z - Closed a connection. Number of open connections: 5
[NX Daemon Server] - 2025-07-09T09:53:02.834Z - There are open file watchers. Resetting inactivity timer.
[NX Daemon Server] - 2025-07-09T11:42:59.806Z - Closed a connection. Number of open connections: 4
[NX Daemon Server] - 2025-07-09T11:42:59.863Z - Closed a connection. Number of open connections: 3
[NX Daemon Server] - 2025-07-09T11:42:59.888Z - Closed a connection. Number of open connections: 2
