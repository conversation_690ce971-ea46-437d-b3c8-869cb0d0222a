# PostgreSQL configuration file
# Basic Settings
listen_addresses = '*'                  # Listen on all interfaces within container
max_connections = 100                   # Maximum number of connections
shared_buffers = 256MB                  # Memory for caching and shared operations
work_mem = 8MB                          # Memory for query operations
maintenance_work_mem = 64MB             # Memory for maintenance operations

# Security Settings
password_encryption = 'scram-sha-256'   # Strong password hashing
ssl = off                               # Disable SSL for now to simplify setup
ssl_prefer_server_ciphers = on          # Prefer server's cipher preference
log_connections = on                    # Log all connections
log_disconnections = on                 # Log all disconnections
log_statement = 'none'                  # Don't log statements by default
log_min_duration_statement = 1000       # Log slow queries (over 1s)

# Prevent certain dangerous operations
allow_system_table_mods = off           # Prevent system table modifications

# Additional Security Hardening
authentication_timeout = 1min           # Limit authentication attempts
password_min_length = 12                # Enforce strong passwords
password_min_uppercase = 1              # Require at least one uppercase letter
password_min_lowercase = 1              # Require at least one lowercase letter
password_min_special = 1                # Require at least one special character
password_min_digits = 1                 # Require at least one digit
