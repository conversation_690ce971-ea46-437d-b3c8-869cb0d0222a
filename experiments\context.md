# Project Context: Psychology Chat Application

## Project Overview

This project is an NX monorepo containing a NestJS backend application for a Psychology Chat service. The application provides authentication functionality and chat storage capabilities as specified in the API specification document.

## Repository Structure

### Apps

- **psychology-chat**: Main NestJS application that serves as the backend API
  - Uses Swagger for API documentation
  - Implements CORS
  - Exposes API endpoints with a global prefix of 'api'

### Libraries

- **be-auth**: Authentication library
  - Handles user registration and login
  - Implements JWT authentication
  - Uses PostgreSQL database via Knex.js
  - Contains migrations for user table

- **be-database**: Database configuration library
  - Contains shared database configurations
  - Provides Knex.js configuration with enhanced security
  - Manages environment variables for database connections
  - Includes security enhancements based on previous security issues

- **be-chat**: Chat storage library
  - Implements the Chat Storage API as specified in the API specification
  - Provides CRUD operations for chat data
  - Manages chat messages, reasoning steps, and source documents
  - Uses JWT authentication for securing endpoints

## Components

### Modules

- **AppModule**: Main application module that imports other modules
  - Imports BeAuthModule for authentication
  - Imports BeChatModule for chat storage
  - Imports ConfigModule for environment configuration

- **BeAuthModule**: Authentication module
  - Registers JWT module
  - Provides authentication services and controllers
  - Handles database migrations on module initialization

- **BeChatModule**: Chat storage module
  - Provides chat services and controllers
  - Handles database migrations for chat-related tables
  - Implements the Chat Storage API endpoints

### Controllers

- **AuthController**: Handles authentication endpoints
  - POST /auth/register: Registers a new user
  - POST /auth/login: Authenticates a user and returns a JWT token
  - Uses Swagger decorators for API documentation

- **ChatController**: Handles chat storage endpoints
  - GET /chats: Gets all chats for the authenticated user
  - GET /chats/{chatId}: Gets a specific chat by ID
  - POST /chats: Creates a new chat
  - PUT /chats/{chatId}: Updates an existing chat
  - DELETE /chats/{chatId}: Deletes a chat
  - POST /chats/{chatId}/messages: Adds a message to a chat

### Services

- **AuthService**: Implements authentication logic
  - register(): Creates a new user with a hashed password
  - login(): Authenticates a user and generates a JWT token

- **ChatService**: Implements chat storage logic
  - getAllChats(): Gets all chats for a user
  - getChatById(): Gets a specific chat by ID
  - createChat(): Creates a new chat
  - updateChat(): Updates an existing chat
  - deleteChat(): Deletes a chat
  - addMessage(): Adds a message to a chat

### Repositories

- **UserRepository**: Handles database operations for users
  - createUser(): Creates a new user in the database
  - findByEmail(): Finds a user by email

- **ChatRepository**: Handles database operations for chats
  - findAllChats(): Gets all chats for a user
  - findChatById(): Gets a specific chat by ID
  - createChat(): Creates a new chat
  - updateChat(): Updates an existing chat
  - deleteChat(): Deletes a chat
  - addMessage(): Adds a message to a chat

### Models

- **User**: Represents a user in the system
  - id: Unique identifier
  - email: User's email address
  - password: Hashed password

- **Chat**: Represents a chat in the system
  - id: Unique identifier
  - title: Display title for the chat
  - messages: Array of messages in the chat
  - createdAt: Creation timestamp
  - updatedAt: Last update timestamp
  - flowChatId: FlowWise chatId for session continuity
  - sessionId: FlowWise sessionId for PostgreSQL agent memory
  - reasoningSteps: AI reasoning steps
  - sourceDocuments: Reference documents used by AI
  - userId: ID of the user who owns the chat

- **Message**: Represents a message in a chat
  - id: Unique identifier
  - role: Who sent the message (user or assistant)
  - content: Text content of the message
  - timestamp: When the message was sent
  - isComplete: Whether the message is complete
  - thinking: Optional thinking/reasoning text

- **AgentReasoningStep**: Represents an AI agent's reasoning step
  - agentName: Name of the AI agent
  - messages: Messages processed by the agent
  - next: Next step information
  - instructions: Instructions for the agent
  - usedTools: Tools used by the agent
  - sourceDocuments: Source documents referenced
  - artifacts: Any artifacts produced
  - nodeId: Node ID in the flow
  - thought: Agent's thought process
  - action: Action taken by the agent
  - observation: Agent's observation

- **SourceDocument**: Represents a reference document used by AI
  - id: Document identifier
  - pageContent: Content of the document
  - metadata: Metadata about the document

### DTOs

- **RegisterDto**: Data Transfer Object for user registration
  - email: User's email address
  - password: User's password

- **LoginDto**: Data Transfer Object for user login
  - email: User's email address
  - password: User's password

- **CreateChatDto**: Data Transfer Object for creating a chat
  - title: Display title for the chat
  - messages: Array of messages in the chat
  - flowChatId: FlowWise chatId for session continuity
  - sessionId: FlowWise sessionId for PostgreSQL agent memory

- **UpdateChatDto**: Data Transfer Object for updating a chat
  - title: Display title for the chat
  - messages: Array of messages in the chat
  - flowChatId: FlowWise chatId for session continuity
  - sessionId: FlowWise sessionId for PostgreSQL agent memory
  - reasoningSteps: AI reasoning steps
  - sourceDocuments: Reference documents used by AI

- **AddMessageDto**: Data Transfer Object for adding a message to a chat
  - role: Who sent the message (user or assistant)
  - content: Text content of the message
  - timestamp: When the message was sent
  - isComplete: Whether the message is complete
  - thinking: Optional thinking/reasoning text

### Database

- Uses PostgreSQL with Knex.js for database operations
- Migrations for creating the users, chats, messages, reasoning_steps, and source_documents tables
- Environment variables for database configuration
- Enhanced security configurations based on previous security issues

### Security

- JWT authentication for securing endpoints
- Password hashing using bcrypt
- Guards for protecting routes
- Proper error handling and validation
- Enhanced database security configurations
- Security improvements based on previous PostgreSQL security issues:
  - Improved pg_hba.conf and postgresql.conf configurations
  - Proper Docker volume configuration for data persistence
  - Updated authentication with strong password
  - Restricted network access

## API Specification

The API specification document outlines requirements for a Chat Storage API with the following features:

- Authentication using JWT
- CRUD operations for chat data
- Data models for Chat, Message, AgentReasoningStep, and SourceDocument
- Error handling and response formats
- Implementation notes for frontend integration
- Security and data size considerations
- Migration plan from local storage to server-side storage

## Implementation Status

- Authentication functionality is fully implemented
- Chat Storage API is fully implemented
- Database migrations are created for all required tables
- Security enhancements have been implemented
- Type issues and dependency constraints have been fixed
