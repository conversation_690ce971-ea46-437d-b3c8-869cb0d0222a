# Psychology Chat Application

A modern NestJS-based chat application with PostgreSQL database for psychology-related conversations.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Tech Stack](#tech-stack)
- [Project Structure](#project-structure)
- [Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Database Setup](#database-setup)
  - [Running the Application](#running-the-application)
  - [API Documentation](#api-documentation)
- [Development](#development)
  - [Available Scripts](#available-scripts)
  - [Adding New Components](#adding-new-components)
- [Docker Deployment](#docker-deployment)
- [Troubleshooting](#troubleshooting)
- [Contributing](#contributing)

## Overview

Psychology Chat is a specialized chat application designed to facilitate psychology-related conversations. It provides a secure platform with user authentication, real-time messaging, and data persistence.

## Features

- User registration and authentication with JWT
- RESTful API with Swagger documentation
- PostgreSQL database for data persistence
- Docker containerization for easy deployment
- NestJS backend with modular architecture

## Tech Stack

- **Backend**: NestJS, TypeScript
- **Database**: PostgreSQL
- **Authentication**: JWT, Passport
- **API Documentation**: Swagger/OpenAPI
- **Containerization**: Docker, Docker Compose
- **Validation**: Zod
- **Build Tools**: Nx, Webpack

## Project Structure

This project is organized as an Nx monorepo with the following structure:

```
experiments/
├── apps/
│   └── psychology-chat/       # NestJS backend application
├── libs/
│   └── be-auth/               # Authentication library
├── docker/
│   └── db/                    # Database setup and management
│       ├── docker-compose.yml # Database-specific docker-compose
│       ├── init.sql           # Database initialization script
│       └── manage-db.sh       # Database management script
├── docker-compose.yml         # Main application docker-compose
└── start-app.sh               # Helper script to start the entire application
```

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- npm or yarn
- Docker and Docker Compose (V1 or V2)
- Git

### Database Setup

The database is managed separately from the application to prevent accidental data loss. To start the database:

```sh
cd docker/db
./manage-db.sh start
```

The management script provides several commands:
- `start` - Start the database
- `stop` - Stop the database (preserves data)
- `restart` - Restart the database
- `status` - Check database status
- `reset` - Reset the database (WARNING: Deletes all data)
- `connect` - Connect to the database with psql

### Running the Application

You can run the entire application (database and backend) using the provided helper script:

```sh
./start-app.sh start
```

The script provides several commands:
- `start` - Start the entire application
- `stop` - Stop the entire application
- `restart` - Restart the entire application
- `status` - Check application status

#### Development Mode

If you prefer to run the components separately:

```sh
# Install dependencies
npm install

# Start the backend service
npm run dev-be-psy-chat
```

#### Using Docker

```sh
# Make sure the database is running first
docker compose up -d  # For Docker Compose V2
# OR
docker-compose up -d  # For Docker Compose V1
```

### API Documentation

The API documentation is available via Swagger UI at:

```
http://localhost:3003/docs
```

## Development

### Available Scripts

```sh
# Start the backend in development mode
npm run dev-be-psy-chat

# Build the backend for production
npm run build-be-psy-chat
```

### Adding New Components

This project uses Nx for managing the monorepo. To generate new components:

```sh
# Generate a new NestJS controller
nx g @nx/nest:controller new-controller --project=psychology-chat

# Generate a new NestJS service
nx g @nx/nest:service new-service --project=psychology-chat
```

## Docker Deployment

The application is containerized using Docker for easy deployment:

1. Use the helper script to start everything:
   ```sh
   ./start-app.sh start
   ```

Or start components individually:

1. Start the database first:
   ```sh
   cd docker/db
   ./manage-db.sh start
   ```

2. Then start the application:
   ```sh
   cd ../../
   docker compose up -d  # For Docker Compose V2
   # OR
   docker-compose up -d  # For Docker Compose V1
   ```

The application will be available at `http://localhost:3003/api` with Swagger documentation at `http://localhost:3003/docs`.

## Troubleshooting

### Docker Compose Command Not Found

If you encounter an error like `docker-compose: command not found`, your system is using Docker Compose V2. The scripts should automatically detect this and use the correct command. If you're running Docker commands manually, use:

```sh
docker compose up -d  # With a space instead of a hyphen
```

### Database Connection Issues

If the application can't connect to the database:

1. Check if the database container is running:
   ```sh
   docker ps | grep postgres_db
   ```

2. Verify the database exists:
   ```sh
   docker exec postgres_db psql -U postgres -c "SELECT datname FROM pg_database WHERE datname = 'psy_chat';"
   ```

3. Make sure the application is configured to connect to the right host:
   - When running locally: `localhost`
   - When running in Docker: `db` (the service name in docker-compose)

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature-name`
3. Commit your changes: `git commit -m 'Add some feature'`
4. Push to the branch: `git push origin feature/your-feature-name`
5. Submit a pull request
