import { Injectable } from '@nestjs/common';
import { UserRepository } from '../repository/be-user.repository';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { RegisterDto, LoginDto } from '../dtos/be-auth.dto';
import { User } from '../models/be-user.model';

@Injectable()
export class AuthService {
  constructor(private userRepo: UserRepository, private jwtService: JwtService) {}

  async register(data: RegisterDto): Promise<User> {
    const hashedPassword = await bcrypt.hash(data.password, 10);
    return this.userRepo.createUser({ ...data, password: hashedPassword });
  }

  async login(data: LoginDto): Promise<{ token: string }> {
    const user = await this.userRepo.findByEmail(data.email);
    if (!user || !(bcrypt.compare(data.password, user.password))) {
      throw new Error('Invalid credentials');
    }
    const payload = { id: user.id, email: user.email };
    return { token: this.jwtService.sign(payload) };
  }
}
