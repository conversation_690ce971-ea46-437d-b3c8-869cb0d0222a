#!/bin/bash

# Simple script to check database status and start the Psychology Chat application

# Determine which docker compose command to use
if command -v docker-compose &> /dev/null; then
  DOCKER_COMPOSE="docker-compose"
elif command -v docker &> /dev/null && docker compose version &> /dev/null; then
  DOCKER_COMPOSE="docker compose"
else
  echo "❌ Error: Neither docker-compose nor docker compose is available"
  echo "Please install Docker Compose to continue."
  exit 1
fi

# Database credentials
DB_USER="postgres"
# DB_PASSWORD="JK1zU0z2U7SiAo6"
DB_PASSWORD="user"
SERVICE_USER="service_user"
SERVICE_PASSWORD="Str0ng_S3rvic3_P@ssw0rd_2025!"
DB_NAMES=("psy_chat" "psy_agent_chat")

# Function to check if docker compose is available
check_docker_compose() {
  if ! $DOCKER_COMPOSE version &> /dev/null; then
    echo "❌ Docker Compose is not available"
    return 1
  fi
  return 0
}

# Function to check if database container is running
check_db_running() {
  if docker ps | grep -q postgres_db; then
    return 0  # Database is running
  else
    return 1  # Database is not running
  fi
}

# Function to wait for database to be ready
wait_for_db() {
  echo "Waiting for database to be ready..."

  # Wait for up to 60 seconds for the database to be ready
  for i in {1..60}; do
    if docker exec postgres_db pg_isready -U "$DB_USER" &> /dev/null; then
      echo "✅ Database is ready"
      sleep 5  # Give it a few more seconds to fully initialize
      return 0
    fi
    echo "Waiting... ($i/60)"
    sleep 1
  done

  echo "❌ Timed out waiting for database to be ready"
  return 1
}

# Function to check if a specific database exists
check_db_exists() {
  local db_name=$1
  # Run the command directly in the container
  if docker exec postgres_db bash -c "PGPASSWORD='$DB_PASSWORD' psql -U $DB_USER -lqt | cut -d \| -f 1 | grep -qw $db_name"; then
    return 0  # Database exists
  else
    return 1  # Database does not exist
  fi
}

# Function to verify service user exists and has correct permissions
verify_service_user() {
  echo "Verifying service user..."

  # Attempt to connect to the database and check user
  local user_check=$(docker exec postgres_db bash -c "PGPASSWORD='$DB_PASSWORD' psql -U $DB_USER -tAc \"
    SELECT 1 FROM pg_roles WHERE rolname = '$SERVICE_USER';
  \"")

  if [[ -z "$user_check" ]]; then
    echo "❌ Service user does not exist"

    # Attempt to recreate the user
    docker exec postgres_db bash -c "PGPASSWORD='$DB_PASSWORD' psql -U $DB_USER -c \"
      CREATE USER $SERVICE_USER WITH
        PASSWORD '$SERVICE_PASSWORD'
        LOGIN
        SUPERUSER
        CREATEDB
        CREATEROLE;
    \""

    # Verify user creation
    user_check=$(docker exec postgres_db bash -c "PGPASSWORD='$DB_PASSWORD' psql -U $DB_USER -tAc \"
      SELECT 1 FROM pg_roles WHERE rolname = '$SERVICE_USER';
    \"")

    if [[ -z "$user_check" ]]; then
      echo "❌ Failed to create service user"
      return 1
    fi
  fi

  # Check user privileges on both databases
  for db_name in "${DB_NAMES[@]}"; do
    # Grant database-level privileges
    docker exec postgres_db bash -c "
      PGPASSWORD='$DB_PASSWORD' psql -U $DB_USER -c \"
        -- Grant database privileges
        GRANT ALL PRIVILEGES ON DATABASE $db_name TO $SERVICE_USER;
      \"

      # Set up schema privileges
      PGPASSWORD='$DB_PASSWORD' psql -U $DB_USER -d $db_name -c \"
        -- Grant schema-level privileges
        GRANT ALL PRIVILEGES ON SCHEMA public TO $SERVICE_USER;
        GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO $SERVICE_USER;
        GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO $SERVICE_USER;
        GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO $SERVICE_USER;
        ALTER USER $SERVICE_USER SET search_path TO public;
      \"
    "
  done

  echo "✅ Service user verified successfully"
  return 0
}

# Function to create database and grant privileges
create_database_and_grant_privileges() {
  local db_name=$1

  # Create the database directly
  docker exec postgres_db bash -c "PGPASSWORD='$DB_PASSWORD' psql -U $DB_USER -c \"CREATE DATABASE $db_name;\""

  # Grant database-level privileges
  docker exec postgres_db bash -c "
    PGPASSWORD='$DB_PASSWORD' psql -U $DB_USER -c \"
      -- Grant database privileges
      GRANT ALL PRIVILEGES ON DATABASE $db_name TO $SERVICE_USER;
    \"

    # Set up schema privileges
    PGPASSWORD='$DB_PASSWORD' psql -U $DB_USER -d $db_name -c \"
      -- Grant schema-level privileges
      GRANT ALL PRIVILEGES ON SCHEMA public TO $SERVICE_USER;
      GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO $SERVICE_USER;
      GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO $SERVICE_USER;
      GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO $SERVICE_USER;
      ALTER USER $SERVICE_USER SET search_path TO public;
    \"
  "
}

# Main script logic
echo "Checking system requirements..."

# Check Docker Compose availability
if ! check_docker_compose; then
  echo "❌ Docker Compose is not installed or not working correctly."
  echo "Please install Docker Compose and ensure it's in your system PATH."
  exit 1
fi

echo "Checking database status..."

# Step 1: Check if database container is running
if check_db_running; then
  echo "✅ Database container is running"
else
  echo "❌ Database container is not running"
  echo "Starting database container..."

  # Remove any existing container to start fresh
  docker rm -f postgres_db &> /dev/null

  # Start the database with initialization script
  cd docker/db
  $DOCKER_COMPOSE up -d
  cd ../..

  # Wait for database to be ready
  if ! wait_for_db; then
    echo "❌ Failed to start database properly"
    exit 1
  fi
fi

# Step 2: Verify all required databases exist
for db_name in "${DB_NAMES[@]}"; do
  if check_db_exists "$db_name"; then
    echo "✅ $db_name database exists"
  else
    echo "❌ $db_name database does not exist"
    echo "Creating $db_name database..."

    create_database_and_grant_privileges "$db_name"

    # Verify database was created
    if check_db_exists "$db_name"; then
      echo "✅ $db_name database created successfully"
    else
      echo "❌ Failed to create $db_name database"
      exit 1
    fi
  fi
done

# Step 3: Verify service user
if ! verify_service_user; then
  echo "❌ Failed to verify service user"
  exit 1
fi

# Start the rest of the application
echo "Starting backend service..."
cd docker/db  # Ensure we're in the right directory for docker-compose
$DOCKER_COMPOSE -f ../../docker-compose.yml up -d be-psychology-chat
cd ../..

echo "Checking application status..."
if docker ps | grep -q be_psychology_chat; then
  echo "✅ Backend service is running"
  echo "API is available at http://localhost:3003/api"
  echo "Swagger documentation is available at http://localhost:3003/docs"
  echo "Psychology Chat application is ready!"
else
  echo "❌ Failed to start backend service"
  exit 1
fi
