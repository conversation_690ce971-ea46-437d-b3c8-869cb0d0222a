import { z } from 'zod';

/**
 * AgentReasoningStep model schema definition using Zod
 * Based on the API specification
 */
export const AgentReasoningStepModel = z.object({
  id: z.string().uuid().optional(),
  stepNumber: z.number().int().positive().optional(),
  content: z.string().optional(),
  agentName: z.string().optional(),
  messages: z.array(z.string()).optional(),
  next: z.string().optional(),
  instructions: z.string().optional(),
  usedTools: z.array(z.unknown()).optional(),
  sourceDocuments: z.array(z.unknown()).optional(),
  artifacts: z.array(z.unknown()).optional(),
  nodeId: z.string().optional(),
  thought: z.string().optional(),
  action: z.string().optional(),
  observation: z.string().optional(),
});

export type AgentReasoningStep = z.infer<typeof AgentReasoningStepModel>;
