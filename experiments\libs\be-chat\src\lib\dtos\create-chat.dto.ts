import { z } from 'zod';
import { createZodDto } from '@anatine/zod-nestjs';
import { MessageModel } from '../models/message.model';

// Define Zod Schema for CreateChatDto
export const CreateChatSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  messages: z.array(MessageModel).default([]),
  flowChatId: z.string().optional(),
  sessionId: z.string().optional(),
});

// Convert Zod Schema into NestJS DTO for Validation
export class CreateChatDto extends createZodDto(CreateChatSchema) {}

// Infer TypeScript Type from Zod Schema
export type CreateChatDtoType = z.infer<typeof CreateChatSchema>;
