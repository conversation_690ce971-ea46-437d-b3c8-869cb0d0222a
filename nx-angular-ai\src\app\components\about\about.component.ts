import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { NavbarComponent } from '../landing/navbar/navbar.component';
import { FooterComponent } from '../landing/footer/footer.component';

@Component({
  selector: 'app-about',
  standalone: true,
  imports: [CommonModule, RouterModule, NavbarComponent, FooterComponent],
  template: `
    <div class="min-h-screen bg-gray-50">
      <app-navbar></app-navbar>
      
      <main class="pt-20">
        <!-- Hero Section -->
        <section class="py-16 bg-gradient-to-br from-blue-600 to-purple-600 text-white">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-6">
              About Psychology Chat
            </h1>
            <p class="text-xl text-blue-100 max-w-3xl mx-auto">
              We're revolutionizing mental health support through AI-powered psychology, 
              making professional guidance accessible to everyone, everywhere.
            </p>
          </div>
        </section>

        <!-- Mission Section -->
        <section class="py-16 bg-white">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 class="text-3xl font-bold text-gray-900 mb-6">Our Mission</h2>
                <p class="text-lg text-gray-600 mb-6">
                  Mental health support shouldn't be limited by geography, time zones, or financial barriers. 
                  Our mission is to democratize access to psychological guidance through cutting-edge AI technology.
                </p>
                <p class="text-lg text-gray-600 mb-6">
                  We believe everyone deserves immediate, professional-quality mental health support when they need it most. 
                  Our AI-powered platform provides evidence-based psychological interventions available 24/7.
                </p>
                <div class="flex items-center space-x-4">
                  <div class="bg-blue-100 rounded-full p-3">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                  </div>
                  <span class="text-gray-700 font-medium">Compassionate AI for everyone</span>
                </div>
              </div>
              <div class="bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8">
                <div class="grid grid-cols-2 gap-6 text-center">
                  <div>
                    <div class="text-3xl font-bold text-blue-600 mb-2">10K+</div>
                    <div class="text-gray-600">Users Helped</div>
                  </div>
                  <div>
                    <div class="text-3xl font-bold text-purple-600 mb-2">24/7</div>
                    <div class="text-gray-600">Availability</div>
                  </div>
                  <div>
                    <div class="text-3xl font-bold text-green-600 mb-2">95%</div>
                    <div class="text-gray-600">Satisfaction</div>
                  </div>
                  <div>
                    <div class="text-3xl font-bold text-orange-600 mb-2">50+</div>
                    <div class="text-gray-600">Countries</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Values Section -->
        <section class="py-16 bg-gray-50">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
              <h2 class="text-3xl font-bold text-gray-900 mb-4">Our Values</h2>
              <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                These core values guide everything we do and shape how we approach mental health support.
              </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div class="bg-white rounded-xl p-8 shadow-lg text-center">
                <div class="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6">
                  <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                  </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Privacy First</h3>
                <p class="text-gray-600">
                  Your conversations are completely confidential and encrypted. We never share your personal information.
                </p>
              </div>

              <div class="bg-white rounded-xl p-8 shadow-lg text-center">
                <div class="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6">
                  <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                  </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Evidence-Based</h3>
                <p class="text-gray-600">
                  Our AI is trained on proven psychological frameworks including CBT, DBT, and mindfulness techniques.
                </p>
              </div>

              <div class="bg-white rounded-xl p-8 shadow-lg text-center">
                <div class="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6">
                  <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Accessibility</h3>
                <p class="text-gray-600">
                  Mental health support should be available to everyone, regardless of location, time, or financial situation.
                </p>
              </div>
            </div>
          </div>
        </section>

        <!-- Team Section -->
        <section class="py-16 bg-white">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
              <h2 class="text-3xl font-bold text-gray-900 mb-4">Our Team</h2>
              <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                We're a diverse team of psychologists, AI researchers, and engineers passionate about mental health.
              </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div class="text-center">
                <div class="bg-gradient-to-br from-blue-400 to-blue-600 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-4">
                  <span class="text-white text-2xl font-bold">DR</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Dr. Sarah Johnson</h3>
                <p class="text-blue-600 mb-2">Clinical Psychologist & Co-Founder</p>
                <p class="text-gray-600 text-sm">
                  15+ years in clinical psychology, specializing in CBT and digital mental health interventions.
                </p>
              </div>

              <div class="text-center">
                <div class="bg-gradient-to-br from-purple-400 to-purple-600 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-4">
                  <span class="text-white text-2xl font-bold">AM</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Alex Martinez</h3>
                <p class="text-purple-600 mb-2">AI Research Lead</p>
                <p class="text-gray-600 text-sm">
                  PhD in Machine Learning with focus on natural language processing and conversational AI.
                </p>
              </div>

              <div class="text-center">
                <div class="bg-gradient-to-br from-green-400 to-green-600 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-4">
                  <span class="text-white text-2xl font-bold">MK</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Maria Kim</h3>
                <p class="text-green-600 mb-2">Product Manager</p>
                <p class="text-gray-600 text-sm">
                  Former healthcare product manager with expertise in user experience and digital health platforms.
                </p>
              </div>
            </div>
          </div>
        </section>

        <!-- CTA Section -->
        <section class="py-16 bg-gradient-to-br from-blue-600 to-purple-600 text-white">
          <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold mb-6">Ready to Start Your Journey?</h2>
            <p class="text-xl text-blue-100 mb-8">
              Join thousands of users who have already transformed their mental health with our platform.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              <a routerLink="/sign-up" 
                 class="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 rounded-lg text-lg font-semibold transition-colors">
                Start Free Trial
              </a>
              <a routerLink="/pricing" 
                 class="border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold transition-colors">
                View Pricing
              </a>
            </div>
          </div>
        </section>
      </main>

      <app-footer></app-footer>
    </div>
  `,
  styleUrls: ['./about.component.scss']
})
export class AboutComponent {
}
