import { Injectable, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { ChatRepository } from '../repository/chat.repository';
import { Chat } from '../models/chat.model';
import { Message } from '../models/message.model';
import { CreateChatDto, UpdateChatDto, AddMessageDto } from '../dtos/chat.dto';

@Injectable()
export class ChatService {
  constructor(private chatRepository: ChatRepository) {}

  /**
   * Get all chats for a user
   * @param userId The ID of the user
   * @returns Array of chats with basic metadata
   */
  async getAllChats(userId: string): Promise<Partial<Chat>[]> {
    return this.chatRepository.findAllChats(userId);
  }

  /**
   * Get a chat by its ID
   * @param chatId The ID of the chat
   * @param userId The ID of the user (for authorization)
   * @returns The complete chat with all messages and related data
   */
  async getChatById(chatId: string, userId: string): Promise<Chat> {
    const chat = await this.chatRepository.findChatById(chatId);
    
    // Ensure the user owns this chat
    if (chat.userId !== userId) {
      throw new UnauthorizedException('You do not have permission to access this chat');
    }
    
    return chat;
  }

  /**
   * Create a new chat
   * @param createChatDto The chat data to create
   * @param userId The ID of the user who owns the chat
   * @returns The created chat
   */
  async createChat(createChatDto: CreateChatDto, userId: string): Promise<Chat> {
    const chatData: Omit<Chat, 'id'> = {
      ...createChatDto,
      userId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    return this.chatRepository.createChat(chatData);
  }

  /**
   * Update an existing chat
   * @param chatId The ID of the chat to update
   * @param updateChatDto The chat data to update
   * @param userId The ID of the user (for authorization)
   * @returns The updated chat
   */
  async updateChat(chatId: string, updateChatDto: UpdateChatDto, userId: string): Promise<Chat> {
    // Verify ownership
    await this.verifyOwnership(chatId, userId);
    
    return this.chatRepository.updateChat(chatId, updateChatDto);
  }

  /**
   * Delete a chat
   * @param chatId The ID of the chat to delete
   * @param userId The ID of the user (for authorization)
   * @returns Success message
   */
  async deleteChat(chatId: string, userId: string): Promise<{ message: string }> {
    // Verify ownership
    await this.verifyOwnership(chatId, userId);
    
    await this.chatRepository.deleteChat(chatId);
    return { message: 'Chat deleted successfully' };
  }

  /**
   * Add a message to a chat
   * @param chatId The ID of the chat
   * @param addMessageDto The message to add
   * @param userId The ID of the user (for authorization)
   * @returns The added message and updated chat
   */
  async addMessage(
    chatId: string, 
    addMessageDto: AddMessageDto, 
    userId: string
  ): Promise<{ message: Message; chat: Partial<Chat> }> {
    // Verify ownership
    await this.verifyOwnership(chatId, userId);
    
    const timestamp = addMessageDto.timestamp || new Date().toISOString();
    
    return this.chatRepository.addMessage(chatId, {
      ...addMessageDto,
      timestamp
    });
  }

  /**
   * Verify that a user owns a chat
   * @param chatId The ID of the chat
   * @param userId The ID of the user
   * @throws UnauthorizedException if the user doesn't own the chat
   */
  private async verifyOwnership(chatId: string, userId: string): Promise<void> {
    try {
      const chat = await this.chatRepository.findChatById(chatId);
      
      if (chat.userId !== userId) {
        throw new UnauthorizedException('You do not have permission to modify this chat');
      }
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new Error('Failed to verify chat ownership');
    }
  }
}
