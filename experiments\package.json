{"name": "@experiments/source", "version": "0.0.0", "license": "Apache License", "scripts": {"prepare": "husky install", "dev-be-psy-chat": "nx serve psychology-chat", "build-be-psy-chat": "nx build psychology-chat"}, "private": true, "dependencies": {"@anatine/zod-nestjs": "^2.0.10", "@anatine/zod-openapi": "^2.2.7", "@angular/animations": "19.1.8", "@angular/common": "19.1.8", "@angular/compiler": "19.1.8", "@angular/core": "19.1.8", "@angular/forms": "19.1.8", "@angular/platform-browser": "19.1.8", "@angular/platform-browser-dynamic": "19.1.8", "@angular/router": "19.1.8", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@nestjs/axios": "^3.1.1", "@nestjs/common": "^10.4.15", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.15", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.0.2", "@nestjs/swagger": "^7.4.2", "axios": "1.7.8", "bcrypt": "^5.1.1", "lint-staged": "^15.2.10", "openapi3-ts": "^4.4.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.13.3", "reflect-metadata": "^0.1.13", "rxjs": "~7.8.0", "swagger-ui-express": "^5.0.1", "twitter-api-v2": "^1.18.1", "zod": "^3.24.2", "zone.js": "0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.0", "@angular-devkit/core": "19.1.9", "@angular-devkit/schematics": "19.1.9", "@angular/cli": "~19.1.0", "@angular/compiler-cli": "19.1.8", "@angular/language-service": "19.1.8", "@eslint/js": "^9.8.0", "@nestjs/schematics": "^10.0.1", "@nestjs/testing": "^10.0.2", "@nrwl/workspace": "^19.8.4", "@nx/angular": "^20.1.4", "@nx/devkit": "20.4.6", "@nx/eslint": "20.4.6", "@nx/eslint-plugin": "20.4.6", "@nx/jest": "20.4.6", "@nx/js": "20.4.6", "@nx/nest": "20.4.6", "@nx/node": "20.4.6", "@nx/playwright": "20.4.6", "@nx/web": "20.4.6", "@nx/webpack": "20.4.6", "@nx/workspace": "20.4.6", "@playwright/test": "^1.36.0", "@schematics/angular": "19.1.9", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/bcrypt": "^5.0.2", "@types/jest": "^29.5.12", "@types/knex": "^0.15.2", "@types/node": "18.16.9", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/utils": "8.25.0", "angular-eslint": "19.1.0", "eslint": "^9.8.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-playwright": "^1.6.2", "husky": "^9.1.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-preset-angular": "14.4.2", "knex": "^3.1.0", "nx": "20.4.6", "prettier": "^3.1.1", "prettier-plugin-organize-imports": "^4.1.0", "supertest": "^7.1.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.2", "tslib": "^2.3.0", "typescript": "5.7.3", "typescript-eslint": "8.25.0", "webpack-cli": "^5.1.4"}}