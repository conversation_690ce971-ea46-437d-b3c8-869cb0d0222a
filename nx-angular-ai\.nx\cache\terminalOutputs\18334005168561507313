[1m[33mComponent HMR has been enabled.[39m[22m
[1m[33mIf you encounter application reload issues, you can manually reload the page to bypass HMR and/or disable this feature with the `--no-hmr` command line option.[39m[22m
[1m[33mPlease consider reporting any issues you encounter here: https://github.com/angular/angular-cli/issues[39m[22m
[1m[33m[39m[22m
[33m❯[39m Building...
[32m✔[39m Building...
[37m[1mInitial chunk files[22m[2m | [22m[1mNames[22m        [2m | [22m [1mRaw size[22m[39m
[37m[32mmain.js[39m[37m            [2m | [22m[2mmain[22m         [2m | [22m[36m177.63 kB[39m[37m[2m | [22m[39m
[37m[32mpolyfills.js[39m[37m       [2m | [22m[2mpolyfills[22m    [2m | [22m [36m90.20 kB[39m[37m[2m | [22m[39m
[37m[32mstyles.css[39m[37m         [2m | [22m[2mstyles[22m       [2m | [22m [36m27.75 kB[39m[37m[2m | [22m[39m
[37m[39m
[37m[1m [22m                  [2m | [22m[1mInitial total[22m[2m | [22m[1m295.59 kB[22m[39m
[37m[39m
[37mApplication bundle generation complete. [5.488 seconds][39m
[37m[39m
[37mWatch mode enabled. Watching for file changes...[39m
[37mNOTE: Raw file sizes do not reflect development server per-request transformations.[39m
  [32m➜[39m  [1mLocal[22m:   [36mhttp://localhost:[1m4200[22m/[39m
[2m[32m  ➜[39m[22m[2m  press [22m[1mh + enter[22m[2m to show help[22m
