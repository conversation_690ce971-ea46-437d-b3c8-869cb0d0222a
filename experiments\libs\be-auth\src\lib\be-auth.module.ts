import { Modu<PERSON>, OnModuleInit } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { AuthController } from './controllers/auth.controller';
import { AuthService } from './services/be-auth.service';
import { UserRepository } from './repository/be-user.repository';
import { JwtAuthGuard } from './guards/be-jwt-auth.guard';
import { JWT_SECRET } from './env.config';
import knex from 'knex';
import { centralizedConfig } from '@experiments/be-database';

/**
 * Authentication module for the backend
 * Provides user authentication and authorization services
 * @type:feature
 */
@Module({
  imports: [
    JwtModule.register({
      secret: JWT_SECRET || 'secret',
      signOptions: { expiresIn: '1h' },
      global: true, // Make JwtModule global so it's available everywhere
    }),
  ],
  controllers: [AuthController],
  providers: [
    AuthService, 
    UserRepository, 
    JwtAuthGuard,
    {
      provide: 'KnexConnection',
      useValue: knex(centralizedConfig),
    }
  ],
  exports: [
    AuthService, 
    JwtAuthGuard, 
    'KnexConnection',
    JwtModule, // Export JwtModule to make JwtService available to other modules
  ],
})
export class BeAuthModule implements OnModuleInit {
  onModuleInit() {
    console.log('BeAuthModule initialized');
  }
}
