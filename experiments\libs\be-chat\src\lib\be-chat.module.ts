import { Modu<PERSON>, OnModuleInit } from '@nestjs/common';
import { Chat<PERSON>ontroller } from './controllers/chat.controller';
import { ChatService } from './services/chat.service';
import { ChatRepository } from './repository/chat.repository';
import { centralizedConfig } from '@experiments/be-database';
import { BeAuthModule } from '@experiments/be-auth';
import knex from 'knex';

/**
 * Chat module for the backend
 * Provides chat storage and management services
 * @type:feature
 */
@Module({
  imports: [
    BeAuthModule, // Import BeAuthModule to provide JwtService and JwtAuthGuard
  ],
  controllers: [ChatController],
  providers: [
    ChatService, 
    ChatRepository,
    {
      provide: 'KnexConnection',
      useFactory: () => {
        // Use the centralized database configuration from be-database
        return knex(centralizedConfig);
      },
    }
  ],
  exports: [ChatService, 'KnexConnection'],
})
export class BeChatModule implements OnModuleInit {
  async onModuleInit() {
    console.log('Starting chat migrations...');
    const knexInstance = knex(centralizedConfig);
    
    try {
      const [batchNo, log] = await knexInstance.migrate.latest();
      console.log(`Batch ${batchNo} run: ${log.length} migrations`);
      console.log('Chat migrations:', log);
    } catch (error) {
      console.error('Chat migration failed:', error);
    } finally {
      await knexInstance.destroy();
    }
    console.log('Chat migration process completed');
  }
}
