const nx = require('@nx/eslint-plugin');

module.exports = [
  ...nx.configs['flat/base'],
  ...nx.configs['flat/typescript'],
  ...nx.configs['flat/javascript'],
  {
    ignores: ['**/dist'],
  },
  {
    files: ['**/*.ts', '**/*.tsx', '**/*.js', '**/*.jsx'],
    rules: {
      '@nx/enforce-module-boundaries': [
        'error',
        {
          enforceBuildableLibDependency: true,
          allow: ['^.*/eslint(\\.base)?\\.config\\.[cm]?js$'],
          depConstraints: [
            {
              sourceTag: '*',
              onlyDependOnLibsWithTags: ['*'],
            },
          ],
        },
      ],
    },
  },
  {
    files: ['**/*.ts', '**/*.tsx', '**/*.js', '**/*.jsx'],
    // Override or add rules here
    rules: {
      '@nx/enforce-module-boundaries': [
        'error',
        {
          enforceBuildableLibDependency: true,
          allow: [],
          depConstraints: [
            {
              sourceTag: 'type:app',
              onlyDependOnLibsWithTags: ['*'],
            },
            {
              sourceTag: 'type:type',
              onlyDependOnLibsWithTags: ['type:type'],
            },
            {
              sourceTag: 'type:util',
              onlyDependOnLibsWithTags: ['type:util', 'type:type'],
            },
            {
              sourceTag: 'type:service',
              onlyDependOnLibsWithTags: [
                'type:util',
                'type:type',
                'type:data-access',
              ],
            },
            {
              sourceTag: 'type:lib',
              onlyDependOnLibsWithTags: [
                'type:util',
                'type:type',
                'type:data-access',
                'type:database',
                'type:feature'
              ],
            },
            {
              sourceTag: 'type:feature',
              onlyDependOnLibsWithTags: [
                'type:util',
                'type:type',
                'type:data-access',
                'type:database',
                'type:feature'
              ],
            },
            {
              sourceTag: 'type:shell',
              onlyDependOnLibsWithTags: ['type:lib', 'type:feature'],
            },
            {
              sourceTag: 'type:data-access',
              onlyDependOnLibsWithTags: [
                'type:util',
                'type:type',
                'type:data-access',
              ],
            },
            {
              sourceTag: 'type:database',
              onlyDependOnLibsWithTags: [],
            },
            {
              sourceTag: 'type:e2e',
              onlyDependOnLibsWithTags: ['scope:testing'],
            },
          ],
        },
      ],

    },
  },
];
