{"name": "psychology-chat", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/psychology-chat/src", "projectType": "application", "tags": ["type:app"], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "psychology-chat:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "psychology-chat:build:development"}, "production": {"buildTarget": "psychology-chat:build:production"}}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/psychology-chat/jest.config.ts"}}}}