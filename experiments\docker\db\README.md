# Database Setup

This directory contains the PostgreSQL database setup for the Psychology Chat application.

## Starting the Database

To start the database independently:

```bash
cd /path/to/experiments/docker/db
docker-compose up -d
```

This will:
1. Create a PostgreSQL database container
2. Initialize the `psy_chat` database using the `init.sql` script
3. Create necessary tables
4. Persist data in a named volume `psychology_chat_postgres_data`

## Stopping the Database

To stop the database without removing data:

```bash
docker-compose down
```

To stop the database and remove all data (use with caution):

```bash
docker-compose down -v
```

## Checking Database Status

To check if the database is running:

```bash
docker ps | grep postgres_db
```

To check if the `psy_chat` database exists:

```bash
docker exec -it postgres_db psql -U postgres -c "SELECT datname FROM pg_database WHERE datname = 'psy_chat';"
```

## Connecting to the Database

The database is accessible on:
- Host: localhost (or `db` from inside Docker containers)
- Port: 5432
- Username: postgres
- Password: password
- Database: psy_chat

## Running the Application

After starting the database, you can run the application using the main docker-compose file:

```bash
cd /path/to/experiments
docker-compose up -d
```

The application will connect to the existing database container.
