# Contribution Guidelines

## Introduction

Thank you for your interest in contributing to the Angular Micro-Frontend Chess Game! To maintain the quality of the codebase and ensure a coherent development process, we adhere to certain standards and practices. One of these is following the Conventional Commits specification.

## Conventional Commits

We use the [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/#summary) specification as a guideline for our commit messages. This specification provides an easy set of rules for creating an explicit commit history, making it easier to write automated tools on top of and enabling better communication among the team members.

### Commit Message Format

A commit message should be structured as follows:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

Where `type` can be:

- **feat**: A new feature
- **fix**: A bug fix
- **docs**: Documentation only changes
- **style**: Changes that do not affect the meaning of the code (white-space, formatting, missing semicolons, etc.)
- **refactor**: A code change that neither fixes a bug nor adds a feature
- **perf**: A code change that improves performance
- **test**: Adding missing tests or correcting existing tests
- **chore**: Changes to the build process or auxiliary tools and libraries such as documentation generation

### Examples

- `feat(chessboard): add 'move played' sound effect`
- `fix(timer): handle edge case in stopwatch reset`
- `docs(readme): update installation instructions`

## How to Contribute

1. **Fork the Repository**: Start by forking the repository to your GitHub account.
2. **Clone the Fork**: Clone the forked repository to your local machine.
3. **Create a New Branch**: For each new feature or fix, create a new branch from `main`.
4. **Make Your Changes**: Implement your changes, following the coding standards and commit message format.
5. **Test Your Changes**: Ensure that your changes do not break any existing functionality.
6. **Submit a Pull Request**: Push your branch to your fork on GitHub and submit a pull request.

## Code of Conduct

We are committed to fostering a welcoming and respectful community. Please adhere to our Code of Conduct during your interactions within the project.

## Questions?

If you have any questions or need more clarification, feel free to open an issue in the repository. We are more than happy to assist you.

Thank you for contributing to the Angular Micro-Frontend Chess Game! Your efforts help us improve and evolve this project.
