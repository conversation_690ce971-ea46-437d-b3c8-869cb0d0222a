import { Controller, Post, Body } from '@nestjs/common';
import { AuthService } from '../services/be-auth.service';
import { RegisterDto, LoginDto } from '../dtos/be-auth.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

@ApiTags('Auth') // Swagger Tag for grouping endpoints
@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post('register')
  @ApiOperation({ summary: 'Register a new user' })
  @ApiResponse({ status: 201, description: 'User registered successfully', type: RegisterDto })
  register(@Body() data: RegisterDto) {
    return this.authService.register(data);
  }

  @Post('login')
  @ApiOperation({ summary: 'Login and receive JWT token' })
  @ApiResponse({ status: 200, description: 'Login successful', type: LoginDto })
  login(@Body() data: LoginDto) {
    return this.authService.login(data);
  }
}
