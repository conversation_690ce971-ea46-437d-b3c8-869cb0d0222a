// CommonJS migration file

/**
 * Migration to create the chats table
 * This table stores the main chat metadata
 */
exports.up = async function(knex) {
  await knex.schema.createTable('chats', (table) => {
    table.string('id').primary();
    table.string('title').notNullable();
    table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    table.timestamp('updated_at').notNullable().defaultTo(knex.fn.now());
    table.string('flow_chat_id').nullable();
    table.string('session_id').nullable();
    table.integer('user_id').notNullable();
    
    // Add foreign key reference to users table
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    
    // Add indexes for faster lookups
    table.index('user_id');
    table.index('updated_at');
  });
  
  // Add a comment to the table for documentation
  await knex.raw(`COMMENT ON TABLE chats IS 'Stores chat metadata for the chat storage API'`);
};

exports.down = async function(knex) {
  await knex.schema.dropTable('chats');
};
