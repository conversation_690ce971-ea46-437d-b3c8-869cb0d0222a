import { z } from 'zod';
import { MessageModel } from './message.model';
import { AgentReasoningStepModel } from './agent-reasoning-step.model';
import { SourceDocumentModel } from './source-document.model';

/**
 * Chat model schema definition using Zod
 * Based on the API specification
 */
export const ChatModel = z.object({
  id: z.string().uuid().optional(),
  title: z.string(),
  messages: z.array(z.lazy(() => MessageModel)).default([]),
  createdAt: z.string().datetime().optional(),
  updatedAt: z.string().datetime().optional(),
  flowChatId: z.string().optional(),
  sessionId: z.string().optional(),
  reasoningSteps: z.array(z.lazy(() => AgentReasoningStepModel)).optional(),
  sourceDocuments: z.array(z.lazy(() => SourceDocumentModel)).optional(),
  userId: z.string(),
});

export type Chat = z.infer<typeof ChatModel>;
