/**
 * Migration to create the users table
 * Enhanced with additional security measures based on previous security issues
 */
exports.up = async function(knex) {
  await knex.schema.createTable('users', (table) => {
    table.increments('id').primary();
    table.string('email').unique().notNullable();
    table.string('password').notNullable();
    table.boolean('is_active').defaultTo(true);
    table.timestamp('last_login').nullable();
    table.timestamps(true, true);
    
    // Add index for faster lookups
    table.index('email');
  });
  
  // Add a comment to the table for documentation
  await knex.raw(`COMMENT ON TABLE users IS 'User accounts for authentication'`);
};

exports.down = async function(knex) {
  await knex.schema.dropTable('users');
};
