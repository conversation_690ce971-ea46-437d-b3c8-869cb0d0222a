import { Test, TestingModule } from '@nestjs/testing';
import { ChatRepository } from '../repository/chat.repository';
import { v4 as uuidv4 } from 'uuid';
import { NotFoundException } from '@nestjs/common';

const TEST_USER_ID = uuidv4();

// Define a type for our mock Knex function
interface MockKnex extends jest.Mock {
  transaction: jest.Mock;
}

// Define a type for transaction object
interface MockTransaction {
  commit: jest.Mock;
  rollback: jest.Mock;
}

describe('ChatRepository', () => {
  let repository: ChatRepository;
  let knex: MockKnex;

  beforeEach(async () => {
    // Create a simple mock function for Knex with the correct type
    const mockKnex = jest.fn() as MockKnex;
    
    // Add transaction method to the mock
    mockKnex.transaction = jest.fn();
    
    knex = mockKnex;
    
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ChatRepository,
        {
          provide: 'KNEX_CONNECTION',
          useValue: knex,
        },
      ],
    }).compile();

    repository = module.get<ChatRepository>(ChatRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(repository).toBeDefined();
  });

  describe('findAllChats', () => {
    it('should return all chats for a user', async () => {
      const mockChats = [
        {
          id: uuidv4(),
          title: 'Test Chat 1',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          userId: TEST_USER_ID,
        },
        {
          id: uuidv4(),
          title: 'Test Chat 2',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          userId: TEST_USER_ID,
        },
      ];

      // Mock the knex query builder
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        select: jest.fn().mockResolvedValue(mockChats),
      };

      knex.mockReturnValue(mockQueryBuilder);

      const result = await repository.findAllChats(TEST_USER_ID);
      expect(result).toEqual(mockChats);
      expect(knex).toHaveBeenCalledWith('chats');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('userId', TEST_USER_ID);
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('updatedAt', 'desc');
    });
  });

  describe('findChatById', () => {
    it('should find a chat by ID', async () => {
      const chatId = uuidv4();
      const expectedChat = {
        id: chatId,
        title: 'Test Chat',
        messages: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: TEST_USER_ID,
      };

      // Mock the knex query builder
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(expectedChat),
      };

      knex.mockReturnValue(mockQueryBuilder);

      const result = await repository.findChatById(chatId);
      expect(result).toEqual(expectedChat);
      expect(knex).toHaveBeenCalledWith('chats');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('id', chatId);
      expect(mockQueryBuilder.first).toHaveBeenCalled();
    });

    it('should throw NotFoundException if chat is not found', async () => {
      const chatId = uuidv4();

      // Mock the knex query builder
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(null),
      };

      knex.mockReturnValue(mockQueryBuilder);

      await expect(repository.findChatById(chatId)).rejects.toThrow(NotFoundException);
      expect(knex).toHaveBeenCalledWith('chats');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('id', chatId);
      expect(mockQueryBuilder.first).toHaveBeenCalled();
    });
  });

  describe('createChat', () => {
    it('should create a new chat', async () => {
      const chatData = {
        title: 'New Test Chat',
        messages: [],
        userId: TEST_USER_ID,
      };

      const expectedChat = {
        id: expect.any(String),
        ...chatData,
        createdAt: expect.any(String),
        updatedAt: expect.any(String),
      };

      // Mock the knex query builder
      const mockQueryBuilder = {
        insert: jest.fn().mockReturnThis(),
        returning: jest.fn().mockResolvedValue([expectedChat]),
      };

      knex.mockReturnValue(mockQueryBuilder);

      const result = await repository.createChat(chatData);
      expect(result).toEqual(expectedChat);
      expect(knex).toHaveBeenCalledWith('chats');
      expect(mockQueryBuilder.insert).toHaveBeenCalledWith(expect.objectContaining({
        id: expect.any(String),
        title: chatData.title,
        userId: chatData.userId,
        createdAt: expect.any(String),
        updatedAt: expect.any(String),
      }));
      expect(mockQueryBuilder.returning).toHaveBeenCalledWith('*');
    });
  });

  describe('updateChat', () => {
    it('should update a chat', async () => {
      const chatId = uuidv4();
      const updateData = {
        title: 'Updated Test Chat',
      };

      const updatedChat = {
        id: chatId,
        title: updateData.title,
        messages: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: TEST_USER_ID,
      };

      // Mock the knex query builder
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        returning: jest.fn().mockResolvedValue([updatedChat]),
      };

      knex.mockReturnValue(mockQueryBuilder);

      const result = await repository.updateChat(chatId, updateData);
      expect(result).toEqual(updatedChat);
      expect(knex).toHaveBeenCalledWith('chats');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('id', chatId);
      expect(mockQueryBuilder.update).toHaveBeenCalledWith(expect.objectContaining({
        title: updateData.title,
        updatedAt: expect.any(String),
      }));
      expect(mockQueryBuilder.returning).toHaveBeenCalledWith('*');
    });
  });

  describe('deleteChat', () => {
    it('should delete a chat', async () => {
      const chatId = uuidv4();

      // Mock the knex query builder
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        del: jest.fn().mockResolvedValue(1),
      };

      knex.mockReturnValue(mockQueryBuilder);

      const result = await repository.deleteChat(chatId);
      expect(result).toBe(true);
      expect(knex).toHaveBeenCalledWith('chats');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('id', chatId);
      expect(mockQueryBuilder.del).toHaveBeenCalled();
    });
  });

  describe('addMessage', () => {
    it('should add a message to a chat', async () => {
      const chatId = uuidv4();
      const messageData = {
        role: 'user' as const,
        content: 'Test message content',
        isComplete: true,
      };

      // Define types for mock data
      interface ReasoningStep {
        id: string;
        messageId: string;
        step: number;
        content: string;
        createdAt: string;
      }

      interface SourceDocument {
        id: string;
        messageId: string;
        title: string;
        content: string;
        url?: string;
        createdAt: string;
      }

      const mockReasoningSteps: ReasoningStep[] = [];
      const mockSourceDocuments: SourceDocument[] = [];

      const mockMessage = {
        id: uuidv4(),
        chatId,
        role: messageData.role,
        content: messageData.content,
        isComplete: messageData.isComplete,
        timestamp: new Date().toISOString(),
        reasoningSteps: mockReasoningSteps,
        sourceDocuments: mockSourceDocuments,
      };

      const mockUpdatedChat = {
        id: chatId,
        updatedAt: new Date().toISOString(),
      };

      // Mock transaction
      const mockTrx: MockTransaction = {
        commit: jest.fn(),
        rollback: jest.fn(),
      };

      // Mock the knex transaction
      knex.transaction.mockImplementation(async (callback: (trx: MockTransaction) => Promise<unknown>) => {
        return callback(mockTrx);
      });

      // Mock message insert
      const mockMessageQueryBuilder = {
        insert: jest.fn().mockReturnThis(),
        returning: jest.fn().mockResolvedValue([mockMessage]),
      };

      // Mock chat update
      const mockChatQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        returning: jest.fn().mockResolvedValue([mockUpdatedChat]),
      };

      // Setup the mock for both queries
      knex.mockImplementation((table: string) => {
        if (table === 'messages') {
          return mockMessageQueryBuilder;
        }
        if (table === 'chats') {
          return mockChatQueryBuilder;
        }
        return {};
      });

      const result = await repository.addMessage(chatId, messageData);
      expect(result).toEqual({
        message: mockMessage,
        chat: mockUpdatedChat,
      });
      expect(knex.transaction).toHaveBeenCalled();
      expect(mockMessageQueryBuilder.insert).toHaveBeenCalledWith(expect.objectContaining({
        id: expect.any(String),
        chatId,
        role: messageData.role,
        content: messageData.content,
        isComplete: messageData.isComplete,
        timestamp: expect.any(String),
      }));
      expect(mockChatQueryBuilder.where).toHaveBeenCalledWith('id', chatId);
      expect(mockChatQueryBuilder.update).toHaveBeenCalledWith({
        updatedAt: expect.any(String),
      });
    });

    it('should throw NotFoundException if chat is not found', async () => {
      const chatId = uuidv4();
      const messageData = {
        role: 'user' as const,
        content: 'Test message content',
        isComplete: true,
      };

      // Mock the knex query builder
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(null),
      };

      knex.mockReturnValue(mockQueryBuilder);

      await expect(repository.addMessage(chatId, messageData)).rejects.toThrow(NotFoundException);
      expect(knex).toHaveBeenCalledWith('chats');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('id', chatId);
      expect(mockQueryBuilder.first).toHaveBeenCalled();
    });
  });
});
