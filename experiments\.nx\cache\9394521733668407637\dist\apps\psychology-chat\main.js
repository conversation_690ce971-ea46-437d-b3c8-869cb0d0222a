/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ([
/* 0 */,
/* 1 */
/***/ ((module) => {

module.exports = require("@nestjs/common");

/***/ }),
/* 2 */
/***/ ((module) => {

module.exports = require("@nestjs/core");

/***/ }),
/* 3 */
/***/ ((module) => {

module.exports = require("express");

/***/ }),
/* 4 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AppModule = void 0;
const tslib_1 = __webpack_require__(5);
const common_1 = __webpack_require__(1);
const be_auth_1 = __webpack_require__(6);
const config_1 = __webpack_require__(26);
const be_chat_1 = __webpack_require__(27);
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = tslib_1.__decorate([
    (0, common_1.Module)({
        imports: [
            be_auth_1.BeAuthModule,
            be_chat_1.BeChatModule,
            config_1.ConfigModule.forRoot({
                isGlobal: true,
            }),
        ]
    })
], AppModule);


/***/ }),
/* 5 */
/***/ ((module) => {

module.exports = require("tslib");

/***/ }),
/* 6 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
const tslib_1 = __webpack_require__(5);
tslib_1.__exportStar(__webpack_require__(7), exports);
tslib_1.__exportStar(__webpack_require__(10), exports);
tslib_1.__exportStar(__webpack_require__(11), exports);
tslib_1.__exportStar(__webpack_require__(19), exports);
tslib_1.__exportStar(__webpack_require__(9), exports);
// Removed export from knexfile as we're now using the centralized configuration from be-database


/***/ }),
/* 7 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.BeAuthModule = void 0;
const tslib_1 = __webpack_require__(5);
const common_1 = __webpack_require__(1);
const jwt_1 = __webpack_require__(8);
const auth_controller_1 = __webpack_require__(9);
const be_auth_service_1 = __webpack_require__(10);
const be_user_repository_1 = __webpack_require__(11);
const be_jwt_auth_guard_1 = __webpack_require__(19);
const env_config_1 = __webpack_require__(20);
const knex_1 = tslib_1.__importDefault(__webpack_require__(12));
const be_database_1 = __webpack_require__(22);
/**
 * Authentication module for the backend
 * Provides user authentication and authorization services
 * @type:feature
 */
let BeAuthModule = class BeAuthModule {
    onModuleInit() {
        console.log('BeAuthModule initialized');
    }
};
exports.BeAuthModule = BeAuthModule;
exports.BeAuthModule = BeAuthModule = tslib_1.__decorate([
    (0, common_1.Module)({
        imports: [
            jwt_1.JwtModule.register({
                secret: env_config_1.JWT_SECRET || 'secret',
                signOptions: { expiresIn: '1h' },
                global: true, // Make JwtModule global so it's available everywhere
            }),
        ],
        controllers: [auth_controller_1.AuthController],
        providers: [
            be_auth_service_1.AuthService,
            be_user_repository_1.UserRepository,
            be_jwt_auth_guard_1.JwtAuthGuard,
            {
                provide: 'KnexConnection',
                useValue: (0, knex_1.default)(be_database_1.centralizedConfig),
            }
        ],
        exports: [
            be_auth_service_1.AuthService,
            be_jwt_auth_guard_1.JwtAuthGuard,
            'KnexConnection',
            jwt_1.JwtModule, // Export JwtModule to make JwtService available to other modules
        ],
    })
], BeAuthModule);


/***/ }),
/* 8 */
/***/ ((module) => {

module.exports = require("@nestjs/jwt");

/***/ }),
/* 9 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AuthController = void 0;
const tslib_1 = __webpack_require__(5);
const common_1 = __webpack_require__(1);
const be_auth_service_1 = __webpack_require__(10);
const be_auth_dto_1 = __webpack_require__(16);
const swagger_1 = __webpack_require__(18);
let AuthController = class AuthController {
    constructor(authService) {
        this.authService = authService;
    }
    register(data) {
        return this.authService.register(data);
    }
    login(data) {
        return this.authService.login(data);
    }
};
exports.AuthController = AuthController;
tslib_1.__decorate([
    (0, common_1.Post)('register'),
    (0, swagger_1.ApiOperation)({ summary: 'Register a new user' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'User registered successfully', type: be_auth_dto_1.RegisterDto }),
    tslib_1.__param(0, (0, common_1.Body)()),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [typeof (_b = typeof be_auth_dto_1.RegisterDto !== "undefined" && be_auth_dto_1.RegisterDto) === "function" ? _b : Object]),
    tslib_1.__metadata("design:returntype", void 0)
], AuthController.prototype, "register", null);
tslib_1.__decorate([
    (0, common_1.Post)('login'),
    (0, swagger_1.ApiOperation)({ summary: 'Login and receive JWT token' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Login successful', type: be_auth_dto_1.LoginDto }),
    tslib_1.__param(0, (0, common_1.Body)()),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [typeof (_c = typeof be_auth_dto_1.LoginDto !== "undefined" && be_auth_dto_1.LoginDto) === "function" ? _c : Object]),
    tslib_1.__metadata("design:returntype", void 0)
], AuthController.prototype, "login", null);
exports.AuthController = AuthController = tslib_1.__decorate([
    (0, swagger_1.ApiTags)('Auth') // Swagger Tag for grouping endpoints
    ,
    (0, common_1.Controller)('auth'),
    tslib_1.__metadata("design:paramtypes", [typeof (_a = typeof be_auth_service_1.AuthService !== "undefined" && be_auth_service_1.AuthService) === "function" ? _a : Object])
], AuthController);


/***/ }),
/* 10 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AuthService = void 0;
const tslib_1 = __webpack_require__(5);
const common_1 = __webpack_require__(1);
const be_user_repository_1 = __webpack_require__(11);
const jwt_1 = __webpack_require__(8);
const bcrypt = tslib_1.__importStar(__webpack_require__(15));
let AuthService = class AuthService {
    constructor(userRepo, jwtService) {
        this.userRepo = userRepo;
        this.jwtService = jwtService;
    }
    async register(data) {
        const hashedPassword = await bcrypt.hash(data.password, 10);
        return this.userRepo.createUser({ ...data, password: hashedPassword });
    }
    async login(data) {
        const user = await this.userRepo.findByEmail(data.email);
        if (!user || !(bcrypt.compare(data.password, user.password))) {
            throw new Error('Invalid credentials');
        }
        const payload = { id: user.id, email: user.email };
        return { token: this.jwtService.sign(payload) };
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = tslib_1.__decorate([
    (0, common_1.Injectable)(),
    tslib_1.__metadata("design:paramtypes", [typeof (_a = typeof be_user_repository_1.UserRepository !== "undefined" && be_user_repository_1.UserRepository) === "function" ? _a : Object, typeof (_b = typeof jwt_1.JwtService !== "undefined" && jwt_1.JwtService) === "function" ? _b : Object])
], AuthService);


/***/ }),
/* 11 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UserRepository = void 0;
const tslib_1 = __webpack_require__(5);
const common_1 = __webpack_require__(1);
const knex_1 = __webpack_require__(12);
const be_user_model_1 = __webpack_require__(13);
let UserRepository = class UserRepository {
    constructor(knex) {
        this.knex = knex;
    }
    async createUser(user) {
        const [createdUser] = await this.knex('users').insert(user).returning('*');
        return be_user_model_1.UserModel.parse(createdUser);
    }
    async findByEmail(email) {
        const user = await this.knex('users').where({ email }).first();
        return user ? be_user_model_1.UserModel.parse(user) : null;
    }
};
exports.UserRepository = UserRepository;
exports.UserRepository = UserRepository = tslib_1.__decorate([
    (0, common_1.Injectable)(),
    tslib_1.__param(0, (0, common_1.Inject)('KnexConnection')),
    tslib_1.__metadata("design:paramtypes", [typeof (_a = typeof knex_1.Knex !== "undefined" && knex_1.Knex) === "function" ? _a : Object])
], UserRepository);


/***/ }),
/* 12 */
/***/ ((module) => {

module.exports = require("knex");

/***/ }),
/* 13 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UserModel = void 0;
const zod_1 = __webpack_require__(14);
exports.UserModel = zod_1.z.object({
    id: zod_1.z.number().optional(),
    email: zod_1.z.string().email(),
    password: zod_1.z.string(),
});


/***/ }),
/* 14 */
/***/ ((module) => {

module.exports = require("zod");

/***/ }),
/* 15 */
/***/ ((module) => {

module.exports = require("bcrypt");

/***/ }),
/* 16 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.LoginDto = exports.RegisterDto = exports.LoginSchema = exports.RegisterSchema = void 0;
const zod_1 = __webpack_require__(14);
const zod_nestjs_1 = __webpack_require__(17);
// ✅ Define Zod Schemas
exports.RegisterSchema = zod_1.z.object({
    email: zod_1.z.string().email('Invalid email address'),
    password: zod_1.z.string().min(6, 'Password must be at least 6 characters long'),
});
exports.LoginSchema = zod_1.z.object({
    email: zod_1.z.string().email('Invalid email address'),
    password: zod_1.z.string().min(6, 'Password must be at least 6 characters long'),
});
// ✅ Convert Zod Schemas into NestJS DTOs for Validation
class RegisterDto extends (0, zod_nestjs_1.createZodDto)(exports.RegisterSchema) {
}
exports.RegisterDto = RegisterDto;
class LoginDto extends (0, zod_nestjs_1.createZodDto)(exports.LoginSchema) {
}
exports.LoginDto = LoginDto;


/***/ }),
/* 17 */
/***/ ((module) => {

module.exports = require("@anatine/zod-nestjs");

/***/ }),
/* 18 */
/***/ ((module) => {

module.exports = require("@nestjs/swagger");

/***/ }),
/* 19 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.JwtAuthGuard = void 0;
const tslib_1 = __webpack_require__(5);
const common_1 = __webpack_require__(1);
const jwt_1 = __webpack_require__(8);
let JwtAuthGuard = class JwtAuthGuard {
    constructor(jwtService) {
        this.jwtService = jwtService;
    }
    canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const authHeader = request.headers.authorization;
        if (request.url.startsWith('/auth/'))
            return true; // Allow auth routes
        if (!authHeader)
            return false;
        try {
            const token = authHeader.split(' ')[1];
            request.user = this.jwtService.verify(token);
            return true;
        }
        catch {
            return false;
        }
    }
};
exports.JwtAuthGuard = JwtAuthGuard;
exports.JwtAuthGuard = JwtAuthGuard = tslib_1.__decorate([
    (0, common_1.Injectable)(),
    tslib_1.__metadata("design:paramtypes", [typeof (_a = typeof jwt_1.JwtService !== "undefined" && jwt_1.JwtService) === "function" ? _a : Object])
], JwtAuthGuard);


/***/ }),
/* 20 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.JWT_SECRET = exports.POSTGRES_DB = exports.POSTGRES_PASSWORD = exports.POSTGRES_USER = exports.POSTGRES_PORT = exports.POSTGRES_HOST = void 0;
const tslib_1 = __webpack_require__(5);
const process = tslib_1.__importStar(__webpack_require__(21));
process.env['NODE_ENV'] = process.env['NODE_ENV'] ?? 'development';
const EnvVariables = process.env;
exports.POSTGRES_HOST = EnvVariables['POSTGRES_HOST'] ?? 'localhost';
exports.POSTGRES_PORT = EnvVariables['POSTGRES_PORT'] ?? '5432';
exports.POSTGRES_USER = EnvVariables['POSTGRES_USER'] ?? 'postgres';
exports.POSTGRES_PASSWORD = EnvVariables['POSTGRES_PASSWORD'] ?? 'JK1zU0z2U7SiAo6';
exports.POSTGRES_DB = EnvVariables['POSTGRES_DB'] ?? 'psy_chat';
exports.JWT_SECRET = EnvVariables['JWT_SECRET'] ?? 'my_secret_key';


/***/ }),
/* 21 */
/***/ ((module) => {

module.exports = require("node:process");

/***/ }),
/* 22 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
const tslib_1 = __webpack_require__(5);
// Export database configuration
tslib_1.__exportStar(__webpack_require__(23), exports);
tslib_1.__exportStar(__webpack_require__(25), exports);


/***/ }),
/* 23 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.beChatConfig = exports.beAuthConfig = exports.centralizedConfig = exports.baseConfig = void 0;
const tslib_1 = __webpack_require__(5);
const dotenv = tslib_1.__importStar(__webpack_require__(24));
const env_config_1 = __webpack_require__(25);
dotenv.config();
/**
 * Base database configuration with security enhancements
 * Addresses previous security issues with unauthorized access attempts
 * and improves connection handling
 */
exports.baseConfig = {
    client: 'pg',
    connection: {
        host: env_config_1.POSTGRES_HOST,
        port: Number(env_config_1.POSTGRES_PORT),
        user: env_config_1.POSTGRES_USER,
        password: env_config_1.POSTGRES_PASSWORD,
        database: env_config_1.POSTGRES_DB,
        // Security enhancements
        ssl: process.env['NODE_ENV'] === 'production' ? { rejectUnauthorized: true } : false,
        connectionTimeout: Number(env_config_1.DB_CONNECTION_TIMEOUT),
    },
    pool: {
        min: Number(env_config_1.DB_POOL_MIN),
        max: Number(env_config_1.DB_POOL_MAX),
        idleTimeoutMillis: Number(env_config_1.DB_IDLE_TIMEOUT),
        // Properly terminate connections to avoid connection leaks
        afterCreate: (conn, done) => {
            conn.query('SELECT 1', (err) => {
                if (err) {
                    console.error('Error creating connection:', err);
                }
                done(err, conn);
            });
        },
    },
    // Improved logging for security monitoring
    log: {
        warn(message) {
            console.warn('[DATABASE WARNING]', message);
        },
        error(message) {
            console.error('[DATABASE ERROR]', message);
        },
        deprecate(message) {
            console.warn('[DATABASE DEPRECATED]', message);
        },
        debug(message) {
            if (process.env['NODE_ENV'] !== 'production') {
                console.log('[DATABASE DEBUG]', message);
            }
        },
    },
};
/**
 * Centralized database configuration for all migrations
 * This replaces the separate auth and chat configurations
 * to ensure migrations run in the correct order
 */
exports.centralizedConfig = {
    ...exports.baseConfig,
    migrations: {
        tableName: 'knex_migrations',
        directory: './libs/be-database/src/lib/migrations',
        extension: 'ts',
        loadExtensions: [".ts"],
    }
};
/**
 * Legacy configurations kept for backward compatibility
 * These should be deprecated in favor of the centralized configuration
 */
exports.beAuthConfig = {
    ...exports.centralizedConfig
};
exports.beChatConfig = {
    ...exports.centralizedConfig
};
exports["default"] = exports.centralizedConfig;


/***/ }),
/* 24 */
/***/ ((module) => {

module.exports = require("dotenv");

/***/ }),
/* 25 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.DB_IDLE_TIMEOUT = exports.DB_POOL_MAX = exports.DB_POOL_MIN = exports.DB_CONNECTION_TIMEOUT = exports.JWT_SECRET = exports.POSTGRES_DB = exports.POSTGRES_PASSWORD = exports.POSTGRES_USER = exports.POSTGRES_PORT = exports.POSTGRES_HOST = void 0;
const tslib_1 = __webpack_require__(5);
const process = tslib_1.__importStar(__webpack_require__(21));
process.env['NODE_ENV'] = process.env['NODE_ENV'] ?? 'development';
const EnvVariables = process.env;
// Database configuration
exports.POSTGRES_HOST = EnvVariables['POSTGRES_HOST'] ?? 'localhost';
exports.POSTGRES_PORT = EnvVariables['POSTGRES_PORT'] ?? '5432';
exports.POSTGRES_USER = EnvVariables['POSTGRES_USER'] ?? 'postgres';
exports.POSTGRES_PASSWORD = EnvVariables['POSTGRES_PASSWORD'] ?? 'JK1zU0z2U7SiAo6';
exports.POSTGRES_DB = EnvVariables['POSTGRES_DB'] ?? 'psy_chat';
// Authentication configuration
exports.JWT_SECRET = EnvVariables['JWT_SECRET'] ?? 'my_secret_key';
// Security enhancements based on previous security issues
exports.DB_CONNECTION_TIMEOUT = EnvVariables['DB_CONNECTION_TIMEOUT'] ?? '30000'; // 30 seconds
exports.DB_POOL_MIN = EnvVariables['DB_POOL_MIN'] ?? '2';
exports.DB_POOL_MAX = EnvVariables['DB_POOL_MAX'] ?? '10';
exports.DB_IDLE_TIMEOUT = EnvVariables['DB_IDLE_TIMEOUT'] ?? '30000'; // 30 seconds


/***/ }),
/* 26 */
/***/ ((module) => {

module.exports = require("@nestjs/config");

/***/ }),
/* 27 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
const tslib_1 = __webpack_require__(5);
tslib_1.__exportStar(__webpack_require__(28), exports);
tslib_1.__exportStar(__webpack_require__(30), exports);
tslib_1.__exportStar(__webpack_require__(29), exports);
tslib_1.__exportStar(__webpack_require__(31), exports);
tslib_1.__exportStar(__webpack_require__(37), exports);
tslib_1.__exportStar(__webpack_require__(34), exports);
tslib_1.__exportStar(__webpack_require__(35), exports);
tslib_1.__exportStar(__webpack_require__(36), exports);
tslib_1.__exportStar(__webpack_require__(33), exports);


/***/ }),
/* 28 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.BeChatModule = void 0;
const tslib_1 = __webpack_require__(5);
const common_1 = __webpack_require__(1);
const chat_controller_1 = __webpack_require__(29);
const chat_service_1 = __webpack_require__(30);
const chat_repository_1 = __webpack_require__(31);
const be_database_1 = __webpack_require__(22);
const be_auth_1 = __webpack_require__(6);
const knex_1 = tslib_1.__importDefault(__webpack_require__(12));
/**
 * Chat module for the backend
 * Provides chat storage and management services
 * @type:feature
 */
let BeChatModule = class BeChatModule {
    async onModuleInit() {
        console.log('Starting chat migrations...');
        const knexInstance = (0, knex_1.default)(be_database_1.centralizedConfig);
        try {
            const [batchNo, log] = await knexInstance.migrate.latest();
            console.log(`Batch ${batchNo} run: ${log.length} migrations`);
            console.log('Chat migrations:', log);
        }
        catch (error) {
            console.error('Chat migration failed:', error);
        }
        finally {
            await knexInstance.destroy();
        }
        console.log('Chat migration process completed');
    }
};
exports.BeChatModule = BeChatModule;
exports.BeChatModule = BeChatModule = tslib_1.__decorate([
    (0, common_1.Module)({
        imports: [
            be_auth_1.BeAuthModule, // Import BeAuthModule to provide JwtService and JwtAuthGuard
        ],
        controllers: [chat_controller_1.ChatController],
        providers: [
            chat_service_1.ChatService,
            chat_repository_1.ChatRepository,
            {
                provide: 'KnexConnection',
                useFactory: () => {
                    // Use the centralized database configuration from be-database
                    return (0, knex_1.default)(be_database_1.centralizedConfig);
                },
            }
        ],
        exports: [chat_service_1.ChatService, 'KnexConnection'],
    })
], BeChatModule);


/***/ }),
/* 29 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ChatController = void 0;
const tslib_1 = __webpack_require__(5);
const common_1 = __webpack_require__(1);
const swagger_1 = __webpack_require__(18);
const chat_service_1 = __webpack_require__(30);
const chat_dto_1 = __webpack_require__(33);
const be_auth_1 = __webpack_require__(6);
let ChatController = class ChatController {
    constructor(chatService) {
        this.chatService = chatService;
    }
    async getAllChats(req) {
        try {
            const chats = await this.chatService.getAllChats(req.user.id);
            return { success: true, chats };
        }
        catch (error) {
            return {
                success: false,
                error: {
                    code: 'FETCH_CHATS_ERROR',
                    message: error instanceof Error ? error.message : 'Unknown error'
                }
            };
        }
    }
    async getChatById(chatId, req) {
        try {
            const chat = await this.chatService.getChatById(chatId, req.user.id);
            return { success: true, chat };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                return {
                    success: false,
                    error: {
                        code: 'CHAT_NOT_FOUND',
                        message: error.message
                    }
                };
            }
            if (error instanceof common_1.UnauthorizedException) {
                return {
                    success: false,
                    error: {
                        code: 'UNAUTHORIZED',
                        message: error.message
                    }
                };
            }
            return {
                success: false,
                error: {
                    code: 'FETCH_CHAT_ERROR',
                    message: error instanceof Error ? error.message : 'Unknown error'
                }
            };
        }
    }
    async createChat(createChatDto, req) {
        try {
            const chat = await this.chatService.createChat(createChatDto, req.user.id);
            return { success: true, chat };
        }
        catch (error) {
            return {
                success: false,
                error: {
                    code: 'CREATE_CHAT_ERROR',
                    message: error instanceof Error ? error.message : 'Unknown error'
                }
            };
        }
    }
    async updateChat(chatId, updateChatDto, req) {
        try {
            const chat = await this.chatService.updateChat(chatId, updateChatDto, req.user.id);
            return { success: true, chat };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                return {
                    success: false,
                    error: {
                        code: 'CHAT_NOT_FOUND',
                        message: error.message
                    }
                };
            }
            if (error instanceof common_1.UnauthorizedException) {
                return {
                    success: false,
                    error: {
                        code: 'UNAUTHORIZED',
                        message: error.message
                    }
                };
            }
            return {
                success: false,
                error: {
                    code: 'UPDATE_CHAT_ERROR',
                    message: error instanceof Error ? error.message : 'Unknown error'
                }
            };
        }
    }
    async deleteChat(chatId, req) {
        try {
            const result = await this.chatService.deleteChat(chatId, req.user.id);
            return { success: true, message: result.message };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                return {
                    success: false,
                    error: {
                        code: 'CHAT_NOT_FOUND',
                        message: error.message
                    }
                };
            }
            if (error instanceof common_1.UnauthorizedException) {
                return {
                    success: false,
                    error: {
                        code: 'UNAUTHORIZED',
                        message: error.message
                    }
                };
            }
            return {
                success: false,
                error: {
                    code: 'DELETE_CHAT_ERROR',
                    message: error instanceof Error ? error.message : 'Unknown error'
                }
            };
        }
    }
    async addMessage(chatId, addMessageDto, req) {
        try {
            const result = await this.chatService.addMessage(chatId, addMessageDto, req.user.id);
            return { success: true, message: result.message, chat: result.chat };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                return {
                    success: false,
                    error: {
                        code: 'CHAT_NOT_FOUND',
                        message: error.message
                    }
                };
            }
            if (error instanceof common_1.UnauthorizedException) {
                return {
                    success: false,
                    error: {
                        code: 'UNAUTHORIZED',
                        message: error.message
                    }
                };
            }
            return {
                success: false,
                error: {
                    code: 'ADD_MESSAGE_ERROR',
                    message: error instanceof Error ? error.message : 'Unknown error'
                }
            };
        }
    }
};
exports.ChatController = ChatController;
tslib_1.__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all chats for the authenticated user' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Returns all chats with basic metadata'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized'
    }),
    tslib_1.__param(0, (0, common_1.Request)()),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], ChatController.prototype, "getAllChats", null);
tslib_1.__decorate([
    (0, common_1.Get)(':chatId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a chat by ID' }),
    (0, swagger_1.ApiParam)({ name: 'chatId', description: 'The ID of the chat' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Returns the complete chat with all messages and related data'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Chat not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized'
    }),
    tslib_1.__param(0, (0, common_1.Param)('chatId')),
    tslib_1.__param(1, (0, common_1.Request)()),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], ChatController.prototype, "getChatById", null);
tslib_1.__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new chat' }),
    (0, swagger_1.ApiBody)({ type: chat_dto_1.CreateChatDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Chat created successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized'
    }),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    tslib_1.__param(0, (0, common_1.Body)()),
    tslib_1.__param(1, (0, common_1.Request)()),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [typeof (_b = typeof chat_dto_1.CreateChatDto !== "undefined" && chat_dto_1.CreateChatDto) === "function" ? _b : Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], ChatController.prototype, "createChat", null);
tslib_1.__decorate([
    (0, common_1.Put)(':chatId'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a chat' }),
    (0, swagger_1.ApiParam)({ name: 'chatId', description: 'The ID of the chat' }),
    (0, swagger_1.ApiBody)({ type: chat_dto_1.UpdateChatDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Chat updated successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Chat not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input'
    }),
    tslib_1.__param(0, (0, common_1.Param)('chatId')),
    tslib_1.__param(1, (0, common_1.Body)()),
    tslib_1.__param(2, (0, common_1.Request)()),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, typeof (_c = typeof chat_dto_1.UpdateChatDto !== "undefined" && chat_dto_1.UpdateChatDto) === "function" ? _c : Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], ChatController.prototype, "updateChat", null);
tslib_1.__decorate([
    (0, common_1.Delete)(':chatId'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a chat' }),
    (0, swagger_1.ApiParam)({ name: 'chatId', description: 'The ID of the chat' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Chat deleted successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Chat not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized'
    }),
    tslib_1.__param(0, (0, common_1.Param)('chatId')),
    tslib_1.__param(1, (0, common_1.Request)()),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], ChatController.prototype, "deleteChat", null);
tslib_1.__decorate([
    (0, common_1.Post)(':chatId/messages'),
    (0, swagger_1.ApiOperation)({ summary: 'Add a message to a chat' }),
    (0, swagger_1.ApiParam)({ name: 'chatId', description: 'The ID of the chat' }),
    (0, swagger_1.ApiBody)({ type: chat_dto_1.AddMessageDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Message added successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Chat not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input'
    }),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    tslib_1.__param(0, (0, common_1.Param)('chatId')),
    tslib_1.__param(1, (0, common_1.Body)()),
    tslib_1.__param(2, (0, common_1.Request)()),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, typeof (_d = typeof chat_dto_1.AddMessageDto !== "undefined" && chat_dto_1.AddMessageDto) === "function" ? _d : Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], ChatController.prototype, "addMessage", null);
exports.ChatController = ChatController = tslib_1.__decorate([
    (0, swagger_1.ApiTags)('Chats'),
    (0, common_1.Controller)('chats'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(be_auth_1.JwtAuthGuard),
    tslib_1.__metadata("design:paramtypes", [typeof (_a = typeof chat_service_1.ChatService !== "undefined" && chat_service_1.ChatService) === "function" ? _a : Object])
], ChatController);


/***/ }),
/* 30 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ChatService = void 0;
const tslib_1 = __webpack_require__(5);
const common_1 = __webpack_require__(1);
const chat_repository_1 = __webpack_require__(31);
let ChatService = class ChatService {
    constructor(chatRepository) {
        this.chatRepository = chatRepository;
    }
    /**
     * Get all chats for a user
     * @param userId The ID of the user
     * @returns Array of chats with basic metadata
     */
    async getAllChats(userId) {
        return this.chatRepository.findAllChats(userId);
    }
    /**
     * Get a chat by its ID
     * @param chatId The ID of the chat
     * @param userId The ID of the user (for authorization)
     * @returns The complete chat with all messages and related data
     */
    async getChatById(chatId, userId) {
        const chat = await this.chatRepository.findChatById(chatId);
        // Ensure the user owns this chat
        if (chat.userId !== userId) {
            throw new common_1.UnauthorizedException('You do not have permission to access this chat');
        }
        return chat;
    }
    /**
     * Create a new chat
     * @param createChatDto The chat data to create
     * @param userId The ID of the user who owns the chat
     * @returns The created chat
     */
    async createChat(createChatDto, userId) {
        const chatData = {
            ...createChatDto,
            userId,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        return this.chatRepository.createChat(chatData);
    }
    /**
     * Update an existing chat
     * @param chatId The ID of the chat to update
     * @param updateChatDto The chat data to update
     * @param userId The ID of the user (for authorization)
     * @returns The updated chat
     */
    async updateChat(chatId, updateChatDto, userId) {
        // Verify ownership
        await this.verifyOwnership(chatId, userId);
        return this.chatRepository.updateChat(chatId, updateChatDto);
    }
    /**
     * Delete a chat
     * @param chatId The ID of the chat to delete
     * @param userId The ID of the user (for authorization)
     * @returns Success message
     */
    async deleteChat(chatId, userId) {
        // Verify ownership
        await this.verifyOwnership(chatId, userId);
        await this.chatRepository.deleteChat(chatId);
        return { message: 'Chat deleted successfully' };
    }
    /**
     * Add a message to a chat
     * @param chatId The ID of the chat
     * @param addMessageDto The message to add
     * @param userId The ID of the user (for authorization)
     * @returns The added message and updated chat
     */
    async addMessage(chatId, addMessageDto, userId) {
        // Verify ownership
        await this.verifyOwnership(chatId, userId);
        const timestamp = addMessageDto.timestamp || new Date().toISOString();
        return this.chatRepository.addMessage(chatId, {
            ...addMessageDto,
            timestamp
        });
    }
    /**
     * Verify that a user owns a chat
     * @param chatId The ID of the chat
     * @param userId The ID of the user
     * @throws UnauthorizedException if the user doesn't own the chat
     */
    async verifyOwnership(chatId, userId) {
        try {
            const chat = await this.chatRepository.findChatById(chatId);
            if (chat.userId !== userId) {
                throw new common_1.UnauthorizedException('You do not have permission to modify this chat');
            }
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            if (error instanceof common_1.UnauthorizedException) {
                throw error;
            }
            throw new Error('Failed to verify chat ownership');
        }
    }
};
exports.ChatService = ChatService;
exports.ChatService = ChatService = tslib_1.__decorate([
    (0, common_1.Injectable)(),
    tslib_1.__metadata("design:paramtypes", [typeof (_a = typeof chat_repository_1.ChatRepository !== "undefined" && chat_repository_1.ChatRepository) === "function" ? _a : Object])
], ChatService);


/***/ }),
/* 31 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ChatRepository = void 0;
const tslib_1 = __webpack_require__(5);
const common_1 = __webpack_require__(1);
const knex_1 = __webpack_require__(12);
const uuid_1 = __webpack_require__(32);
let ChatRepository = class ChatRepository {
    constructor(knex) {
        this.knex = knex;
    }
    /**
     * Find all chats for a specific user
     * @param userId The ID of the user
     * @returns Array of chats with basic metadata (no full messages)
     */
    async findAllChats(userId) {
        // Get all chats with basic metadata, excluding full message content
        const chats = await this.knex('chats')
            .select('id', 'title', 'created_at as createdAt', 'updated_at as updatedAt', 'flow_chat_id as flowChatId', 'session_id as sessionId', this.knex.raw('(select count(*) from messages where chat_id = chats.id) as messageCount'))
            .where({ user_id: userId })
            .orderBy('updated_at', 'desc');
        // For each chat, get the last message for preview
        for (const chat of chats) {
            const lastMessage = await this.knex('messages')
                .select('content', 'timestamp', 'role')
                .where({ chat_id: chat.id })
                .orderBy('timestamp', 'desc')
                .first();
            chat.lastMessage = lastMessage;
        }
        return chats;
    }
    /**
     * Find a chat by its ID
     * @param chatId The ID of the chat
     * @returns The complete chat with all messages and related data
     */
    async findChatById(chatId) {
        // Get the chat
        const chat = await this.knex('chats')
            .select('id', 'title', 'created_at as createdAt', 'updated_at as updatedAt', 'flow_chat_id as flowChatId', 'session_id as sessionId', 'user_id as userId')
            .where({ id: chatId })
            .first();
        if (!chat) {
            throw new common_1.NotFoundException(`Chat with ID ${chatId} not found`);
        }
        // Get all messages for the chat
        chat.messages = await this.knex('messages')
            .select('id', 'role', 'content', 'timestamp', 'is_complete as isComplete', 'thinking')
            .where({ chat_id: chatId })
            .orderBy('timestamp', 'asc');
        // Get reasoning steps if any
        chat.reasoningSteps = await this.knex('reasoning_steps')
            .select('*')
            .where({ chat_id: chatId })
            .orderBy('id', 'asc');
        // Get source documents if any
        chat.sourceDocuments = await this.knex('source_documents')
            .select('*')
            .where({ chat_id: chatId });
        return chat;
    }
    /**
     * Create a new chat
     * @param chat The chat data to create
     * @returns The created chat
     */
    async createChat(chat) {
        const now = new Date().toISOString();
        const id = (0, uuid_1.v4)();
        // Insert the chat
        await this.knex('chats').insert({
            id,
            title: chat.title,
            created_at: now,
            updated_at: now,
            flow_chat_id: chat.flowChatId,
            session_id: chat.sessionId,
            user_id: chat.userId
        });
        // Insert messages if any
        if (chat.messages && chat.messages.length > 0) {
            const messagesToInsert = chat.messages.map(message => ({
                id: (0, uuid_1.v4)(),
                chat_id: id,
                role: message.role,
                content: message.content,
                timestamp: message.timestamp || now,
                is_complete: message.isComplete,
                thinking: message.thinking
            }));
            await this.knex('messages').insert(messagesToInsert);
        }
        // Insert reasoning steps if any
        if (chat.reasoningSteps && chat.reasoningSteps.length > 0) {
            const stepsToInsert = chat.reasoningSteps.map((step, index) => {
                // Create a content field that captures all the important information
                let content = '';
                if (step.content) {
                    content = step.content;
                }
                else if (step.thought) {
                    content = step.thought;
                }
                else if (step.instructions) {
                    content = step.instructions;
                }
                else {
                    // Store all fields as JSON if no direct content is available
                    content = JSON.stringify({
                        agentName: step.agentName,
                        messages: step.messages,
                        next: step.next,
                        instructions: step.instructions,
                        usedTools: step.usedTools,
                        sourceDocuments: step.sourceDocuments,
                        artifacts: step.artifacts,
                        nodeId: step.nodeId,
                        thought: step.thought,
                        action: step.action,
                        observation: step.observation
                    });
                }
                return {
                    id: step.id || (0, uuid_1.v4)(),
                    chat_id: id,
                    step_number: step.stepNumber || index + 1,
                    content: content,
                    timestamp: now
                };
            });
            await this.knex('reasoning_steps').insert(stepsToInsert);
        }
        // Insert source documents if any
        if (chat.sourceDocuments && chat.sourceDocuments.length > 0) {
            const docsToInsert = chat.sourceDocuments.map(doc => ({
                id: doc.id || (0, uuid_1.v4)(),
                chat_id: id,
                page_content: doc.pageContent,
                metadata: JSON.stringify(doc.metadata)
            }));
            await this.knex('source_documents').insert(docsToInsert);
        }
        // Return the created chat
        return this.findChatById(id);
    }
    /**
     * Update an existing chat
     * @param chatId The ID of the chat to update
     * @param chat The chat data to update
     * @returns The updated chat
     */
    async updateChat(chatId, chat) {
        const now = new Date().toISOString();
        // Check if chat exists
        const existingChat = await this.knex('chats')
            .select('id')
            .where({ id: chatId })
            .first();
        if (!existingChat) {
            throw new common_1.NotFoundException(`Chat with ID ${chatId} not found`);
        }
        // Update chat basic info
        const updateData = {
            updated_at: now
        };
        if (chat.title)
            updateData['title'] = chat.title;
        if (chat.flowChatId)
            updateData['flow_chat_id'] = chat.flowChatId;
        if (chat.sessionId)
            updateData['session_id'] = chat.sessionId;
        await this.knex('chats')
            .where({ id: chatId })
            .update(updateData);
        // Update messages if provided
        if (chat.messages && chat.messages.length > 0) {
            // Delete existing messages
            await this.knex('messages')
                .where({ chat_id: chatId })
                .delete();
            // Insert new messages
            const messagesToInsert = chat.messages.map(message => ({
                id: message.id || (0, uuid_1.v4)(),
                chat_id: chatId,
                role: message.role,
                content: message.content,
                timestamp: message.timestamp || now,
                is_complete: message.isComplete,
                thinking: message.thinking
            }));
            await this.knex('messages').insert(messagesToInsert);
        }
        // Update reasoning steps if provided
        if (chat.reasoningSteps && chat.reasoningSteps.length > 0) {
            // Delete existing reasoning steps
            await this.knex('reasoning_steps')
                .where({ chat_id: chatId })
                .delete();
            // Insert new reasoning steps
            const stepsToInsert = chat.reasoningSteps.map((step, index) => ({
                id: step.id || (0, uuid_1.v4)(),
                chat_id: chatId,
                step_number: step.stepNumber || index + 1,
                content: step.content || step.thought || step.instructions || JSON.stringify({
                    agentName: step.agentName,
                    messages: step.messages,
                    next: step.next,
                    instructions: step.instructions,
                    usedTools: step.usedTools,
                    sourceDocuments: step.sourceDocuments,
                    artifacts: step.artifacts,
                    nodeId: step.nodeId,
                    thought: step.thought,
                    action: step.action,
                    observation: step.observation
                }),
                timestamp: now
            }));
            await this.knex('reasoning_steps').insert(stepsToInsert);
        }
        // Update source documents if provided
        if (chat.sourceDocuments && chat.sourceDocuments.length > 0) {
            // Delete existing source documents
            await this.knex('source_documents')
                .where({ chat_id: chatId })
                .delete();
            // Insert new source documents
            const docsToInsert = chat.sourceDocuments.map(doc => ({
                id: doc.id || (0, uuid_1.v4)(),
                chat_id: chatId,
                page_content: doc.pageContent,
                metadata: JSON.stringify(doc.metadata)
            }));
            await this.knex('source_documents').insert(docsToInsert);
        }
        // Return the updated chat
        return this.findChatById(chatId);
    }
    /**
     * Delete a chat
     * @param chatId The ID of the chat to delete
     * @returns True if successful
     */
    async deleteChat(chatId) {
        try {
            console.log(`Starting deletion of chat with ID: ${chatId}`);
            // Check if chat exists
            const existingChat = await this.knex('chats')
                .select('id', 'user_id as userId')
                .where({ id: chatId })
                .first();
            if (!existingChat) {
                console.log(`Chat with ID ${chatId} not found`);
                throw new common_1.NotFoundException(`Chat with ID ${chatId} not found`);
            }
            console.log(`Found chat with ID: ${chatId}, user ID: ${existingChat.userId}, proceeding with deletion`);
            // Count related records before deletion for verification using raw queries
            const messageCountQuery = await this.knex.raw('SELECT COUNT(*) as count FROM messages WHERE chat_id = ?', [chatId]);
            const stepCountQuery = await this.knex.raw('SELECT COUNT(*) as count FROM reasoning_steps WHERE chat_id = ?', [chatId]);
            const docCountQuery = await this.knex.raw('SELECT COUNT(*) as count FROM source_documents WHERE chat_id = ?', [chatId]);
            // Extract count values directly from raw query results (PostgreSQL specific)
            const messageCountValue = parseInt(messageCountQuery.rows[0].count, 10);
            const stepCountValue = parseInt(stepCountQuery.rows[0].count, 10);
            const docCountValue = parseInt(docCountQuery.rows[0].count, 10);
            console.log(`Related records for chat ${chatId}: messages=${messageCountValue}, steps=${stepCountValue}, docs=${docCountValue}`);
            // First attempt: Use a transaction with explicit deletions
            try {
                await this.knex.transaction(async (trx) => {
                    // Delete related data first (foreign key constraints)
                    const deletedMessages = await trx('messages').where({ chat_id: chatId }).delete();
                    console.log(`Deleted ${deletedMessages} messages for chat ${chatId}`);
                    const deletedSteps = await trx('reasoning_steps').where({ chat_id: chatId }).delete();
                    console.log(`Deleted ${deletedSteps} reasoning steps for chat ${chatId}`);
                    const deletedDocs = await trx('source_documents').where({ chat_id: chatId }).delete();
                    console.log(`Deleted ${deletedDocs} source documents for chat ${chatId}`);
                    // Delete the chat
                    const deletedChat = await trx('chats').where({ id: chatId }).delete();
                    console.log(`Deleted chat ${chatId}: ${deletedChat} rows affected`);
                    if (deletedChat === 0) {
                        throw new Error(`Failed to delete chat ${chatId} in transaction`);
                    }
                });
            }
            catch (trxError) {
                console.error(`Transaction-based deletion failed for chat ${chatId}:`, trxError);
                console.log(`Attempting alternative deletion approach for chat ${chatId}...`);
                // Second attempt: Try direct deletion with CASCADE relying on foreign key constraints
                try {
                    // Directly delete the chat and rely on CASCADE constraints
                    const deletedChat = await this.knex('chats').where({ id: chatId }).delete();
                    console.log(`Direct deletion of chat ${chatId}: ${deletedChat} rows affected`);
                    if (deletedChat === 0) {
                        throw new Error(`Failed to delete chat ${chatId} with direct deletion`);
                    }
                }
                catch (directError) {
                    console.error(`Direct deletion failed for chat ${chatId}:`, directError);
                    // Third attempt: Try raw SQL with CASCADE
                    try {
                        console.log(`Attempting raw SQL deletion for chat ${chatId}...`);
                        await this.knex.raw(`DELETE FROM chats WHERE id = ?`, [chatId]);
                        console.log(`Raw SQL deletion completed for chat ${chatId}`);
                    }
                    catch (rawError) {
                        console.error(`Raw SQL deletion failed for chat ${chatId}:`, rawError);
                        throw new Error(`All deletion methods failed for chat ${chatId}`);
                    }
                }
            }
            // Verify deletion
            const chatStillExists = await this.knex('chats')
                .select('id')
                .where({ id: chatId })
                .first();
            if (chatStillExists) {
                console.error(`CRITICAL ERROR: Chat ${chatId} still exists after deletion!`);
                // Log database state for debugging
                const dbState = await this.knex.raw(`
          SELECT 
            (SELECT COUNT(*) FROM chats WHERE id = ?) as chat_count,
            (SELECT COUNT(*) FROM messages WHERE chat_id = ?) as message_count,
            (SELECT COUNT(*) FROM reasoning_steps WHERE chat_id = ?) as step_count,
            (SELECT COUNT(*) FROM source_documents WHERE chat_id = ?) as doc_count
        `, [chatId, chatId, chatId, chatId]);
                console.error(`Database state for chat ${chatId}:`, dbState.rows[0]);
                return false;
            }
            console.log(`Successfully deleted chat with ID: ${chatId}`);
            return true;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error('Error deleting chat:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            // Log more detailed error information
            if (error instanceof Error && error.stack) {
                console.error('Error stack:', error.stack);
            }
            throw new Error(`Failed to delete chat: ${errorMessage}`);
        }
    }
    /**
     * Add a message to a chat
     * @param chatId The ID of the chat
     * @param message The message to add
     * @returns The added message and updated chat
     */
    async addMessage(chatId, message) {
        const now = new Date().toISOString();
        // Check if chat exists
        const existingChat = await this.knex('chats')
            .select('id')
            .where({ id: chatId })
            .first();
        if (!existingChat) {
            throw new common_1.NotFoundException(`Chat with ID ${chatId} not found`);
        }
        // Create message
        const messageId = (0, uuid_1.v4)();
        const newMessage = {
            id: messageId,
            chat_id: chatId,
            role: message.role,
            content: message.content,
            timestamp: message.timestamp || now,
            is_complete: message.isComplete,
            thinking: message.thinking
        };
        await this.knex('messages').insert(newMessage);
        // Add reasoning steps if provided
        if (message.reasoningSteps && message.reasoningSteps.length > 0) {
            const stepsToInsert = message.reasoningSteps.map((step, index) => ({
                id: step.id || (0, uuid_1.v4)(),
                chat_id: chatId,
                step_number: step.stepNumber || index + 1,
                content: step.content || step.thought || '',
                timestamp: now
            }));
            await this.knex('reasoning_steps').insert(stepsToInsert);
        }
        // Update chat's updated_at timestamp
        await this.knex('chats')
            .where({ id: chatId })
            .update({ updated_at: now });
        // Get the created message
        const createdMessage = await this.knex('messages')
            .select('id', 'role', 'content', 'timestamp', 'is_complete as isComplete', 'thinking')
            .where({ id: messageId })
            .first();
        // Get updated chat (just basic info)
        const updatedChat = await this.knex('chats')
            .select('id', 'updated_at as updatedAt')
            .where({ id: chatId })
            .first();
        return {
            message: createdMessage,
            chat: updatedChat
        };
    }
};
exports.ChatRepository = ChatRepository;
exports.ChatRepository = ChatRepository = tslib_1.__decorate([
    (0, common_1.Injectable)(),
    tslib_1.__param(0, (0, common_1.Inject)('KnexConnection')),
    tslib_1.__metadata("design:paramtypes", [typeof (_a = typeof knex_1.Knex !== "undefined" && knex_1.Knex) === "function" ? _a : Object])
], ChatRepository);


/***/ }),
/* 32 */
/***/ ((module) => {

module.exports = require("uuid");

/***/ }),
/* 33 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AddMessageDto = exports.UpdateChatDto = exports.CreateChatDto = exports.AddMessageSchema = exports.UpdateChatSchema = exports.CreateChatSchema = void 0;
const zod_1 = __webpack_require__(14);
const zod_nestjs_1 = __webpack_require__(17);
const message_model_1 = __webpack_require__(34);
const agent_reasoning_step_model_1 = __webpack_require__(35);
const source_document_model_1 = __webpack_require__(36);
// ✅ Define Zod Schemas for DTOs
exports.CreateChatSchema = zod_1.z.object({
    title: zod_1.z.string().min(1, 'Title is required'),
    messages: zod_1.z.array(message_model_1.MessageModel).default([]),
    flowChatId: zod_1.z.string().optional(),
    sessionId: zod_1.z.string().optional(),
});
exports.UpdateChatSchema = zod_1.z.object({
    title: zod_1.z.string().min(1, 'Title is required').optional(),
    messages: zod_1.z.array(message_model_1.MessageModel).optional(),
    flowChatId: zod_1.z.string().optional(),
    sessionId: zod_1.z.string().optional(),
    reasoningSteps: zod_1.z.array(agent_reasoning_step_model_1.AgentReasoningStepModel).optional(),
    sourceDocuments: zod_1.z.array(source_document_model_1.SourceDocumentModel).optional(),
});
exports.AddMessageSchema = zod_1.z.object({
    role: zod_1.z.enum(['user', 'assistant']),
    content: zod_1.z.string().min(1, 'Content is required'),
    timestamp: zod_1.z.string().datetime().optional(),
    isComplete: zod_1.z.boolean().default(true),
    thinking: zod_1.z.string().optional(),
    reasoningSteps: zod_1.z.array(agent_reasoning_step_model_1.AgentReasoningStepModel).optional(),
});
// ✅ Convert Zod Schemas into NestJS DTOs for Validation
class CreateChatDto extends (0, zod_nestjs_1.createZodDto)(exports.CreateChatSchema) {
}
exports.CreateChatDto = CreateChatDto;
class UpdateChatDto extends (0, zod_nestjs_1.createZodDto)(exports.UpdateChatSchema) {
}
exports.UpdateChatDto = UpdateChatDto;
class AddMessageDto extends (0, zod_nestjs_1.createZodDto)(exports.AddMessageSchema) {
}
exports.AddMessageDto = AddMessageDto;


/***/ }),
/* 34 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.MessageModel = void 0;
const zod_1 = __webpack_require__(14);
const agent_reasoning_step_model_1 = __webpack_require__(35);
/**
 * Message model schema definition using Zod
 * Based on the API specification
 */
exports.MessageModel = zod_1.z.object({
    id: zod_1.z.string().uuid().optional(),
    role: zod_1.z.enum(['user', 'assistant']),
    content: zod_1.z.string(),
    timestamp: zod_1.z.string().datetime().optional(),
    isComplete: zod_1.z.boolean().default(true),
    thinking: zod_1.z.string().optional(),
    reasoningSteps: zod_1.z.array(agent_reasoning_step_model_1.AgentReasoningStepModel).optional(),
});


/***/ }),
/* 35 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AgentReasoningStepModel = void 0;
const zod_1 = __webpack_require__(14);
/**
 * AgentReasoningStep model schema definition using Zod
 * Based on the API specification
 */
exports.AgentReasoningStepModel = zod_1.z.object({
    id: zod_1.z.string().uuid().optional(),
    stepNumber: zod_1.z.number().int().positive().optional(),
    content: zod_1.z.string().optional(),
    agentName: zod_1.z.string().optional(),
    messages: zod_1.z.array(zod_1.z.string()).optional(),
    next: zod_1.z.string().optional(),
    instructions: zod_1.z.string().optional(),
    usedTools: zod_1.z.array(zod_1.z.unknown()).optional(),
    sourceDocuments: zod_1.z.array(zod_1.z.unknown()).optional(),
    artifacts: zod_1.z.array(zod_1.z.unknown()).optional(),
    nodeId: zod_1.z.string().optional(),
    thought: zod_1.z.string().optional(),
    action: zod_1.z.string().optional(),
    observation: zod_1.z.string().optional(),
});


/***/ }),
/* 36 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.SourceDocumentModel = void 0;
const zod_1 = __webpack_require__(14);
/**
 * SourceDocument model schema definition using Zod
 * Based on the API specification
 */
exports.SourceDocumentModel = zod_1.z.object({
    id: zod_1.z.string(),
    pageContent: zod_1.z.string(),
    metadata: zod_1.z.object({
        source: zod_1.z.string(),
        blobType: zod_1.z.string(),
        pdf: zod_1.z.object({
        // PDF metadata fields
        // Using a more flexible approach since the exact fields aren't specified
        }).optional(),
        loc: zod_1.z.object({
            lines: zod_1.z.object({
                from: zod_1.z.number(),
                to: zod_1.z.number(),
            }),
        }).optional(),
    }).catchall(zod_1.z.unknown()), // Allow additional metadata fields
});


/***/ }),
/* 37 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ChatModel = void 0;
const zod_1 = __webpack_require__(14);
const message_model_1 = __webpack_require__(34);
const agent_reasoning_step_model_1 = __webpack_require__(35);
const source_document_model_1 = __webpack_require__(36);
/**
 * Chat model schema definition using Zod
 * Based on the API specification
 */
exports.ChatModel = zod_1.z.object({
    id: zod_1.z.string().uuid().optional(),
    title: zod_1.z.string(),
    messages: zod_1.z.array(zod_1.z.lazy(() => message_model_1.MessageModel)).default([]),
    createdAt: zod_1.z.string().datetime().optional(),
    updatedAt: zod_1.z.string().datetime().optional(),
    flowChatId: zod_1.z.string().optional(),
    sessionId: zod_1.z.string().optional(),
    reasoningSteps: zod_1.z.array(zod_1.z.lazy(() => agent_reasoning_step_model_1.AgentReasoningStepModel)).optional(),
    sourceDocuments: zod_1.z.array(zod_1.z.lazy(() => source_document_model_1.SourceDocumentModel)).optional(),
    userId: zod_1.z.string(),
});


/***/ })
/******/ 	]);
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
var exports = __webpack_exports__;

/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */
Object.defineProperty(exports, "__esModule", ({ value: true }));
const common_1 = __webpack_require__(1);
const core_1 = __webpack_require__(2);
const express_1 = __webpack_require__(3);
const app_module_1 = __webpack_require__(4);
const swagger_1 = __webpack_require__(18);
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule, { cors: true });
    // Configure body parser to accept larger payloads (50MB)
    app.use((0, express_1.json)({ limit: '50mb' }));
    app.use((0, express_1.urlencoded)({ limit: '50mb', extended: true }));
    // Enable CORS with a more permissive configuration
    app.enableCors({
        origin: true, // Allow all origins
        methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
        credentials: true,
        allowedHeaders: 'Origin,X-Requested-With,Content-Type,Accept,Authorization,Access-Control-Allow-Origin',
        preflightContinue: false,
        optionsSuccessStatus: 204
    });
    const globalPrefix = 'api';
    app.setGlobalPrefix(globalPrefix);
    // Setup Swagger
    const config = new swagger_1.DocumentBuilder()
        .setTitle('Psychology Chat API')
        .setDescription('API Documentation for Psychology Chat')
        .setVersion('1.0')
        .addBearerAuth() // Enables JWT token authentication
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('docs', app, document);
    const port = process.env.PORT || 3003;
    await app.listen(port, '0.0.0.0'); // Listen on all network interfaces
    common_1.Logger.log(`🚀 Application is running on: http://localhost:${port}/${globalPrefix}`);
    common_1.Logger.log(`📚 Swagger documentation available at: http://localhost:${port}/docs`);
}
bootstrap();

})();

/******/ })()
;