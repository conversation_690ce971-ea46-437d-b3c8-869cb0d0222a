import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  UseGuards, 
  Request,
  HttpStatus,
  HttpCode,
  NotFoundException,
  UnauthorizedException
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth, 
  ApiParam, 
  ApiBody 
} from '@nestjs/swagger';
import { ChatService } from '../services/chat.service';
import { CreateChatDto, UpdateChatDto, AddMessageDto } from '../dtos/chat.dto';
import { JwtAuthGuard } from '@experiments/be-auth';

// Define the request type with user property
interface RequestWithUser extends Request {
  user: {
    id: string;
    email: string;
  };
}

@ApiTags('Chats')
@Controller('chats')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
export class ChatController {
  constructor(private chatService: ChatService) {}

  @Get()
  @ApiOperation({ summary: 'Get all chats for the authenticated user' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Returns all chats with basic metadata' 
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Unauthorized' 
  })
  async getAllChats(@Request() req: RequestWithUser) {
    try {
      const chats = await this.chatService.getAllChats(req.user.id);
      return { success: true, chats };
    } catch (error: unknown) {
      return { 
        success: false, 
        error: { 
          code: 'FETCH_CHATS_ERROR', 
          message: error instanceof Error ? error.message : 'Unknown error' 
        } 
      };
    }
  }

  @Get(':chatId')
  @ApiOperation({ summary: 'Get a chat by ID' })
  @ApiParam({ name: 'chatId', description: 'The ID of the chat' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Returns the complete chat with all messages and related data' 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Chat not found' 
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Unauthorized' 
  })
  async getChatById(@Param('chatId') chatId: string, @Request() req: RequestWithUser) {
    try {
      const chat = await this.chatService.getChatById(chatId, req.user.id);
      return { success: true, chat };
    } catch (error: unknown) {
      if (error instanceof NotFoundException) {
        return { 
          success: false, 
          error: { 
            code: 'CHAT_NOT_FOUND', 
            message: error.message 
          } 
        };
      }
      if (error instanceof UnauthorizedException) {
        return { 
          success: false, 
          error: { 
            code: 'UNAUTHORIZED', 
            message: error.message 
          } 
        };
      }
      return { 
        success: false, 
        error: { 
          code: 'FETCH_CHAT_ERROR', 
          message: error instanceof Error ? error.message : 'Unknown error' 
        } 
      };
    }
  }

  @Post()
  @ApiOperation({ summary: 'Create a new chat' })
  @ApiBody({ type: CreateChatDto })
  @ApiResponse({ 
    status: HttpStatus.CREATED, 
    description: 'Chat created successfully' 
  })
  @ApiResponse({ 
    status: HttpStatus.BAD_REQUEST, 
    description: 'Invalid input' 
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Unauthorized' 
  })
  @HttpCode(HttpStatus.CREATED)
  async createChat(@Body() createChatDto: CreateChatDto, @Request() req: RequestWithUser) {
    try {
      const chat = await this.chatService.createChat(createChatDto, req.user.id);
      return { success: true, chat };
    } catch (error: unknown) {
      return { 
        success: false, 
        error: { 
          code: 'CREATE_CHAT_ERROR', 
          message: error instanceof Error ? error.message : 'Unknown error' 
        } 
      };
    }
  }

  @Put(':chatId')
  @ApiOperation({ summary: 'Update a chat' })
  @ApiParam({ name: 'chatId', description: 'The ID of the chat' })
  @ApiBody({ type: UpdateChatDto })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Chat updated successfully' 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Chat not found' 
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Unauthorized' 
  })
  @ApiResponse({ 
    status: HttpStatus.BAD_REQUEST, 
    description: 'Invalid input' 
  })
  async updateChat(
    @Param('chatId') chatId: string, 
    @Body() updateChatDto: UpdateChatDto, 
    @Request() req: RequestWithUser
  ) {
    try {
      const chat = await this.chatService.updateChat(chatId, updateChatDto, req.user.id);
      return { success: true, chat };
    } catch (error: unknown) {
      if (error instanceof NotFoundException) {
        return { 
          success: false, 
          error: { 
            code: 'CHAT_NOT_FOUND', 
            message: error.message 
          } 
        };
      }
      if (error instanceof UnauthorizedException) {
        return { 
          success: false, 
          error: { 
            code: 'UNAUTHORIZED', 
            message: error.message 
          } 
        };
      }
      return { 
        success: false, 
        error: { 
          code: 'UPDATE_CHAT_ERROR', 
          message: error instanceof Error ? error.message : 'Unknown error' 
        } 
      };
    }
  }

  @Delete(':chatId')
  @ApiOperation({ summary: 'Delete a chat' })
  @ApiParam({ name: 'chatId', description: 'The ID of the chat' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Chat deleted successfully' 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Chat not found' 
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Unauthorized' 
  })
  async deleteChat(@Param('chatId') chatId: string, @Request() req: RequestWithUser) {
    try {
      const result = await this.chatService.deleteChat(chatId, req.user.id);
      return { success: true, message: result.message };
    } catch (error: unknown) {
      if (error instanceof NotFoundException) {
        return { 
          success: false, 
          error: { 
            code: 'CHAT_NOT_FOUND', 
            message: error.message 
          } 
        };
      }
      if (error instanceof UnauthorizedException) {
        return { 
          success: false, 
          error: { 
            code: 'UNAUTHORIZED', 
            message: error.message 
          } 
        };
      }
      return { 
        success: false, 
        error: { 
          code: 'DELETE_CHAT_ERROR', 
          message: error instanceof Error ? error.message : 'Unknown error' 
        } 
      };
    }
  }

  @Post(':chatId/messages')
  @ApiOperation({ summary: 'Add a message to a chat' })
  @ApiParam({ name: 'chatId', description: 'The ID of the chat' })
  @ApiBody({ type: AddMessageDto })
  @ApiResponse({ 
    status: HttpStatus.CREATED, 
    description: 'Message added successfully' 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Chat not found' 
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Unauthorized' 
  })
  @ApiResponse({ 
    status: HttpStatus.BAD_REQUEST, 
    description: 'Invalid input' 
  })
  @HttpCode(HttpStatus.CREATED)
  async addMessage(
    @Param('chatId') chatId: string, 
    @Body() addMessageDto: AddMessageDto, 
    @Request() req: RequestWithUser
  ) {
    try {
      const result = await this.chatService.addMessage(chatId, addMessageDto, req.user.id);
      return { success: true, message: result.message, chat: result.chat };
    } catch (error: unknown) {
      if (error instanceof NotFoundException) {
        return { 
          success: false, 
          error: { 
            code: 'CHAT_NOT_FOUND', 
            message: error.message 
          } 
        };
      }
      if (error instanceof UnauthorizedException) {
        return { 
          success: false, 
          error: { 
            code: 'UNAUTHORIZED', 
            message: error.message 
          } 
        };
      }
      return { 
        success: false, 
        error: { 
          code: 'ADD_MESSAGE_ERROR', 
          message: error instanceof Error ? error.message : 'Unknown error' 
        } 
      };
    }
  }
}
