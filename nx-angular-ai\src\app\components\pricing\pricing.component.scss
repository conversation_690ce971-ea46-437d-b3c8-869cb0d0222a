/* Pricing component styles */
.pricing-card {
  transition: all 0.3s ease;
}

.pricing-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.popular-badge {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

/* Toggle switch styles */
.billing-toggle {
  transition: all 0.3s ease;
}

/* Plan card animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.plan-card {
  animation: slideUp 0.6s ease-out;
}

.plan-card:nth-child(2) {
  animation-delay: 0.1s;
}

.plan-card:nth-child(3) {
  animation-delay: 0.2s;
}
