/* About page styles */
.team-member {
  transition: all 0.3s ease;
}

.team-member:hover {
  transform: translateY(-4px);
}

.value-card {
  transition: all 0.3s ease;
}

.value-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Stats animation */
@keyframes countUp {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.stat-number {
  animation: countUp 0.6s ease-out;
}
