# Azure OpenAI LLM

Azure OpenAI LLM integration for Flowise

## 🌱 Env Variables

| Variable                     | Description                                                                                     | Type                                             | Default                             |
| ---------------------------- | ----------------------------------------------------------------------------------------------- | ------------------------------------------------ | ----------------------------------- |
| AZURE_OPENAI_API_KEY    | Default `credential.azureOpenAIApiKey` for Azure OpenAI LLM                                            | String                                                                    |            |
| AZURE_OPENAI_API_INSTANCE_NAME    | Default `credential.azureOpenAIApiInstanceName` for Azure OpenAI LLM                                            | String                                                                    |            |
| AZURE_OPENAI_API_DEPLOYMENT_NAME    | Default `credential.azureOpenAIApiDeploymentName` for Azure OpenAI LLM                                            | String                                                                    |            |
| AZURE_OPENAI_API_VERSION    | Default `credential.azureOpenAIApiVersion` for Azure OpenAI LLM                                            | String                                                                    |            |

## License

Source code in this repository is made available under the [Apache License Version 2.0](https://github.com/FlowiseAI/Flowise/blob/master/LICENSE.md).