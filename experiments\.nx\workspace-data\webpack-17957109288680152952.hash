{"217693237325826283": {"targets": {"build": {"command": "webpack-cli build", "options": {"cwd": "apps/psychology-chat", "args": ["--node-env=production"]}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["webpack-cli"]}], "outputs": ["{workspaceRoot}/dist\\apps\\psychology-chat"], "metadata": {"technologies": ["webpack"], "description": "Runs Webpack build", "help": {"command": "npx webpack-cli build --help", "example": {"options": {"json": "stats.json"}, "args": ["--profile"]}}}}, "serve": {"command": "webpack-cli serve", "options": {"cwd": "apps/psychology-chat", "args": ["--node-env=development"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}}, "preview": {"command": "webpack-cli serve", "options": {"cwd": "apps/psychology-chat", "args": ["--node-env=production"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server in production mode", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}}, "serve-static": {"dependsOn": ["build"], "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"dependsOn": ["build-deps"], "command": "npx nx watch --projects psychology-chat --includeDependentProjects -- npx nx build-deps psychology-chat"}}, "metadata": {}}}