version: '3.1'

services:
    flowise:
        image: flowise-custom
        restart: always
        environment:
            - PORT=${PORT}
            - CORS_ORIGINS=${CORS_ORIGINS}
            - IFRAME_ORIGINS=${IFRAME_ORIGINS}
            - FLOWISE_USERNAME=${FLOWISE_USERNAME}
            - FLOWISE_PASSWORD=${FLOWISE_PASSWORD}
            - FLOWISE_FILE_SIZE_LIMIT=${FLOWISE_FILE_SIZE_LIMIT}
            - DEBUG=${DEBUG}
            - DATABASE_PATH=${DATABASE_PATH}
            - DATABASE_TYPE=${DATABASE_TYPE}
            - DATABASE_PORT=${DATABASE_PORT}
            - DATABASE_HOST=${DATABASE_HOST}
            - DATABASE_NAME=${DATABASE_NAME}
            - DATABASE_USER=${DATABASE_USER}
            - DATABASE_PASSWORD=${DATABASE_PASSWORD}
            - DATABASE_SSL=${DATABASE_SSL}
            - DATABASE_SSL_KEY_BASE64=${DATABASE_SSL_KEY_BASE64}
            - APIKEY_STORAGE_TYPE=${APIKEY_STORAGE_TYPE}
            - APIKEY_PATH=${APIKEY_PATH}
            - SECRETKEY_PATH=${SECRETKEY_PATH}
            - FLOWISE_SECRETKEY_OVERWRITE=${FLOWISE_SECRETKEY_OVERWRITE}
            - LOG_LEVEL=${LOG_LEVEL}
            - LOG_PATH=${LOG_PATH}
            - BLOB_STORAGE_PATH=${BLOB_STORAGE_PATH}
            - DISABLE_FLOWISE_TELEMETRY=${DISABLE_FLOWISE_TELEMETRY}
            - MODEL_LIST_CONFIG_JSON=${MODEL_LIST_CONFIG_JSON}
            - GLOBAL_AGENT_HTTP_PROXY=${GLOBAL_AGENT_HTTP_PROXY}
            - GLOBAL_AGENT_HTTPS_PROXY=${GLOBAL_AGENT_HTTPS_PROXY}
            - GLOBAL_AGENT_NO_PROXY=${GLOBAL_AGENT_NO_PROXY}
            - DISABLED_NODES=${DISABLED_NODES}
            - MODE=${MODE}
            - WORKER_CONCURRENCY=${WORKER_CONCURRENCY}
            - QUEUE_NAME=${QUEUE_NAME}
            - QUEUE_REDIS_EVENT_STREAM_MAX_LEN=${QUEUE_REDIS_EVENT_STREAM_MAX_LEN}
            - REMOVE_ON_AGE=${REMOVE_ON_AGE}
            - REMOVE_ON_COUNT=${REMOVE_ON_COUNT}
            - REDIS_URL=${REDIS_URL}
            - REDIS_HOST=${REDIS_HOST}
            - REDIS_PORT=${REDIS_PORT}
            - REDIS_PASSWORD=${REDIS_PASSWORD}
            - REDIS_USERNAME=${REDIS_USERNAME}
            - REDIS_TLS=${REDIS_TLS}
            - REDIS_CERT=${REDIS_CERT}
            - REDIS_KEY=${REDIS_KEY}
            - REDIS_CA=${REDIS_CA}
            - NODE_OPTIONS=--max-old-space-size=6144
        ports:
            - '${PORT}:${PORT}'
        healthcheck:
            test: ['CMD', 'curl', '-f', 'http://localhost:${PORT}/api/v1/ping']
            interval: 10s
            timeout: 5s
            retries: 5
            start_period: 30s
        volumes:
            - ~/.flowise:/root/.flowise
        entrypoint: /bin/sh -c "sleep 3; flowise start"
