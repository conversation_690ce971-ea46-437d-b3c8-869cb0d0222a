{"e2e": {"root": ["e2e/project.json", "nx/core/project-json"], "metadata.targetGroups": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "metadata.targetGroups.E2E (CI)": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "metadata.targetGroups.E2E (CI).0": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "metadata.targetGroups.E2E (CI).1": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.options": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.parallelism": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.cache": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.inputs": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.outputs": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.executor": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.options.cwd": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.options.command": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.technologies": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.technologies.0": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.description": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.help": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.help.command": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.help.example": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.options": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.parallelism": ["nx.json", "nx/target-defaults"], "targets.e2e-ci--src/example.spec.ts.metadata": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.cache": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.inputs": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.outputs": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.executor": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.options.cwd": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.options.env": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.options.command": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.technologies": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.technologies.0": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.description": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.help": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.help.command": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.help.example": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.executor": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.cache": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.inputs": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.outputs": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.dependsOn": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.parallelism": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.technologies": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.technologies.0": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.description": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.nonAtomizedTarget": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.help": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.help.command": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.help.example": ["e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.lint": ["e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.cache": ["e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options": ["e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.inputs": ["e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.outputs": ["e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata": ["e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.executor": ["e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.cwd": ["e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.command": ["e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies": ["e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies.0": ["e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.description": ["e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help": ["e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.command": ["e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.example": ["e2e/eslint.config.mjs", "@nx/eslint/plugin"], "name": ["e2e/project.json", "nx/core/project-json"], "$schema": ["e2e/project.json", "nx/core/project-json"], "projectType": ["e2e/project.json", "nx/core/project-json"], "sourceRoot": ["e2e/project.json", "nx/core/project-json"], "// targets": ["e2e/project.json", "nx/core/project-json"], "implicitDependencies": ["e2e/project.json", "nx/core/project-json"], "implicitDependencies.nx-angular-ai": ["e2e/project.json", "nx/core/project-json"], "targets.e2e-ci--src/example.spec.ts.dependsOn": ["nx.json", "nx/target-defaults"]}, ".": {"root": ["project.json", "nx/core/project-json"], "targets": ["eslint.config.mjs", "@nx/eslint/plugin"], "name": ["project.json", "nx/core/project-json"], "includedScripts": ["project.json", "nx/core/project-json"], "tags": ["package.json", "nx/core/package-json"], "tags.npm:private": ["package.json", "nx/core/package-json"], "metadata.targetGroups": ["package.json", "nx/core/package-json"], "metadata.js": ["package.json", "nx/core/package-json"], "metadata.js.packageName": ["package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["package.json", "nx/core/package-json"], "$schema": ["project.json", "nx/core/project-json"], "projectType": ["project.json", "nx/core/project-json"], "prefix": ["project.json", "nx/core/project-json"], "sourceRoot": ["project.json", "nx/core/project-json"], "targets.build": ["project.json", "nx/core/project-json"], "targets.build.executor": ["project.json", "nx/core/project-json"], "targets.build.outputs": ["project.json", "nx/core/project-json"], "targets.build.options": ["project.json", "nx/core/project-json"], "targets.build.configurations": ["project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["project.json", "nx/core/project-json"], "targets.build.options.outputPath": ["project.json", "nx/core/project-json"], "targets.build.options.index": ["project.json", "nx/core/project-json"], "targets.build.options.browser": ["project.json", "nx/core/project-json"], "targets.build.options.polyfills": ["project.json", "nx/core/project-json"], "targets.build.options.tsConfig": ["project.json", "nx/core/project-json"], "targets.build.options.inlineStyleLanguage": ["project.json", "nx/core/project-json"], "targets.build.options.assets": ["project.json", "nx/core/project-json"], "targets.build.options.styles": ["project.json", "nx/core/project-json"], "targets.build.options.scripts": ["project.json", "nx/core/project-json"], "targets.build.configurations.production": ["project.json", "nx/core/project-json"], "targets.build.configurations.production.budgets": ["project.json", "nx/core/project-json"], "targets.build.configurations.production.outputHashing": ["project.json", "nx/core/project-json"], "targets.build.configurations.development": ["project.json", "nx/core/project-json"], "targets.build.configurations.development.optimization": ["project.json", "nx/core/project-json"], "targets.build.configurations.development.extractLicenses": ["project.json", "nx/core/project-json"], "targets.build.configurations.development.sourceMap": ["project.json", "nx/core/project-json"], "targets.serve": ["project.json", "nx/core/project-json"], "targets.serve.executor": ["project.json", "nx/core/project-json"], "targets.serve.configurations": ["project.json", "nx/core/project-json"], "targets.serve.defaultConfiguration": ["project.json", "nx/core/project-json"], "targets.serve.configurations.production": ["project.json", "nx/core/project-json"], "targets.serve.configurations.production.buildTarget": ["project.json", "nx/core/project-json"], "targets.serve.configurations.development": ["project.json", "nx/core/project-json"], "targets.serve.configurations.development.buildTarget": ["project.json", "nx/core/project-json"], "targets.serve.configurations.development.proxyConfig": ["project.json", "nx/core/project-json"], "targets.extract-i18n": ["project.json", "nx/core/project-json"], "targets.extract-i18n.executor": ["project.json", "nx/core/project-json"], "targets.extract-i18n.options": ["project.json", "nx/core/project-json"], "targets.extract-i18n.options.buildTarget": ["project.json", "nx/core/project-json"], "targets.lint": ["project.json", "nx/core/project-json"], "targets.lint.executor": ["project.json", "nx/core/project-json"], "targets.lint.options": ["project.json", "nx/core/project-json"], "targets.lint.options.lintFilePatterns": ["project.json", "nx/core/project-json"], "targets.test": ["project.json", "nx/core/project-json"], "targets.test.executor": ["project.json", "nx/core/project-json"], "targets.test.outputs": ["project.json", "nx/core/project-json"], "targets.test.options": ["project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["project.json", "nx/core/project-json"], "targets.serve-static": ["project.json", "nx/core/project-json"], "targets.serve-static.executor": ["project.json", "nx/core/project-json"], "targets.serve-static.options": ["project.json", "nx/core/project-json"], "targets.serve-static.options.buildTarget": ["project.json", "nx/core/project-json"], "targets.serve-static.options.port": ["project.json", "nx/core/project-json"], "targets.serve-static.options.staticFilePath": ["project.json", "nx/core/project-json"], "targets.serve-static.options.spa": ["project.json", "nx/core/project-json"], "targets.lint.cache": ["nx.json", "nx/target-defaults"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.options.passWithNoTests": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.ci": ["nx.json", "nx/target-defaults"], "targets.test.configurations.ci.codeCoverage": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}}