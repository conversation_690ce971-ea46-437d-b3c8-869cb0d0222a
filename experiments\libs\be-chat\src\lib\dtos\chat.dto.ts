import { z } from 'zod';
import { createZodDto } from '@anatine/zod-nestjs';
import { MessageModel } from '../models/message.model';
import { AgentReasoningStepModel } from '../models/agent-reasoning-step.model';
import { SourceDocumentModel } from '../models/source-document.model';

// ✅ Define Zod Schemas for DTOs
export const CreateChatSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  messages: z.array(MessageModel).default([]),
  flowChatId: z.string().optional(),
  sessionId: z.string().optional(),
});

export const UpdateChatSchema = z.object({
  title: z.string().min(1, 'Title is required').optional(),
  messages: z.array(MessageModel).optional(),
  flowChatId: z.string().optional(),
  sessionId: z.string().optional(),
  reasoningSteps: z.array(AgentReasoningStepModel).optional(),
  sourceDocuments: z.array(SourceDocumentModel).optional(),
});

export const AddMessageSchema = z.object({
  role: z.enum(['user', 'assistant']),
  content: z.string().min(1, 'Content is required'),
  timestamp: z.string().datetime().optional(),
  isComplete: z.boolean().default(true),
  thinking: z.string().optional(),
  reasoningSteps: z.array(AgentReasoningStepModel).optional(),
});

// ✅ Convert Zod Schemas into NestJS DTOs for Validation
export class CreateChatDto extends createZodDto(CreateChatSchema) {}
export class UpdateChatDto extends createZodDto(UpdateChatSchema) {}
export class AddMessageDto extends createZodDto(AddMessageSchema) {}

// ✅ Infer TypeScript Types from Zod Schemas
export type CreateChatDtoType = z.infer<typeof CreateChatSchema>;
export type UpdateChatDtoType = z.infer<typeof UpdateChatSchema>;
export type AddMessageDtoType = z.infer<typeof AddMessageSchema>;
