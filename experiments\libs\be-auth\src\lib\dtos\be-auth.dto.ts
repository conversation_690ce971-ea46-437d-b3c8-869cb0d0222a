import { z } from 'zod';
import { createZodDto } from '@anatine/zod-nestjs';

// ✅ Define Zod Schemas
export const RegisterSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters long'),
});

export const LoginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters long'),
});

// ✅ Convert Zod Schemas into NestJS DTOs for Validation
export class RegisterDto extends createZodDto(RegisterSchema) {}
export class LoginDto extends createZodDto(LoginSchema) {}

// ✅ Infer TypeScript Types from Zod Schemas
export type RegisterDtoType = z.infer<typeof RegisterSchema>;
export type LoginDtoType = z.infer<typeof LoginSchema>;
