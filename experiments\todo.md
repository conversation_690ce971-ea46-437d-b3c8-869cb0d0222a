# Project Tasks

## 1. Migrate Database Configuration from be-auth to be-database

### Context
Currently, database-related files are located in the be-auth library. We need to extract these and move them to the be-database library to make them shareable across other libraries.

### Tasks
- [x] Create directory structure in be-database library:
  - src/lib/knex/ - For knex configuration
  - src/lib/migrations/ - For database migrations
  - src/lib/env/ - For environment configuration

- [x] Move the following files from be-auth to be-database:
  - knexfile.ts → src/lib/knex/knexfile.ts
  - env.config.ts → src/lib/env/env.config.ts
  - migrations/* → src/lib/migrations/

- [x] Update imports and references in be-auth to use the new locations in be-database
- [x] Create an index.ts file in be-database to export the shared components
- [x] Update the BeAuthModule to import database configuration from be-database

## 2. Implement Chat Storage API

### Context
According to the API specification, we need to implement a new API for storing and retrieving chat data. This will allow users to access their chat history across devices and ensure data persistence.

### Tasks
- [x] Create a new library called be-chat:
  - src/lib/models/ - For chat data models
  - src/lib/controllers/ - For API endpoints
  - src/lib/services/ - For business logic
  - src/lib/repository/ - For database operations
  - src/lib/dtos/ - For data transfer objects

- [x] Create the following models based on the API specification:
  - Chat
  - Message
  - AgentReasoningStep
  - SourceDocument

- [x] Create DTOs for:
  - CreateChatDto
  - UpdateChatDto
  - AddMessageDto

- [x] Create a ChatRepository with methods for:
  - findAllChats(userId)
  - findChatById(chatId)
  - createChat(chat)
  - updateChat(chatId, chat)
  - deleteChat(chatId)
  - addMessage(chatId, message)

- [x] Create a ChatService with business logic for:
  - getAllChats(userId)
  - getChatById(chatId)
  - createChat(createChatDto)
  - updateChat(chatId, updateChatDto)
  - deleteChat(chatId)
  - addMessage(chatId, addMessageDto)

- [x] Create a ChatController with endpoints for:
  - GET /api/chats
  - GET /api/chats/{chatId}
  - POST /api/chats
  - PUT /api/chats/{chatId}
  - DELETE /api/chats/{chatId}
  - POST /api/chats/{chatId}/messages

- [x] Add Swagger documentation for all endpoints
- [x] Implement JWT authentication for all endpoints
- [x] Add validation for all DTOs

## 3. Create Database Migrations

### Context
We need to create database migrations for the chat-related tables.

### Tasks
- [x] Create migrations for:
  - chats table
  - messages table
  - reasoning_steps table
  - source_documents table

- [x] Add relationships between tables:
  - chats to users (many-to-one)
  - messages to chats (many-to-one)
  - reasoning_steps to chats (many-to-one)
  - source_documents to chats (many-to-one)

- [x] Update the module initialization to run migrations

## 4. Update Main Application

### Context
The main psychology-chat application needs to be updated to use the new be-chat library.

### Tasks
- [x] Import BeChatModule in AppModule
- [ ] Update Swagger configuration to include new endpoints
- [ ] Test the API endpoints using Swagger UI

## 5. Security Enhancements

### Context
Based on the previous security issues with the PostgreSQL Docker container, we need to ensure proper security measures are in place.

### Tasks
- [x] Review and update database connection security
- [x] Implement proper error handling for database operations
- [ ] Add rate limiting for API endpoints
- [x] Ensure proper validation of user input
- [x] Add logging for security events

## 6. Fix Lint Issues

### Context
There are several lint issues in the codebase that need to be fixed.

### Tasks
- [x] Fix type issues in the chat controller
- [x] Fix type issues in the migrations
- [x] Fix dependency constraint issues in the be-chat library

## 7. Update Swagger Configuration

### Context
The API endpoints need to be properly documented and testable through Swagger UI.

### Tasks
- [ ] Update Swagger configuration to include new endpoints
- [ ] Add rate limiting for API endpoints
- [ ] Test the API endpoints using Swagger UI
