import * as process from 'node:process';

process.env['NODE_ENV'] = process.env['NODE_ENV'] ?? 'development';

const EnvVariables = process.env;

// Database configuration
export const POSTGRES_HOST = EnvVariables['POSTGRES_HOST'] ?? 'localhost';
export const POSTGRES_PORT = EnvVariables['POSTGRES_PORT'] ?? '5432';
export const POSTGRES_USER = EnvVariables['POSTGRES_USER'] ?? 'postgres';
export const POSTGRES_PASSWORD = EnvVariables['POSTGRES_PASSWORD'] ?? 'JK1zU0z2U7SiAo6';
export const POSTGRES_DB = EnvVariables['POSTGRES_DB'] ?? 'psy_chat';

// Authentication configuration
export const JWT_SECRET = EnvVariables['JWT_SECRET'] ?? 'my_secret_key';

// Security enhancements based on previous security issues
export const DB_CONNECTION_TIMEOUT = EnvVariables['DB_CONNECTION_TIMEOUT'] ?? '30000'; // 30 seconds
export const DB_POOL_MIN = EnvVariables['DB_POOL_MIN'] ?? '2';
export const DB_POOL_MAX = EnvVariables['DB_POOL_MAX'] ?? '10';
export const DB_IDLE_TIMEOUT = EnvVariables['DB_IDLE_TIMEOUT'] ?? '30000'; // 30 seconds
