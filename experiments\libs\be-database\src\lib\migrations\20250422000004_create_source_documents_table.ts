// CommonJS migration file

/**
 * Migration to create the source_documents table
 * This table stores the reference documents used by AI for each chat
 */
exports.up = async function(knex) {
  await knex.schema.createTable('source_documents', (table) => {
    table.string('id').primary();
    table.string('chat_id').notNullable();
    table.text('page_content').notNullable();
    table.jsonb('metadata').notNullable().defaultTo('{}');
    table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    
    // Add foreign key reference to chats table
    table.foreign('chat_id').references('id').inTable('chats').onDelete('CASCADE');
    
    // Add indexes for faster lookups
    table.index('chat_id');
  });
  
  // Add a comment to the table for documentation
  await knex.raw(`COMMENT ON TABLE source_documents IS 'Stores reference documents used by AI for each chat'`);
};

exports.down = async function(knex) {
  await knex.schema.dropTable('source_documents');
};
