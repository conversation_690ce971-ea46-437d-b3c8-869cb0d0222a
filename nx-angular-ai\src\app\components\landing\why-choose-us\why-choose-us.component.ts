import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-why-choose-us',
  standalone: true,
  imports: [CommonModule],
  template: `
    <section class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Header -->
        <div class="text-center mb-16">
          <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Why Choose Our Platform?
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience the future of mental health support with our cutting-edge AI technology 
            and evidence-based psychological approaches.
          </p>
        </div>

        <!-- Features Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Feature 1 -->
          <div class="bg-gradient-to-br from-blue-50 to-indigo-100 p-8 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
            <div class="bg-blue-600 rounded-lg w-12 h-12 flex items-center justify-center mb-6">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Instant Response</h3>
            <p class="text-gray-600">
              Get immediate psychological support without waiting for appointments. 
              Our AI responds instantly to your concerns.
            </p>
          </div>

          <!-- Feature 2 -->
          <div class="bg-gradient-to-br from-green-50 to-emerald-100 p-8 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
            <div class="bg-green-600 rounded-lg w-12 h-12 flex items-center justify-center mb-6">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Evidence-Based</h3>
            <p class="text-gray-600">
              Built on proven psychological frameworks including CBT, DBT, and mindfulness 
              techniques for effective mental health support.
            </p>
          </div>

          <!-- Feature 3 -->
          <div class="bg-gradient-to-br from-purple-50 to-violet-100 p-8 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
            <div class="bg-purple-600 rounded-lg w-12 h-12 flex items-center justify-center mb-6">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Personalized Care</h3>
            <p class="text-gray-600">
              Tailored responses based on your unique situation, personality, 
              and mental health goals for maximum effectiveness.
            </p>
          </div>

          <!-- Feature 4 -->
          <div class="bg-gradient-to-br from-orange-50 to-red-100 p-8 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
            <div class="bg-orange-600 rounded-lg w-12 h-12 flex items-center justify-center mb-6">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Available 24/7</h3>
            <p class="text-gray-600">
              Mental health doesn't follow business hours. Access support whenever 
              you need it, day or night, from anywhere in the world.
            </p>
          </div>

          <!-- Feature 5 -->
          <div class="bg-gradient-to-br from-teal-50 to-cyan-100 p-8 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
            <div class="bg-teal-600 rounded-lg w-12 h-12 flex items-center justify-center mb-6">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Progress Tracking</h3>
            <p class="text-gray-600">
              Monitor your mental health journey with detailed insights, 
              mood tracking, and progress reports to see your improvement.
            </p>
          </div>

          <!-- Feature 6 -->
          <div class="bg-gradient-to-br from-pink-50 to-rose-100 p-8 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
            <div class="bg-pink-600 rounded-lg w-12 h-12 flex items-center justify-center mb-6">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Compassionate AI</h3>
            <p class="text-gray-600">
              Our AI is trained to provide empathetic, non-judgmental support 
              that feels natural and understanding of your emotional needs.
            </p>
          </div>
        </div>

        <!-- Stats Section -->
        <div class="mt-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 md:p-12">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-8 text-center text-white">
            <div>
              <div class="text-4xl font-bold mb-2">10K+</div>
              <div class="text-blue-100">Active Users</div>
            </div>
            <div>
              <div class="text-4xl font-bold mb-2">95%</div>
              <div class="text-blue-100">Satisfaction Rate</div>
            </div>
            <div>
              <div class="text-4xl font-bold mb-2">24/7</div>
              <div class="text-blue-100">Availability</div>
            </div>
            <div>
              <div class="text-4xl font-bold mb-2">50+</div>
              <div class="text-blue-100">Countries Served</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  `,
  styleUrls: ['./why-choose-us.component.scss']
})
export class WhyChooseUsComponent {
}
