import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { NavbarComponent } from '../../landing/navbar/navbar.component';
import { FooterComponent } from '../../landing/footer/footer.component';
import { SafepayService } from '../../../services/safepay.service';

@Component({
  selector: 'app-payment-success',
  standalone: true,
  imports: [CommonModule, RouterModule, NavbarComponent, FooterComponent],
  template: `
    <div class="min-h-screen bg-gray-50 flex flex-col">
      <app-navbar></app-navbar>
      
      <div class="flex-grow flex items-center justify-center">
        <div class="max-w-md w-full bg-white shadow-lg rounded-lg p-8 mx-4">
          <div class="text-center">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100">
              <svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <h2 class="mt-4 text-2xl font-bold text-gray-900">Payment Successful!</h2>
            <p class="mt-2 text-gray-600">
              Thank you for your payment. Your subscription has been activated.
            </p>
            <div class="mt-6 space-y-2">
              <p class="text-sm text-gray-500">Order ID: {{ orderId }}</p>
              <p class="text-sm text-gray-500">Transaction ID: {{ transactionId }}</p>
            </div>
            <div class="mt-6">
              <button 
                (click)="navigateToChat()" 
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Go to Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <app-footer></app-footer>
    </div>
  `,
})
export class PaymentSuccessComponent implements OnInit {
  orderId: string = '';
  transactionId: string = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private safepayService: SafepayService
  ) {}

  ngOnInit(): void {
    // Get token from URL query parameters
    this.route.queryParams.subscribe(params => {
      const token = params['token'];
      if (token) {
        this.verifyPayment(token);
      }
    });
  }

  verifyPayment(token: string): void {
    this.safepayService.verifyPayment(token).subscribe({
      next: (response) => {
        console.log('Payment verification successful:', response);
        this.orderId = response.order_id || 'N/A';
        this.transactionId = response.id || 'N/A';
        // Here you would typically update the user's subscription status in your database
      },
      error: (error) => {
        console.error('Payment verification failed:', error);
        // Handle verification failure
        this.router.navigate(['/payment/cancel']);
      }
    });
  }

  navigateToChat(): void {
    this.router.navigate(['/chat-layout']);
  }
}
