import { Inject, Injectable } from '@nestjs/common';
import { Knex } from 'knex';
import { v4 as uuidv4 } from 'uuid';
import { Chat, Message, AgentReasoningStep, SourceDocument } from '../models/chat.model';
import { CreateChatDto } from '../dtos/create-chat.dto';
import { UpdateChatDto } from '../dtos/update-chat.dto';
import { AddMessageDto } from '../dtos/add-message.dto';

@Injectable()
export class ChatRepository {
  constructor(@Inject('KnexConnection') private readonly knex: Knex) {}

  /**
   * Find all chats for a specific user
   * @param userId The user ID
   * @returns Array of chats
   */
  async findAllChats(userId: string): Promise<Chat[]> {
    const chats = await this.knex
      .select('c.*')
      .from('chats as c')
      .where('c.user_id', userId)
      .orderBy('c.updated_at', 'desc');

    const result: Chat[] = [];

    for (const chat of chats) {
      // Get messages for this chat
      const messages = await this.knex
        .select('*')
        .from('messages')
        .where('chat_id', chat.id)
        .orderBy('timestamp', 'asc');

      // Get reasoning steps for this chat
      const reasoningSteps = await this.knex
        .select('*')
        .from('reasoning_steps')
        .where('chat_id', chat.id);

      // Get source documents for this chat
      const sourceDocuments = await this.knex
        .select('*')
        .from('source_documents')
        .where('chat_id', chat.id);

      result.push({
        id: chat.id,
        title: chat.title,
        messages: messages.map(this.mapMessageFromDb),
        createdAt: chat.created_at,
        updatedAt: chat.updated_at,
        flowChatId: chat.flow_chat_id,
        sessionId: chat.session_id,
        reasoningSteps: reasoningSteps.map(this.mapReasoningStepFromDb),
        sourceDocuments: sourceDocuments.map(this.mapSourceDocumentFromDb),
        userId: chat.user_id,
      });
    }

    return result;
  }

  /**
   * Find a chat by ID for a specific user
   * @param chatId The chat ID
   * @param userId The user ID
   * @returns The chat or null if not found
   */
  async findChatById(chatId: string, userId: string): Promise<Chat | null> {
    const chat = await this.knex
      .select('*')
      .from('chats')
      .where({ id: chatId, user_id: userId })
      .first();

    if (!chat) {
      return null;
    }

    // Get messages for this chat
    const messages = await this.knex
      .select('*')
      .from('messages')
      .where('chat_id', chatId)
      .orderBy('timestamp', 'asc');

    // Get reasoning steps for this chat
    const reasoningSteps = await this.knex
      .select('*')
      .from('reasoning_steps')
      .where('chat_id', chatId);

    // Get source documents for this chat
    const sourceDocuments = await this.knex
      .select('*')
      .from('source_documents')
      .where('chat_id', chatId);

    return {
      id: chat.id,
      title: chat.title,
      messages: messages.map(this.mapMessageFromDb),
      createdAt: chat.created_at,
      updatedAt: chat.updated_at,
      flowChatId: chat.flow_chat_id,
      sessionId: chat.session_id,
      reasoningSteps: reasoningSteps.map(this.mapReasoningStepFromDb),
      sourceDocuments: sourceDocuments.map(this.mapSourceDocumentFromDb),
      userId: chat.user_id,
    };
  }

  /**
   * Create a new chat
   * @param chatData The chat data
   * @param userId The user ID
   * @returns The created chat
   */
  async createChat(chatData: CreateChatDto, userId: string): Promise<Chat> {
    const chatId = uuidv4();

    await this.knex.transaction(async (trx) => {
      // Insert the chat
      await trx('chats').insert({
        id: chatId,
        title: chatData.title,
        flow_chat_id: chatData.flowChatId,
        session_id: chatData.sessionId,
        user_id: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

      // Insert messages if provided
      if (chatData.messages && chatData.messages.length > 0) {
        const messagesToInsert = chatData.messages.map((message) => ({
          id: uuidv4(),
          chat_id: chatId,
          role: message.role,
          content: message.content,
          is_complete: message.isComplete,
          thinking: message.thinking,
          timestamp: message.timestamp || new Date().toISOString(),
        }));

        await trx('messages').insert(messagesToInsert);
      }

      // Insert reasoning steps if provided
      if (chatData.reasoningSteps && chatData.reasoningSteps.length > 0) {
        const reasoningStepsToInsert = chatData.reasoningSteps.map((step) => ({
          id: uuidv4(),
          chat_id: chatId,
          agent_name: step.agentName,
          messages: JSON.stringify(step.messages),
          next: step.next,
          instructions: step.instructions,
          used_tools: JSON.stringify(step.usedTools),
          source_documents: JSON.stringify(step.sourceDocuments),
          artifacts: JSON.stringify(step.artifacts),
          node_id: step.nodeId,
          thought: step.thought,
          action: step.action,
          observation: step.observation,
        }));

        await trx('reasoning_steps').insert(reasoningStepsToInsert);
      }

      // Insert source documents if provided
      if (chatData.sourceDocuments && chatData.sourceDocuments.length > 0) {
        const sourceDocumentsToInsert = chatData.sourceDocuments.map((doc) => ({
          id: uuidv4(),
          chat_id: chatId,
          page_content: doc.pageContent,
          metadata: JSON.stringify(doc.metadata),
        }));

        await trx('source_documents').insert(sourceDocumentsToInsert);
      }
    });

    return this.findChatById(chatId, userId) as Promise<Chat>;
  }

  /**
   * Update an existing chat
   * @param chatId The chat ID
   * @param chatData The chat data
   * @param userId The user ID
   * @returns The updated chat or null if not found
   */
  async updateChat(chatId: string, chatData: UpdateChatDto, userId: string): Promise<Chat | null> {
    const rowsAffected = await this.knex.transaction(async (trx) => {
      // Update the chat
      const updated = await trx('chats')
        .where({ id: chatId, user_id: userId })
        .update({
          title: chatData.title,
          flow_chat_id: chatData.flowChatId,
          session_id: chatData.sessionId,
          updated_at: new Date().toISOString(),
        });

      if (updated === 0) {
        return 0;
      }

      // Update messages if provided
      if (chatData.messages && chatData.messages.length > 0) {
        // Delete existing messages
        await trx('messages').where('chat_id', chatId).del();

        // Insert new messages
        const messagesToInsert = chatData.messages.map((message) => ({
          id: message.id || uuidv4(),
          chat_id: chatId,
          role: message.role,
          content: message.content,
          is_complete: message.isComplete,
          thinking: message.thinking,
          timestamp: message.timestamp || new Date().toISOString(),
        }));

        await trx('messages').insert(messagesToInsert);
      }

      // Update reasoning steps if provided
      if (chatData.reasoningSteps && chatData.reasoningSteps.length > 0) {
        // Delete existing reasoning steps
        await trx('reasoning_steps').where('chat_id', chatId).del();

        // Insert new reasoning steps
        const reasoningStepsToInsert = chatData.reasoningSteps.map((step) => ({
          id: uuidv4(),
          chat_id: chatId,
          agent_name: step.agentName,
          messages: JSON.stringify(step.messages),
          next: step.next,
          instructions: step.instructions,
          used_tools: JSON.stringify(step.usedTools),
          source_documents: JSON.stringify(step.sourceDocuments),
          artifacts: JSON.stringify(step.artifacts),
          node_id: step.nodeId,
          thought: step.thought,
          action: step.action,
          observation: step.observation,
        }));

        await trx('reasoning_steps').insert(reasoningStepsToInsert);
      }

      // Update source documents if provided
      if (chatData.sourceDocuments && chatData.sourceDocuments.length > 0) {
        // Delete existing source documents
        await trx('source_documents').where('chat_id', chatId).del();

        // Insert new source documents
        const sourceDocumentsToInsert = chatData.sourceDocuments.map((doc) => ({
          id: uuidv4(),
          chat_id: chatId,
          page_content: doc.pageContent,
          metadata: JSON.stringify(doc.metadata),
        }));

        await trx('source_documents').insert(sourceDocumentsToInsert);
      }

      return updated;
    });

    if (rowsAffected === 0) {
      return null;
    }

    return this.findChatById(chatId, userId);
  }

  /**
   * Delete a chat
   * @param chatId The chat ID
   * @param userId The user ID
   * @returns True if deleted, false if not found
   */
  async deleteChat(chatId: string, userId: string): Promise<boolean> {
    const rowsAffected = await this.knex.transaction(async (trx) => {
      // Delete messages
      await trx('messages').where('chat_id', chatId).del();

      // Delete reasoning steps
      await trx('reasoning_steps').where('chat_id', chatId).del();

      // Delete source documents
      await trx('source_documents').where('chat_id', chatId).del();

      // Delete the chat
      return await trx('chats').where({ id: chatId, user_id: userId }).del();
    });

    return rowsAffected > 0;
  }

  /**
   * Add a message to a chat
   * @param chatId The chat ID
   * @param messageData The message data
   * @param userId The user ID
   * @returns The updated chat or null if not found
   */
  async addMessage(chatId: string, messageData: AddMessageDto, userId: string): Promise<Chat | null> {
    const chat = await this.knex.transaction(async (trx) => {
      // Check if the chat exists and belongs to the user
      const chatExists = await trx
        .select('*')
        .from('chats')
        .where({ id: chatId, user_id: userId })
        .first();

      if (!chatExists) {
        return null;
      }

      // Insert the message
      await trx('messages').insert({
        id: uuidv4(),
        chat_id: chatId,
        role: messageData.role,
        content: messageData.content,
        is_complete: messageData.isComplete,
        thinking: messageData.thinking,
        timestamp: messageData.timestamp || new Date().toISOString(),
      });

      // Update the chat's updated_at timestamp
      await trx('chats')
        .where({ id: chatId })
        .update({ updated_at: new Date().toISOString() });

      return chatExists;
    });

    if (!chat) {
      return null;
    }

    return this.findChatById(chatId, userId);
  }

  /**
   * Map a message from the database to the Message type
   * @param dbMessage The message from the database
   * @returns The mapped Message
   */
  private mapMessageFromDb(dbMessage: any): Message {
    return {
      id: dbMessage.id,
      role: dbMessage.role,
      content: dbMessage.content,
      isComplete: dbMessage.is_complete,
      thinking: dbMessage.thinking,
      timestamp: dbMessage.timestamp,
    };
  }

  /**
   * Map a reasoning step from the database to the AgentReasoningStep type
   * @param dbStep The reasoning step from the database
   * @returns The mapped AgentReasoningStep
   */
  private mapReasoningStepFromDb(dbStep: any): AgentReasoningStep {
    return {
      agentName: dbStep.agent_name,
      messages: JSON.parse(dbStep.messages || '[]'),
      next: dbStep.next,
      instructions: dbStep.instructions,
      usedTools: JSON.parse(dbStep.used_tools || '[]'),
      sourceDocuments: JSON.parse(dbStep.source_documents || '[]'),
      artifacts: JSON.parse(dbStep.artifacts || '[]'),
      nodeId: dbStep.node_id,
      thought: dbStep.thought,
      action: dbStep.action,
      observation: dbStep.observation,
    };
  }

  /**
   * Map a source document from the database to the SourceDocument type
   * @param dbDoc The source document from the database
   * @returns The mapped SourceDocument
   */
  private mapSourceDocumentFromDb(dbDoc: any): SourceDocument {
    return {
      id: dbDoc.id,
      pageContent: dbDoc.page_content,
      metadata: JSON.parse(dbDoc.metadata || '{}'),
    };
  }
}
