import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { JwtModule } from '@nestjs/jwt';
import { BeChatModule } from '../be-chat.module';
import { <PERSON><PERSON><PERSON>ontroller } from '../controllers/chat.controller';
import { ChatService } from '../services/chat.service';
import { ChatRepository } from '../repository/chat.repository';
import { v4 as uuidv4 } from 'uuid';
import { CreateChatDto } from '../dtos/create-chat.dto';
import { UpdateChatDto } from '../dtos/update-chat.dto';
import { AddMessageDto } from '../dtos/add-message.dto';

// Mock JWT token for testing
const TEST_JWT_SECRET = 'test-secret';
const TEST_USER_ID = uuidv4();
const VALID_JWT = generateTestJwt(TEST_USER_ID);

function generateTestJwt(userId: string): string {
  const jwt = require('jsonwebtoken');
  return jwt.sign({ sub: userId, email: '<EMAIL>' }, TEST_JWT_SECRET, {
    expiresIn: '1h',
  });
}

// Mock repository
class MockChatRepository {
  async findAllChats(userId: string) {
    return [
      {
        id: uuidv4(),
        title: 'Test Chat 1',
        messages: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: TEST_USER_ID,
      },
      {
        id: uuidv4(),
        title: 'Test Chat 2',
        messages: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: TEST_USER_ID,
      },
    ];
  }

  async findChatById(chatId: string, userId: string) {
    return {
      id: chatId,
      title: 'Test Chat',
      messages: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      userId: TEST_USER_ID,
    };
  }

  async createChat(chatData: CreateChatDto, userId: string) {
    return {
      id: uuidv4(),
      ...chatData,
      messages: chatData.messages || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      userId,
    };
  }

  async updateChat(chatId: string, chatData: UpdateChatDto, userId: string) {
    return {
      id: chatId,
      ...chatData,
      messages: chatData.messages || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      userId,
    };
  }

  async deleteChat(chatId: string, userId: string) {
    return true;
  }

  async addMessage(chatId: string, messageData: AddMessageDto, userId: string) {
    return {
      id: chatId,
      title: 'Test Chat',
      messages: [
        {
          id: uuidv4(),
          ...messageData,
          timestamp: messageData.timestamp || new Date().toISOString(),
        },
      ],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      userId,
    };
  }
}

describe('Chat Integration Tests', () => {
  let app: INestApplication;
  let chatService: ChatService;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        JwtModule.register({
          secret: TEST_JWT_SECRET,
          signOptions: { expiresIn: '1h' },
        }),
      ],
      controllers: [ChatController],
      providers: [
        ChatService,
        {
          provide: ChatRepository,
          useClass: MockChatRepository,
        },
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    chatService = moduleFixture.get<ChatService>(ChatService);
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('GET /chats', () => {
    it('should return all chats for the authenticated user', async () => {
      const response = await request(app.getHttpServer())
        .get('/chats')
        .set('Authorization', `Bearer ${VALID_JWT}`)
        .expect(200);

      expect(response.body).toBeInstanceOf(Array);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0]).toHaveProperty('id');
      expect(response.body[0]).toHaveProperty('title');
    });

    it('should return 401 if no JWT token is provided', async () => {
      await request(app.getHttpServer())
        .get('/chats')
        .expect(401);
    });
  });

  describe('GET /chats/:id', () => {
    it('should return a specific chat by ID', async () => {
      const chatId = uuidv4();
      const response = await request(app.getHttpServer())
        .get(`/chats/${chatId}`)
        .set('Authorization', `Bearer ${VALID_JWT}`)
        .expect(200);

      expect(response.body).toHaveProperty('id', chatId);
      expect(response.body).toHaveProperty('title');
    });

    it('should return 401 if no JWT token is provided', async () => {
      const chatId = uuidv4();
      await request(app.getHttpServer())
        .get(`/chats/${chatId}`)
        .expect(401);
    });
  });

  describe('POST /chats', () => {
    it('should create a new chat', async () => {
      const newChat: CreateChatDto = {
        title: 'New Test Chat',
        messages: [],
      };

      const response = await request(app.getHttpServer())
        .post('/chats')
        .set('Authorization', `Bearer ${VALID_JWT}`)
        .send(newChat)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('title', newChat.title);
      expect(response.body).toHaveProperty('userId', TEST_USER_ID);
    });

    it('should return 401 if no JWT token is provided', async () => {
      const newChat: CreateChatDto = {
        title: 'New Test Chat',
        messages: [],
      };

      await request(app.getHttpServer())
        .post('/chats')
        .send(newChat)
        .expect(401);
    });
  });

  describe('PUT /chats/:id', () => {
    it('should update an existing chat', async () => {
      const chatId = uuidv4();
      const updatedChat: UpdateChatDto = {
        title: 'Updated Test Chat',
      };

      const response = await request(app.getHttpServer())
        .put(`/chats/${chatId}`)
        .set('Authorization', `Bearer ${VALID_JWT}`)
        .send(updatedChat)
        .expect(200);

      expect(response.body).toHaveProperty('id', chatId);
      expect(response.body).toHaveProperty('title', updatedChat.title);
      expect(response.body).toHaveProperty('userId', TEST_USER_ID);
    });

    it('should return 401 if no JWT token is provided', async () => {
      const chatId = uuidv4();
      const updatedChat: UpdateChatDto = {
        title: 'Updated Test Chat',
      };

      await request(app.getHttpServer())
        .put(`/chats/${chatId}`)
        .send(updatedChat)
        .expect(401);
    });
  });

  describe('DELETE /chats/:id', () => {
    it('should delete a chat', async () => {
      const chatId = uuidv4();

      await request(app.getHttpServer())
        .delete(`/chats/${chatId}`)
        .set('Authorization', `Bearer ${VALID_JWT}`)
        .expect(200);
    });

    it('should return 401 if no JWT token is provided', async () => {
      const chatId = uuidv4();

      await request(app.getHttpServer())
        .delete(`/chats/${chatId}`)
        .expect(401);
    });
  });

  describe('POST /chats/:id/messages', () => {
    it('should add a message to a chat', async () => {
      const chatId = uuidv4();
      const newMessage: AddMessageDto = {
        role: 'user',
        content: 'Test message content',
        isComplete: true,
      };

      const response = await request(app.getHttpServer())
        .post(`/chats/${chatId}/messages`)
        .set('Authorization', `Bearer ${VALID_JWT}`)
        .send(newMessage)
        .expect(200);

      expect(response.body).toHaveProperty('id', chatId);
      expect(response.body).toHaveProperty('messages');
      expect(response.body.messages.length).toBeGreaterThan(0);
      expect(response.body.messages[0]).toHaveProperty('content', newMessage.content);
      expect(response.body.messages[0]).toHaveProperty('role', newMessage.role);
    });

    it('should return 401 if no JWT token is provided', async () => {
      const chatId = uuidv4();
      const newMessage: AddMessageDto = {
        role: 'user',
        content: 'Test message content',
        isComplete: true,
      };

      await request(app.getHttpServer())
        .post(`/chats/${chatId}/messages`)
        .send(newMessage)
        .expect(401);
    });
  });
});
