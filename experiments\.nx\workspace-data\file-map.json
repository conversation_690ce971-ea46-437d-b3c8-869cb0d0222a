{"version": "6.0", "nxVersion": "20.4.6", "pathMappings": {"@experiments/be-auth": ["libs/be-auth/src/index.ts"], "@experiments/be-database": ["libs/be-database/src/index.ts"], "@experiments/be-chat": ["libs/be-chat/src/index.ts"]}, "nxJsonPlugins": [{"name": "@nx/playwright/plugin", "options": {"targetName": "e2e"}}, {"name": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"name": "@nx/webpack/plugin", "options": {"buildTargetName": "build", "serveTargetName": "serve", "previewTargetName": "preview"}}], "fileMap": {"nonProjectFiles": [{"file": ".dockerignore", "hash": "15422880700179579241"}, {"file": ".editorconfig", "hash": "5443105041930014821"}, {"file": ".env.example", "hash": "14805027137457535899"}, {"file": ".giti<PERSON>re", "hash": "3183856895610895314"}, {"file": ".prettieri<PERSON>re", "hash": "16448050467312557263"}, {"file": ".prettier<PERSON>", "hash": "2742486791828034708"}, {"file": ".vscode/extensions.json", "hash": "17425123947026961272"}, {"file": ".vscode/settings.json", "hash": "15283304382075668742"}, {"file": "CONTRIBUTION.md", "hash": "17777070009520175409"}, {"file": "LICENSE", "hash": "14428130107975762833"}, {"file": "README.md", "hash": "8400872720065311025"}, {"file": "api-specification.md", "hash": "13929825193292068201"}, {"file": "commitlint.config.js", "hash": "4732682152302362927"}, {"file": "context.md", "hash": "10099744506403644228"}, {"file": "docker-compose.yml", "hash": "7767558893549150065"}, {"file": "docker/db/README.md", "hash": "326130063594722514"}, {"file": "docker/db/docker-compose.yml", "hash": "4376430080775051747"}, {"file": "docker/db/pg_hba.conf", "hash": "11789001581082933146"}, {"file": "docker/db/postgresql.conf", "hash": "10954690102483790261"}, {"file": "eslint.config.js", "hash": "12579550640322097274"}, {"file": "jest.config.ts", "hash": "3494843969422251966"}, {"file": "jest.preset.js", "hash": "9430166341120122740"}, {"file": "lint-staged.config.js", "hash": "14648148917025333055"}, {"file": "migrations.json", "hash": "4891501193653098967"}, {"file": "nest-cli.json", "hash": "16491901460726532671"}, {"file": "nx.json", "hash": "18195512228414005117"}, {"file": "package-lock.json", "hash": "14901808678140459923"}, {"file": "package.json", "hash": "17370861051485822843"}, {"file": "run-app.sh", "hash": "9471192734996405291"}, {"file": "stop-app.sh", "hash": "8907648161360670746"}, {"file": "todo.md", "hash": "15132168121977757405"}, {"file": "tsconfig.base.json", "hash": "15423539889019631290"}], "projectFileMap": {"psychology-chat": [{"file": "apps/psychology-chat/Dockerfile", "hash": "9535348810427431123"}, {"file": "apps/psychology-chat/eslint.config.js", "hash": "17472210594086834595"}, {"file": "apps/psychology-chat/jest.config.ts", "hash": "3636951489830392249"}, {"file": "apps/psychology-chat/project.json", "hash": "5209632142584295584"}, {"file": "apps/psychology-chat/src/app/app.module.ts", "hash": "9200975758690367657", "deps": ["npm:@nestjs/common", "be-auth", "npm:@nestjs/config", "be-chat"]}, {"file": "apps/psychology-chat/src/assets/.gitkeep", "hash": "3244421341483603138"}, {"file": "apps/psychology-chat/src/main.ts", "hash": "9580871696005294655", "deps": ["npm:@nestjs/common", "npm:@nestjs/core", "npm:express", "npm:@nestjs/swagger"]}, {"file": "apps/psychology-chat/tsconfig.app.json", "hash": "13133896605933310698"}, {"file": "apps/psychology-chat/tsconfig.json", "hash": "3490524966360018312"}, {"file": "apps/psychology-chat/tsconfig.spec.json", "hash": "11550347367511341917"}, {"file": "apps/psychology-chat/webpack.config.js", "hash": "1527595759214524971", "deps": ["npm:@nx/webpack"]}], "be-auth": [{"file": "libs/be-auth/README.md", "hash": "17810032430258109599"}, {"file": "libs/be-auth/eslint.config.js", "hash": "17472210594086834595"}, {"file": "libs/be-auth/jest.config.ts", "hash": "1048127454851447147"}, {"file": "libs/be-auth/project.json", "hash": "15062612993863123626"}, {"file": "libs/be-auth/src/index.ts", "hash": "11159155949790278406"}, {"file": "libs/be-auth/src/lib/be-auth.module.ts", "hash": "2805115216734750887", "deps": ["npm:@nestjs/common", "npm:@nestjs/jwt", "npm:knex", "be-database"]}, {"file": "libs/be-auth/src/lib/controllers/auth.controller.ts", "hash": "1422506233182790952", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger"]}, {"file": "libs/be-auth/src/lib/dtos/be-auth.dto.ts", "hash": "7590690522361493965", "deps": ["npm:zod", "npm:@anatine/zod-nestjs"]}, {"file": "libs/be-auth/src/lib/env.config.ts", "hash": "8224063901572435531"}, {"file": "libs/be-auth/src/lib/guards/be-jwt-auth.guard.ts", "hash": "10254405516690970673", "deps": ["npm:@nestjs/common", "npm:@nestjs/jwt"]}, {"file": "libs/be-auth/src/lib/knexfile.ts", "hash": "4847320159237536554", "deps": ["npm:dotenv"]}, {"file": "libs/be-auth/src/lib/migrations/20250216231520_create_users.ts", "hash": "15736060903773659188"}, {"file": "libs/be-auth/src/lib/models/be-user.model.ts", "hash": "8580749524423285619", "deps": ["npm:zod"]}, {"file": "libs/be-auth/src/lib/repository/be-user.repository.ts", "hash": "4791057278189631236", "deps": ["npm:@nestjs/common", "npm:knex"]}, {"file": "libs/be-auth/src/lib/services/be-auth.service.ts", "hash": "15390491369925250539", "deps": ["npm:@nestjs/common", "npm:@nestjs/jwt", "npm:bcrypt"]}, {"file": "libs/be-auth/tsconfig.json", "hash": "7605285820089652126"}, {"file": "libs/be-auth/tsconfig.lib.json", "hash": "3550173832616418300"}, {"file": "libs/be-auth/tsconfig.spec.json", "hash": "11550347367511341917"}], "be-chat": [{"file": "libs/be-chat/jest.config.ts", "hash": "18109341748978026124"}, {"file": "libs/be-chat/project.json", "hash": "14021225831236592953"}, {"file": "libs/be-chat/src/index.ts", "hash": "15618220106205283259"}, {"file": "libs/be-chat/src/lib/be-chat.module.ts", "hash": "10240825352043697078", "deps": ["npm:@nestjs/common", "be-database", "be-auth", "npm:knex"]}, {"file": "libs/be-chat/src/lib/controllers/chat.controller.ts", "hash": "15603544618689943759", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "be-auth"]}, {"file": "libs/be-chat/src/lib/dtos/add-message.dto.ts", "hash": "7579909731217853080", "deps": ["npm:zod", "npm:@anatine/zod-nestjs"]}, {"file": "libs/be-chat/src/lib/dtos/chat.dto.ts", "hash": "11822669535393009113", "deps": ["npm:zod", "npm:@anatine/zod-nestjs"]}, {"file": "libs/be-chat/src/lib/dtos/create-chat.dto.ts", "hash": "10718203787695729275", "deps": ["npm:zod", "npm:@anatine/zod-nestjs"]}, {"file": "libs/be-chat/src/lib/dtos/update-chat.dto.ts", "hash": "16179442013572197657", "deps": ["npm:zod", "npm:@anatine/zod-nestjs"]}, {"file": "libs/be-chat/src/lib/migrations/20250422000001_create_chats_table.ts", "hash": "9339227645515609120"}, {"file": "libs/be-chat/src/lib/migrations/20250422000002_create_messages_table.ts", "hash": "6728489691762892195"}, {"file": "libs/be-chat/src/lib/migrations/20250422000003_create_reasoning_steps_table.ts", "hash": "15628067645324021351"}, {"file": "libs/be-chat/src/lib/migrations/20250422000004_create_source_documents_table.ts", "hash": "9926355087037974113"}, {"file": "libs/be-chat/src/lib/models/agent-reasoning-step.model.ts", "hash": "18111487069407246577", "deps": ["npm:zod"]}, {"file": "libs/be-chat/src/lib/models/chat.model.ts", "hash": "12547773328349696247", "deps": ["npm:zod"]}, {"file": "libs/be-chat/src/lib/models/message.model.ts", "hash": "17857457632874583517", "deps": ["npm:zod"]}, {"file": "libs/be-chat/src/lib/models/source-document.model.ts", "hash": "5779405834348726797", "deps": ["npm:zod"]}, {"file": "libs/be-chat/src/lib/repositories/chat.repository.ts", "hash": "3787380264089534582", "deps": ["npm:@nestjs/common", "npm:knex", "npm:uuid"]}, {"file": "libs/be-chat/src/lib/repository/chat.repository.ts", "hash": "9025098023548526921", "deps": ["npm:@nestjs/common", "npm:knex", "npm:uuid"]}, {"file": "libs/be-chat/src/lib/services/chat.service.ts", "hash": "7331739829292269238", "deps": ["npm:@nestjs/common"]}, {"file": "libs/be-chat/src/lib/tests/chat.integration.spec.ts", "hash": "10794403079490481827", "deps": ["npm:@nestjs/testing", "npm:@nestjs/common", "npm:supertest", "npm:@nestjs/jwt", "npm:uuid", ["npm:j<PERSON><PERSON><PERSON><PERSON>", "dynamic"]]}, {"file": "libs/be-chat/src/lib/tests/chat.repository.spec.ts", "hash": "8923994569074557404", "deps": ["npm:@nestjs/testing", "npm:uuid", "npm:@nestjs/common"]}, {"file": "libs/be-chat/src/lib/tests/chat.service.spec.ts", "hash": "8932895439277591636", "deps": ["npm:@nestjs/testing", "npm:uuid", "npm:@nestjs/common"]}, {"file": "libs/be-chat/tsconfig.json", "hash": "7605285820089652126"}, {"file": "libs/be-chat/tsconfig.lib.json", "hash": "3550173832616418300"}, {"file": "libs/be-chat/tsconfig.spec.json", "hash": "11550347367511341917"}], "psychology-chat-e2e": [{"file": "apps/psychology-chat-e2e/eslint.config.js", "hash": "17472210594086834595"}, {"file": "apps/psychology-chat-e2e/jest.config.ts", "hash": "308939211749212968"}, {"file": "apps/psychology-chat-e2e/project.json", "hash": "3823272569338915916"}, {"file": "apps/psychology-chat-e2e/src/psychology-chat/psychology-chat.spec.ts", "hash": "110394728024017389", "deps": ["npm:axios"]}, {"file": "apps/psychology-chat-e2e/src/support/global-setup.ts", "hash": "2568409276037275290"}, {"file": "apps/psychology-chat-e2e/src/support/global-teardown.ts", "hash": "5275447082211835490"}, {"file": "apps/psychology-chat-e2e/src/support/test-setup.ts", "hash": "2474597387307989137", "deps": ["npm:axios"]}, {"file": "apps/psychology-chat-e2e/tsconfig.json", "hash": "15443990093475294480"}, {"file": "apps/psychology-chat-e2e/tsconfig.spec.json", "hash": "14954872453723519891"}], "be-database": [{"file": "libs/be-database/jest.config.ts", "hash": "10469318868641281701"}, {"file": "libs/be-database/project.json", "hash": "918237317263744701"}, {"file": "libs/be-database/src/index.ts", "hash": "4877160985579114894"}, {"file": "libs/be-database/src/lib/env/env.config.ts", "hash": "10992259265789473552"}, {"file": "libs/be-database/src/lib/knex/knexfile.ts", "hash": "13887941944299894766", "deps": ["npm:knex", "npm:dotenv"]}, {"file": "libs/be-database/src/lib/migrations/20250216231520_create_users.ts", "hash": "3087143771057735980"}, {"file": "libs/be-database/src/lib/migrations/20250422000001_create_chats_table.ts", "hash": "872604863274282526"}, {"file": "libs/be-database/src/lib/migrations/20250422000002_create_messages_table.ts", "hash": "6728489691762892195"}, {"file": "libs/be-database/src/lib/migrations/20250422000003_create_reasoning_steps_table.ts", "hash": "17400172311036657268"}, {"file": "libs/be-database/src/lib/migrations/20250422000004_create_source_documents_table.ts", "hash": "9926355087037974113"}, {"file": "libs/be-database/tsconfig.json", "hash": "7605285820089652126"}, {"file": "libs/be-database/tsconfig.lib.json", "hash": "3550173832616418300"}, {"file": "libs/be-database/tsconfig.spec.json", "hash": "11550347367511341917"}]}}}