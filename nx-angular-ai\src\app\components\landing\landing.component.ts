import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { NavbarComponent } from './navbar/navbar.component';
import { HeroBannerComponent } from './hero-banner/hero-banner.component';
import { WhyChooseUsComponent } from './why-choose-us/why-choose-us.component';
import { TransformConversationComponent } from './transform-conversation/transform-conversation.component';
import { FooterComponent } from './footer/footer.component';

@Component({
  selector: 'app-landing',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    NavbarComponent,
    HeroBannerComponent,
    WhyChooseUsComponent,
    TransformConversationComponent,
    FooterComponent
  ],
  template: `
    <div class="min-h-screen bg-gray-50">
      <app-navbar></app-navbar>
      <main>
        <app-hero-banner></app-hero-banner>
        <app-why-choose-us></app-why-choose-us>
        <app-transform-conversation></app-transform-conversation>
      </main>
      <app-footer></app-footer>
    </div>
  `,
  styleUrls: ['./landing.component.scss']
})
export class LandingComponent {
}
