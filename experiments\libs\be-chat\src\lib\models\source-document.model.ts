import { z } from 'zod';

/**
 * SourceDocument model schema definition using Zod
 * Based on the API specification
 */
export const SourceDocumentModel = z.object({
  id: z.string(),
  pageContent: z.string(),
  metadata: z.object({
    source: z.string(),
    blobType: z.string(),
    pdf: z.object({
      // PDF metadata fields
      // Using a more flexible approach since the exact fields aren't specified
    }).optional(),
    loc: z.object({
      lines: z.object({
        from: z.number(),
        to: z.number(),
      }),
    }).optional(),
  }).catchall(z.unknown()), // Allow additional metadata fields
});

export type SourceDocument = z.infer<typeof SourceDocumentModel>;
