import * as process from 'node:process';


process.env['NODE_ENV'] = process.env['NODE_ENV'] ?? 'development';

const EnvVariables = process.env

export const POSTGRES_HOST = EnvVariables['POSTGRES_HOST'] ?? 'localhost';
export const POSTGRES_PORT = EnvVariables['POSTGRES_PORT']  ?? '5432';
export const POSTGRES_USER = EnvVariables['POSTGRES_USER']  ?? 'postgres';
export const POSTGRES_PASSWORD = EnvVariables['POSTGRES_PASSWORD']  ?? 'JK1zU0z2U7SiAo6';
export const POSTGRES_DB = EnvVariables['POSTGRES_DB']  ?? 'psy_chat';
export const JWT_SECRET = EnvVariables['JWT_SECRET']  ?? 'my_secret_key';
