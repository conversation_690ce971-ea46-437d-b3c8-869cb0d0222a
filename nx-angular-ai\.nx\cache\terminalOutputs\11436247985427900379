[33m❯[39m Building...
[32m✔[39m Building...
[37mApplication bundle generation failed. [15.304 seconds][39m
[37m[39m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mD:\project\nx-angular-ai\src\app\components\pricing\pricing.component.ts: Unexpected token (112:47)[39m[22m
[1m[31m[39m[22m
[1m[31m[0m [90m 110 |[39m[31m [32m                  <div class="mb-6">[39m[31m[39m[22m
[1m[31m [90m 111 |[39m[31m [32m                    <span class="text-4xl font-bold text-gray-900">[39m[31m[39m[22m
[1m[31m[31m[1m>[22m[1m[39m[31m[90m 112 |[39m[31m [32m                      ${{ isYearly, basicPlan, : .yearlyPrice, basicPlan, : .monthlyPrice }}[39m[31m[39m[22m
[1m[31m [90m     |[39m[31m                                                [31m[1m^[22m[1m[39m[31m[39m[22m
[1m[31m [90m 113 |[39m[31m [32m                    </span>[39m[31m[39m[22m
[1m[31m [90m 114 |[39m[31m [32m                    <span class="text-gray-600">{{ isYearly ? '/year' : '/month' }}</span>[39m[31m[39m[22m
[1m[31m [90m 115 |[39m[31m [32m                  </div>[39m[31m[0m[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/app.routes.ts:5:33:[39m[22m
[1m[31m[37m      5 │ ...{ PricingComponent } from [32m'./components/pricing/pricing.component'[37m;[39m[22m
[1m[31m        ╵                              [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  This error came from the "onLoad" callback registered here:[39m[22m
[1m[31m[39m[22m
[1m[31m    node_modules/@angular/build/src/tools/esbuild/angular/compiler-plugin.js:323:18:[39m[22m
[1m[31m[37m      323 │             build.[32monLoad[37m({ filter: /\.[cm]?[jt]sx?$/ }, async (ar...[39m[22m
[1m[31m          ╵                   [32m~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m    at setup (D:\project\nx-angular-ai\node_modules\@angular\build\src\tools\esbuild\angular\compiler-plugin.js:323:19)[39m[22m
[1m[31m    at async handlePlugins (D:\project\nx-angular-ai\node_modules\esbuild\lib\main.js:1195:20)[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG5002: Opening tag "div" not terminated.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/landing/hero-banner/hero-banner.component.ts:13:6:[39m[22m
[1m[31m[37m      13 │       [32m<div class="absolute inset-0" style="background-image: url('[37m...[39m[22m
[1m[31m         ╵       [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mNG5002: Unexpected c[39m[22m[1m[31mlosing tag "div". It may happen when the tag has already been closed by another tag. For more info see https://www.w3.org/TR/html5/syntax.html#closing-elements-that-have-implied-end-tags[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/landing/hero-banner/hero-banner.component.ts:13:319:[39m[22m
[1m[31m[37m      13 │ ...rcle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')">[32m</div>[37m[39m[22m
[1m[31m         ╵                                                                [32m~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS-991010: template must be a string[39m[22m
[1m[31m  Value could not be determined statically.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:12:12:[39m[22m
[1m[31m[37m      12 │   template: [32m`[37m[39m[22m
[1m[31m         ╵             [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  A string value could not be determined statically.[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:62:24:[39m[22m
[1m[31m[37m      62 │ ...        ${[32m{ isYearly ? basicPlan.yearlyPrice : basicPlan.monthl[37m...[39m[22m
[1m[31m         ╵              [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18004: No value exists in scope for the shorthand property 'isYearly'. Either declare one or provide an initializer.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:62:26:[39m[22m
[1m[31m[37m      62 │                       ${{ [32misYearly[37m ? basicPlan.yearlyPrice : basic...[39m[22m
[1m[31m         ╵                           [32m~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:62:37:[39m[22m
[1m[31m[37m      62 │ ...            ${{ isYearly ? [32mbasicPlan[37m.yearlyPrice : basicPlan.mo...[39m[22m
[1m[31m         ╵                               [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18004: No value exists in scope for the shorthand property 'basicPlan'. Either declare one or provi[39m[22m[1m[31mde an initializer.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:62:37:[39m[22m
[1m[31m[37m      62 │ ...            ${{ isYearly ? [32mbasicPlan[37m.yearlyPrice : basicPlan.mo...[39m[22m
[1m[31m         ╵                               [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:62:46:[39m[22m
[1m[31m[37m      62 │ ...       ${{ isYearly ? basicPlan[32m.[37myearlyPrice : basicPlan.monthly...[39m[22m
[1m[31m         ╵                                   [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:62:59:[39m[22m
[1m[31m[37m      62 │ ...  ${{ isYearly ? basicPlan.yearlyPrice [32m:[37m basicPlan.monthlyPrice }}[39m[22m
[1m[31m         ╵                                           [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18004: No value exists in scope for the shorthand property 'basicPlan'. Either declare one or provide an initializer.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:62:61:[39m[22m
[1m[31m[37m      62 │ ...  ${{ isYearly ? basicPlan.yearlyPrice : [32mbasicPlan[37m.monthlyPrice }}[39m[22m
[1m[31m         ╵                                             [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:62:70:[39m[22m
[1m[31m[37m      62 │ ...  ${{ isYearly ? basicPlan.yearlyPrice : basicPlan[32m.[37mmonthlyPrice }}[39m[22m
[1m[31m         ╵                                                      [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18004: No value exists in scope for the shorthand property 'isYearly'. Either declare one or provide an initializer.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:110:26:[39m[22m
[1m[31m[3[39m[22m[1m[31m7m      110 │                       ${{ [32misYearly[37m ? intermediatePlan.yearlyPrice...[39m[22m
[1m[31m          ╵                           [32m~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:110:37:[39m[22m
[1m[31m[37m      110 │ ...        ${{ isYearly ? [32mintermediatePlan[37m.yearlyPrice : intermed...[39m[22m
[1m[31m          ╵                           [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18004: No value exists in scope for the shorthand property 'intermediatePlan'. Either declare one or provide an initializer.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:110:37:[39m[22m
[1m[31m[37m      110 │ ...        ${{ isYearly ? [32mintermediatePlan[37m.yearlyPrice : intermed...[39m[22m
[1m[31m          ╵                           [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:110:53:[39m[22m
[1m[31m[37m      110 │ ...${{ isYearly ? intermediatePlan[32m.[37myearlyPrice : intermediatePlan...[39m[22m
[1m[31m          ╵                                   [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:110:66:[39m[22m
[1m[31m[37m      110 │ ...? intermediatePlan.yearlyPrice [32m:[37m intermediatePlan.monthlyPrice }}[39m[22m
[1m[31m          ╵                                   [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18004: No value exists in scope for the shorthand property 'intermediatePlan'. Either declare one or provide an initializer.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:110:68:[39m[22m
[1m[31m[37m      110 │ ...? intermediatePlan.yearlyPrice : [32mintermediatePlan[37m.monthlyPrice }}[39m[22m
[1m[31m          ╵                                 [39m[22m[1m[31m    [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:110:84:[39m[22m
[1m[31m[37m      110 │ ...? intermediatePlan.yearlyPrice : intermediatePlan[32m.[37mmonthlyPrice }}[39m[22m
[1m[31m          ╵                                                     [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18004: No value exists in scope for the shorthand property 'isYearly'. Either declare one or provide an initializer.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:159:26:[39m[22m
[1m[31m[37m      159 │                       ${{ [32misYearly[37m ? advancedPlan.yearlyPrice : a...[39m[22m
[1m[31m          ╵                           [32m~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:159:37:[39m[22m
[1m[31m[37m      159 │ ...          ${{ isYearly ? [32madvancedPlan[37m.yearlyPrice : advancedPl...[39m[22m
[1m[31m          ╵                             [32m~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18004: No value exists in scope for the shorthand property 'advancedPlan'. Either declare one or provide an initializer.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:159:37:[39m[22m
[1m[31m[37m      159 │ ...          ${{ isYearly ? [32madvancedPlan[37m.yearlyPrice : advancedPl...[39m[22m
[1m[31m          ╵                             [32m~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:159:49:[39m[22m
[1m[31m[37m      159 │ ...    ${{ isYearly ? advancedPlan[32m.[37myearlyPrice : advancedPlan.mon...[39m[22m
[1m[31m          ╵                                   [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compi[39m[22m[1m[31mler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:159:62:[39m[22m
[1m[31m[37m      159 │ ...sYearly ? advancedPlan.yearlyPrice [32m:[37m advancedPlan.monthlyPrice }}[39m[22m
[1m[31m          ╵                                       [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18004: No value exists in scope for the shorthand property 'advancedPlan'. Either declare one or provide an initializer.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:159:64:[39m[22m
[1m[31m[37m      159 │ ...sYearly ? advancedPlan.yearlyPrice : [32madvancedPlan[37m.monthlyPrice }}[39m[22m
[1m[31m          ╵                                         [32m~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/app/components/pricing/pricing.component.ts:159:76:[39m[22m
[1m[31m[37m      159 │ ...sYearly ? advancedPlan.yearlyPrice : advancedPlan[32m.[37mmonthlyPrice }}[39m[22m
[1m[31m          ╵                                                     [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
