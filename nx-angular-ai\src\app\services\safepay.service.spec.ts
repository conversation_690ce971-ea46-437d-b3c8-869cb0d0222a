import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { SafepayService, PaymentPlan } from './safepay.service';
import { environment } from '../../../environment';

describe('SafepayService', () => {
  let service: SafepayService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [SafepayService]
    });
    service = TestBed.inject(SafepayService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should create payment with correct headers and payload', () => {
    const mockPlan: PaymentPlan = {
      name: 'Basic Plan',
      amount: 9,
      currency: 'PKR',
      interval: 'monthly'
    };

    const mockResponse = {
      token: 'test-token',
      checkout_url: 'https://checkout.safepay.com/test',
      order_id: 'order_123',
      status: 'pending'
    };

    // Spy on window.location.href
    const locationSpy = jest.spyOn(window.location, 'href', 'set').mockImplementation(() => {});

    service.createPayment(mockPlan);

    const req = httpMock.expectOne(`${environment.SAFEPAY.API_URL}/payments`);
    expect(req.request.method).toBe('POST');
    expect(req.request.headers.get('X-SFPY-MERCHANT-SECRET')).toBe(environment.SAFEPAY.SECRET_KEY);
    expect(req.request.headers.get('Content-Type')).toBe('application/json');
    
    // Check payload
    expect(req.request.body.amount).toBe(900); // 9 * 100 (converted to cents)
    expect(req.request.body.currency).toBe('PKR');
    expect(req.request.body.description).toBe('Basic Plan - monthly subscription');
    expect(req.request.body.success_url).toContain('/payment/success');
    expect(req.request.body.cancel_url).toContain('/payment/cancel');

    req.flush(mockResponse);

    expect(locationSpy).toHaveBeenCalledWith('https://checkout.safepay.com/test');
    locationSpy.mockRestore();
  });

  it('should verify payment with correct headers', () => {
    const token = 'test-token';
    const mockResponse = {
      id: 'payment_123',
      order_id: 'order_123',
      status: 'completed'
    };

    service.verifyPayment(token).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${environment.SAFEPAY.API_URL}/payments/${token}`);
    expect(req.request.method).toBe('GET');
    expect(req.request.headers.get('X-SFPY-MERCHANT-SECRET')).toBe(environment.SAFEPAY.SECRET_KEY);
    expect(req.request.headers.get('Content-Type')).toBe('application/json');

    req.flush(mockResponse);
  });

  it('should handle payment creation error', () => {
    const mockPlan: PaymentPlan = {
      name: 'Basic Plan',
      amount: 9,
      currency: 'PKR',
      interval: 'monthly'
    };

    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    const alertSpy = jest.spyOn(window, 'alert').mockImplementation(() => {});

    service.createPayment(mockPlan);

    const req = httpMock.expectOne(`${environment.SAFEPAY.API_URL}/payments`);
    req.error(new ErrorEvent('Network error'));

    expect(consoleSpy).toHaveBeenCalledWith('Payment initiation failed:', expect.any(Object));
    expect(alertSpy).toHaveBeenCalledWith('Payment initiation failed. Please try again.');

    consoleSpy.mockRestore();
    alertSpy.mockRestore();
  });

  it('should generate unique order IDs', () => {
    const orderId1 = (service as any).generateOrderId();
    const orderId2 = (service as any).generateOrderId();
    
    expect(orderId1).toMatch(/^order_\d+_[a-z0-9]+$/);
    expect(orderId2).toMatch(/^order_\d+_[a-z0-9]+$/);
    expect(orderId1).not.toBe(orderId2);
  });

  it('should create recurring payment with correct payload', () => {
    const mockPlan: PaymentPlan = {
      name: 'Premium Plan',
      amount: 19,
      currency: 'PKR',
      interval: 'yearly'
    };

    const mockResponse = {
      id: 'subscription_123',
      status: 'active'
    };

    service.createRecurringPayment(mockPlan).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${environment.SAFEPAY.API_URL}/subscriptions`);
    expect(req.request.method).toBe('POST');
    expect(req.request.headers.get('X-SFPY-MERCHANT-SECRET')).toBe(environment.SAFEPAY.SECRET_KEY);
    
    // Check payload
    expect(req.request.body.amount).toBe(1900); // 19 * 100
    expect(req.request.body.currency).toBe('PKR');
    expect(req.request.body.interval).toBe('yearly');
    expect(req.request.body.description).toBe('Premium Plan - Recurring yearly subscription');

    req.flush(mockResponse);
  });
});
