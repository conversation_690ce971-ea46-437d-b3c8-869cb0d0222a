// CommonJS migration file

/**
 * Migration to create the reasoning_steps table
 * This table stores the reasoning steps for assistant messages
 */
exports.up = async function(knex) {
  await knex.schema.createTable('reasoning_steps', (table) => {
    table.string('id').primary();
    table.string('chat_id').notNullable();
    table.integer('step_number').notNullable();
    table.text('content').notNullable();
    table.timestamp('timestamp').notNullable().defaultTo(knex.fn.now());
    
    // Add foreign key reference to chats table
    table.foreign('chat_id').references('id').inTable('chats').onDelete('CASCADE');
    
    // Add indexes for faster lookups
    table.index('chat_id');
    table.index(['chat_id', 'step_number']);
  });
  
  // Add a comment to the table for documentation
  await knex.raw(`COMMENT ON TABLE reasoning_steps IS 'Stores reasoning steps for assistant messages'`);
};

exports.down = async function(knex) {
  await knex.schema.dropTable('reasoning_steps');
};
