import { z } from 'zod';
import { AgentReasoningStepModel } from './agent-reasoning-step.model';

/**
 * Message model schema definition using Zod
 * Based on the API specification
 */
export const MessageModel = z.object({
  id: z.string().uuid().optional(),
  role: z.enum(['user', 'assistant']),
  content: z.string(),
  timestamp: z.string().datetime().optional(),
  isComplete: z.boolean().default(true),
  thinking: z.string().optional(),
  reasoningSteps: z.array(AgentReasoningStepModel).optional(),
});

export type Message = z.infer<typeof MessageModel>;
