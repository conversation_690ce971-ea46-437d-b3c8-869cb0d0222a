// CommonJS migration file
const dotenv = require('dotenv');
const { POSTGRES_HOST, POSTGRES_DB, POSTGRES_PASSWORD, POSTGRES_PORT, POSTGRES_USER } = require('./env.config');

dotenv.config();

const beAuthConfig = {
  client: 'pg',
  connection: {
    host: POSTGRES_HOST,
    port: Number(POSTGRES_PORT),
    user: POSTGRES_USER,
    password: POSTGRES_PASSWORD,
    database: POSTGRES_DB,
  },
  migrations: {
    tableName: 'knex_migrations',
    directory: './libs/be-auth/src/lib/migrations',
    extension: 'ts',
    loadExtensions: [".ts"],
  }
};

exports.beAuthConfig = beAuthConfig;
module.exports = beAuthConfig;
