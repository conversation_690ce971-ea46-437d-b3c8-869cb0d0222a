// CommonJS migration file

/**
 * Migration to create the messages table
 * This table stores the messages for each chat
 */
exports.up = async function(knex) {
  await knex.schema.createTable('messages', (table) => {
    table.string('id').primary();
    table.string('chat_id').notNullable();
    table.enum('role', ['user', 'assistant']).notNullable();
    table.text('content').notNullable();
    table.timestamp('timestamp').notNullable().defaultTo(knex.fn.now());
    table.boolean('is_complete').notNullable().defaultTo(true);
    table.text('thinking').nullable();
    
    // Add foreign key reference to chats table
    table.foreign('chat_id').references('id').inTable('chats').onDelete('CASCADE');
    
    // Add indexes for faster lookups
    table.index('chat_id');
    table.index('timestamp');
  });
  
  // Add a comment to the table for documentation
  await knex.raw(`COMMENT ON TABLE messages IS 'Stores messages for each chat'`);
};

exports.down = async function(knex) {
  await knex.schema.dropTable('messages');
};
