import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(private jwtService: JwtService) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;

    if (request.url.startsWith('/auth/')) return true; // Allow auth routes

    if (!authHeader) return false;
    try {
      const token = authHeader.split(' ')[1];
      request.user = this.jwtService.verify(token);
      return true;
    } catch {
      return false;
    }
  }
}
