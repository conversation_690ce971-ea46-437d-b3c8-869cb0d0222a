import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { Knex } from 'knex';
import { Chat } from '../models/chat.model';
import { Message } from '../models/message.model';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class ChatRepository {
  constructor(@Inject('KnexConnection') private readonly knex: Knex) {}

  /**
   * Find all chats for a specific user
   * @param userId The ID of the user
   * @returns Array of chats with basic metadata (no full messages)
   */
  async findAllChats(userId: string): Promise<Partial<Chat>[]> {
    // Get all chats with basic metadata, excluding full message content
    const chats = await this.knex('chats')
      .select(
        'id',
        'title',
        'created_at as createdAt',
        'updated_at as updatedAt',
        'flow_chat_id as flowChatId',
        'session_id as sessionId',
        this.knex.raw('(select count(*) from messages where chat_id = chats.id) as messageCount')
      )
      .where({ user_id: userId })
      .orderBy('updated_at', 'desc');

    // For each chat, get the last message for preview
    for (const chat of chats) {
      const lastMessage = await this.knex('messages')
        .select('content', 'timestamp', 'role')
        .where({ chat_id: chat.id })
        .orderBy('timestamp', 'desc')
        .first();

      chat.lastMessage = lastMessage;
    }

    return chats;
  }

  /**
   * Find a chat by its ID
   * @param chatId The ID of the chat
   * @returns The complete chat with all messages and related data
   */
  async findChatById(chatId: string): Promise<Chat> {
    // Get the chat
    const chat = await this.knex('chats')
      .select(
        'id',
        'title',
        'created_at as createdAt',
        'updated_at as updatedAt',
        'flow_chat_id as flowChatId',
        'session_id as sessionId',
        'user_id as userId'
      )
      .where({ id: chatId })
      .first();

    if (!chat) {
      throw new NotFoundException(`Chat with ID ${chatId} not found`);
    }

    // Get all messages for the chat
    chat.messages = await this.knex('messages')
      .select(
        'id',
        'role',
        'content',
        'timestamp',
        'is_complete as isComplete',
        'thinking'
      )
      .where({ chat_id: chatId })
      .orderBy('timestamp', 'asc');

    // Get reasoning steps if any
    chat.reasoningSteps = await this.knex('reasoning_steps')
      .select('*')
      .where({ chat_id: chatId })
      .orderBy('id', 'asc');

    // Get source documents if any
    chat.sourceDocuments = await this.knex('source_documents')
      .select('*')
      .where({ chat_id: chatId });

    return chat;
  }

  /**
   * Create a new chat
   * @param chat The chat data to create
   * @returns The created chat
   */
  async createChat(chat: Omit<Chat, 'id'>): Promise<Chat> {
    const now = new Date().toISOString();
    const id = uuidv4();

    // Insert the chat
    await this.knex('chats').insert({
      id,
      title: chat.title,
      created_at: now,
      updated_at: now,
      flow_chat_id: chat.flowChatId,
      session_id: chat.sessionId,
      user_id: chat.userId
    });

    // Insert messages if any
    if (chat.messages && chat.messages.length > 0) {
      const messagesToInsert = chat.messages.map(message => ({
        id: uuidv4(),
        chat_id: id,
        role: message.role,
        content: message.content,
        timestamp: message.timestamp || now,
        is_complete: message.isComplete,
        thinking: message.thinking
      }));

      await this.knex('messages').insert(messagesToInsert);
    }

    // Insert reasoning steps if any
    if (chat.reasoningSteps && chat.reasoningSteps.length > 0) {
      const stepsToInsert = chat.reasoningSteps.map((step, index) => {
        // Create a content field that captures all the important information
        let content = '';
        if (step.content) {
          content = step.content;
        } else if (step.thought) {
          content = step.thought;
        } else if (step.instructions) {
          content = step.instructions;
        } else {
          // Store all fields as JSON if no direct content is available
          content = JSON.stringify({
            agentName: step.agentName,
            messages: step.messages,
            next: step.next,
            instructions: step.instructions,
            usedTools: step.usedTools,
            sourceDocuments: step.sourceDocuments,
            artifacts: step.artifacts,
            nodeId: step.nodeId,
            thought: step.thought,
            action: step.action,
            observation: step.observation
          });
        }

        return {
          id: step.id || uuidv4(),
          chat_id: id,
          step_number: step.stepNumber || index + 1,
          content: content,
          timestamp: now
        };
      });

      await this.knex('reasoning_steps').insert(stepsToInsert);
    }

    // Insert source documents if any
    if (chat.sourceDocuments && chat.sourceDocuments.length > 0) {
      const docsToInsert = chat.sourceDocuments.map(doc => ({
        id: doc.id || uuidv4(),
        chat_id: id,
        page_content: doc.pageContent,
        metadata: JSON.stringify(doc.metadata)
      }));

      await this.knex('source_documents').insert(docsToInsert);
    }

    // Return the created chat
    return this.findChatById(id);
  }

  /**
   * Update an existing chat
   * @param chatId The ID of the chat to update
   * @param chat The chat data to update
   * @returns The updated chat
   */
  async updateChat(chatId: string, chat: Partial<Chat>): Promise<Chat> {
    const now = new Date().toISOString();

    // Check if chat exists
    const existingChat = await this.knex('chats')
      .select('id')
      .where({ id: chatId })
      .first();

    if (!existingChat) {
      throw new NotFoundException(`Chat with ID ${chatId} not found`);
    }

    // Update chat basic info
    const updateData: Record<string, string | number | boolean | null> = {
      updated_at: now
    };

    if (chat.title) updateData['title'] = chat.title;
    if (chat.flowChatId) updateData['flow_chat_id'] = chat.flowChatId;
    if (chat.sessionId) updateData['session_id'] = chat.sessionId;

    await this.knex('chats')
      .where({ id: chatId })
      .update(updateData);

    // Update messages if provided
    if (chat.messages && chat.messages.length > 0) {
      // Delete existing messages
      await this.knex('messages')
        .where({ chat_id: chatId })
        .delete();

      // Insert new messages
      const messagesToInsert = chat.messages.map(message => ({
        id: message.id || uuidv4(),
        chat_id: chatId,
        role: message.role,
        content: message.content,
        timestamp: message.timestamp || now,
        is_complete: message.isComplete,
        thinking: message.thinking
      }));

      await this.knex('messages').insert(messagesToInsert);
    }

    // Update reasoning steps if provided
    if (chat.reasoningSteps && chat.reasoningSteps.length > 0) {
      // Delete existing reasoning steps
      await this.knex('reasoning_steps')
        .where({ chat_id: chatId })
        .delete();

      // Insert new reasoning steps
      const stepsToInsert = chat.reasoningSteps.map((step, index) => ({
        id: step.id || uuidv4(),
        chat_id: chatId,
        step_number: step.stepNumber || index + 1,
        content: step.content || step.thought || step.instructions || JSON.stringify({
          agentName: step.agentName,
          messages: step.messages,
          next: step.next,
          instructions: step.instructions,
          usedTools: step.usedTools,
          sourceDocuments: step.sourceDocuments,
          artifacts: step.artifacts,
          nodeId: step.nodeId,
          thought: step.thought,
          action: step.action,
          observation: step.observation
        }),
        timestamp: now
      }));

      await this.knex('reasoning_steps').insert(stepsToInsert);
    }

    // Update source documents if provided
    if (chat.sourceDocuments && chat.sourceDocuments.length > 0) {
      // Delete existing source documents
      await this.knex('source_documents')
        .where({ chat_id: chatId })
        .delete();

      // Insert new source documents
      const docsToInsert = chat.sourceDocuments.map(doc => ({
        id: doc.id || uuidv4(),
        chat_id: chatId,
        page_content: doc.pageContent,
        metadata: JSON.stringify(doc.metadata)
      }));

      await this.knex('source_documents').insert(docsToInsert);
    }

    // Return the updated chat
    return this.findChatById(chatId);
  }

  /**
   * Delete a chat
   * @param chatId The ID of the chat to delete
   * @returns True if successful
   */
  async deleteChat(chatId: string): Promise<boolean> {
    try {
      console.log(`Starting deletion of chat with ID: ${chatId}`);
      
      // Check if chat exists
      const existingChat = await this.knex('chats')
        .select('id', 'user_id as userId')
        .where({ id: chatId })
        .first();

      if (!existingChat) {
        console.log(`Chat with ID ${chatId} not found`);
        throw new NotFoundException(`Chat with ID ${chatId} not found`);
      }

      console.log(`Found chat with ID: ${chatId}, user ID: ${existingChat.userId}, proceeding with deletion`);

      // Count related records before deletion for verification using raw queries
      const messageCountQuery = await this.knex.raw('SELECT COUNT(*) as count FROM messages WHERE chat_id = ?', [chatId]);
      const stepCountQuery = await this.knex.raw('SELECT COUNT(*) as count FROM reasoning_steps WHERE chat_id = ?', [chatId]);
      const docCountQuery = await this.knex.raw('SELECT COUNT(*) as count FROM source_documents WHERE chat_id = ?', [chatId]);
      
      // Extract count values directly from raw query results (PostgreSQL specific)
      const messageCountValue = parseInt(messageCountQuery.rows[0].count, 10);
      const stepCountValue = parseInt(stepCountQuery.rows[0].count, 10);
      const docCountValue = parseInt(docCountQuery.rows[0].count, 10);
      
      console.log(`Related records for chat ${chatId}: messages=${messageCountValue}, steps=${stepCountValue}, docs=${docCountValue}`);

      // First attempt: Use a transaction with explicit deletions
      try {
        await this.knex.transaction(async (trx) => {
          // Delete related data first (foreign key constraints)
          const deletedMessages = await trx('messages').where({ chat_id: chatId }).delete();
          console.log(`Deleted ${deletedMessages} messages for chat ${chatId}`);
          
          const deletedSteps = await trx('reasoning_steps').where({ chat_id: chatId }).delete();
          console.log(`Deleted ${deletedSteps} reasoning steps for chat ${chatId}`);
          
          const deletedDocs = await trx('source_documents').where({ chat_id: chatId }).delete();
          console.log(`Deleted ${deletedDocs} source documents for chat ${chatId}`);

          // Delete the chat
          const deletedChat = await trx('chats').where({ id: chatId }).delete();
          console.log(`Deleted chat ${chatId}: ${deletedChat} rows affected`);
          
          if (deletedChat === 0) {
            throw new Error(`Failed to delete chat ${chatId} in transaction`);
          }
        });
      } catch (trxError) {
        console.error(`Transaction-based deletion failed for chat ${chatId}:`, trxError);
        console.log(`Attempting alternative deletion approach for chat ${chatId}...`);
        
        // Second attempt: Try direct deletion with CASCADE relying on foreign key constraints
        try {
          // Directly delete the chat and rely on CASCADE constraints
          const deletedChat = await this.knex('chats').where({ id: chatId }).delete();
          console.log(`Direct deletion of chat ${chatId}: ${deletedChat} rows affected`);
          
          if (deletedChat === 0) {
            throw new Error(`Failed to delete chat ${chatId} with direct deletion`);
          }
        } catch (directError) {
          console.error(`Direct deletion failed for chat ${chatId}:`, directError);
          
          // Third attempt: Try raw SQL with CASCADE
          try {
            console.log(`Attempting raw SQL deletion for chat ${chatId}...`);
            await this.knex.raw(`DELETE FROM chats WHERE id = ?`, [chatId]);
            console.log(`Raw SQL deletion completed for chat ${chatId}`);
          } catch (rawError) {
            console.error(`Raw SQL deletion failed for chat ${chatId}:`, rawError);
            throw new Error(`All deletion methods failed for chat ${chatId}`);
          }
        }
      }

      // Verify deletion
      const chatStillExists = await this.knex('chats')
        .select('id')
        .where({ id: chatId })
        .first();
        
      if (chatStillExists) {
        console.error(`CRITICAL ERROR: Chat ${chatId} still exists after deletion!`);
        
        // Log database state for debugging
        const dbState = await this.knex.raw(`
          SELECT 
            (SELECT COUNT(*) FROM chats WHERE id = ?) as chat_count,
            (SELECT COUNT(*) FROM messages WHERE chat_id = ?) as message_count,
            (SELECT COUNT(*) FROM reasoning_steps WHERE chat_id = ?) as step_count,
            (SELECT COUNT(*) FROM source_documents WHERE chat_id = ?) as doc_count
        `, [chatId, chatId, chatId, chatId]);
        
        console.error(`Database state for chat ${chatId}:`, dbState.rows[0]);
        return false;
      }

      console.log(`Successfully deleted chat with ID: ${chatId}`);
      return true;
    } catch (error: unknown) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('Error deleting chat:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      // Log more detailed error information
      if (error instanceof Error && error.stack) {
        console.error('Error stack:', error.stack);
      }
      
      throw new Error(`Failed to delete chat: ${errorMessage}`);
    }
  }

  /**
   * Add a message to a chat
   * @param chatId The ID of the chat
   * @param message The message to add
   * @returns The added message and updated chat
   */
  async addMessage(chatId: string, message: Omit<Message, 'id'>): Promise<{ message: Message; chat: Partial<Chat> }> {
    const now = new Date().toISOString();

    // Check if chat exists
    const existingChat = await this.knex('chats')
      .select('id')
      .where({ id: chatId })
      .first();

    if (!existingChat) {
      throw new NotFoundException(`Chat with ID ${chatId} not found`);
    }

    // Create message
    const messageId = uuidv4();
    const newMessage = {
      id: messageId,
      chat_id: chatId,
      role: message.role,
      content: message.content,
      timestamp: message.timestamp || now,
      is_complete: message.isComplete,
      thinking: message.thinking
    };

    await this.knex('messages').insert(newMessage);

    // Add reasoning steps if provided
    if (message.reasoningSteps && message.reasoningSteps.length > 0) {
      const stepsToInsert = message.reasoningSteps.map((step, index) => ({
        id: step.id || uuidv4(),
        chat_id: chatId,
        step_number: step.stepNumber || index + 1,
        content: step.content || step.thought || '',
        timestamp: now
      }));

      await this.knex('reasoning_steps').insert(stepsToInsert);
    }

    // Update chat's updated_at timestamp
    await this.knex('chats')
      .where({ id: chatId })
      .update({ updated_at: now });

    // Get the created message
    const createdMessage = await this.knex('messages')
      .select(
        'id',
        'role',
        'content',
        'timestamp',
        'is_complete as isComplete',
        'thinking'
      )
      .where({ id: messageId })
      .first();

    // Get updated chat (just basic info)
    const updatedChat = await this.knex('chats')
      .select(
        'id',
        'updated_at as updatedAt'
      )
      .where({ id: chatId })
      .first();

    return {
      message: createdMessage,
      chat: updatedChat
    };
  }
}
