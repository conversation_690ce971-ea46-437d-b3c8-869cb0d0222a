version: '3.8'

services:
  db:
    image: postgres:15
    container_name: postgres_db
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: JK1zU0z2U7SiAo6
      POSTGRES_DB: psy_chat
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: "2GB"
    networks:
      - psychology_chat_network
    command: ["postgres", "-c", "shared_buffers=512MB", "-c", "max_connections=200"]

networks:
  psychology_chat_network:
    name: psychology_chat_network
    driver: bridge

volumes:
  postgres_data:
    name: postgres_data
