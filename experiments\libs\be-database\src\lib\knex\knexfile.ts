import type { <PERSON><PERSON> } from 'knex';
import * as dotenv from 'dotenv';
import { 
  POSTGRES_HOST, 
  POSTGRES_DB, 
  POSTGRES_PASSWORD, 
  POSTGRES_PORT, 
  POSTGRES_USER,
  DB_CONNECTION_TIMEOUT,
  DB_POOL_MIN,
  DB_POOL_MAX,
  DB_IDLE_TIMEOUT
} from '../env/env.config';

dotenv.config();

/**
 * Base database configuration with security enhancements
 * Addresses previous security issues with unauthorized access attempts
 * and improves connection handling
 */
export const baseConfig: Knex.Config = {
  client: 'pg',
  connection: {
    host: POSTGRES_HOST,
    port: Number(POSTGRES_PORT),
    user: POSTGRES_USER,
    password: POSTGRES_PASSWORD,
    database: POSTGRES_DB,
    // Security enhancements
    ssl: process.env['NODE_ENV'] === 'production' ? { rejectUnauthorized: true } : false,
    connectionTimeout: Number(DB_CONNECTION_TIMEOUT),
  },
  pool: {
    min: Number(DB_POOL_MIN),
    max: Number(DB_POOL_MAX),
    idleTimeoutMillis: Number(DB_IDLE_TIMEOUT),
    // Properly terminate connections to avoid connection leaks
    afterCreate: (conn: unknown, done: (err: Error | null, conn: unknown) => void) => {
      // Using a more specific type for the connection
      interface PgConnection {
        query: (sql: string, callback: (err: Error | null) => void) => void;
      }
      
      (conn as PgConnection).query('SELECT 1', (err: Error | null) => {
        if (err) {
          console.error('Error creating connection:', err);
        }
        done(err, conn);
      });
    },
  },
  // Improved logging for security monitoring
  log: {
    warn(message) {
      console.warn('[DATABASE WARNING]', message);
    },
    error(message) {
      console.error('[DATABASE ERROR]', message);
    },
    deprecate(message) {
      console.warn('[DATABASE DEPRECATED]', message);
    },
    debug(message) {
      if (process.env['NODE_ENV'] !== 'production') {
        console.log('[DATABASE DEBUG]', message);
      }
    },
  },
};

/**
 * Centralized database configuration for all migrations
 * This replaces the separate auth and chat configurations
 * to ensure migrations run in the correct order
 */
export const centralizedConfig: Knex.Config = {
  ...baseConfig,
  migrations: {
    tableName: 'knex_migrations',
    directory: './libs/be-database/src/lib/migrations',
    extension: 'ts',
    loadExtensions: [".ts"],
  }
};

/**
 * Legacy configurations kept for backward compatibility
 * These should be deprecated in favor of the centralized configuration
 */
export const beAuthConfig: Knex.Config = {
  ...centralizedConfig
};

export const beChatConfig: Knex.Config = {
  ...centralizedConfig
};

export default centralizedConfig;
