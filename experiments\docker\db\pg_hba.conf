# PostgreSQL Client Authentication Configuration File
# TYPE  DATABASE        USER            ADDRESS                 METHOD

# Local connections - require password
local   all             all                                     scram-sha-256

# Allow connections from trusted Docker networks
host    all             all             **********/12           scram-sha-256
host    all             all             ***********/16          scram-sha-256
host    all             all             10.0.0.0/8              scram-sha-256

# Explicitly allow connections from localhost
host    all             all             127.0.0.1/32            scram-sha-256
host    all             all             ::1/128                 scram-sha-256

# Optional: If you have specific known external networks, add them here
# host    all             all             YOUR_NETWORK/CIDR       scram-sha-256
