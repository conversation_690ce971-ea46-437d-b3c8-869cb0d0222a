import { Test, TestingModule } from '@nestjs/testing';
import { ChatService } from '../services/chat.service';
import { ChatRepository } from '../repository/chat.repository';
import { v4 as uuidv4 } from 'uuid';
import { CreateChatDto } from '../dtos/create-chat.dto';
import { UpdateChatDto } from '../dtos/update-chat.dto';
import { AddMessageDto } from '../dtos/add-message.dto';
import { NotFoundException, UnauthorizedException } from '@nestjs/common';

const TEST_USER_ID = uuidv4();

describe('ChatService', () => {
  let service: ChatService;
  let repository: ChatRepository;

  beforeEach(async () => {
    const mockChatRepository = {
      findAllChats: jest.fn(),
      findChatById: jest.fn(),
      createChat: jest.fn(),
      updateChat: jest.fn(),
      deleteChat: jest.fn(),
      addMessage: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ChatService,
        {
          provide: ChatRepository,
          useValue: mockChatRepository,
        },
      ],
    }).compile();

    service = module.get<ChatService>(ChatService);
    repository = module.get<ChatRepository>(ChatRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getAllChats', () => {
    it('should return all chats for a user', async () => {
      const expectedChats = [
        {
          id: uuidv4(),
          title: 'Test Chat 1',
          messages: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          userId: TEST_USER_ID,
        },
        {
          id: uuidv4(),
          title: 'Test Chat 2',
          messages: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          userId: TEST_USER_ID,
        },
      ];

      jest.spyOn(repository, 'findAllChats').mockResolvedValue(expectedChats);

      const result = await service.getAllChats(TEST_USER_ID);
      expect(result).toEqual(expectedChats);
      expect(repository.findAllChats).toHaveBeenCalledWith(TEST_USER_ID);
    });
  });

  describe('getChatById', () => {
    it('should return a chat by ID', async () => {
      const chatId = uuidv4();
      const expectedChat = {
        id: chatId,
        title: 'Test Chat',
        messages: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: TEST_USER_ID,
      };

      jest.spyOn(repository, 'findChatById').mockResolvedValue(expectedChat);

      const result = await service.getChatById(chatId, TEST_USER_ID);
      expect(result).toEqual(expectedChat);
      expect(repository.findChatById).toHaveBeenCalledWith(chatId);
    });

    it('should throw NotFoundException if chat is not found', async () => {
      const chatId = uuidv4();
      
      jest.spyOn(repository, 'findChatById').mockImplementation(() => {
        throw new NotFoundException(`Chat with ID ${chatId} not found`);
      });

      await expect(service.getChatById(chatId, TEST_USER_ID)).rejects.toThrow(NotFoundException);
      expect(repository.findChatById).toHaveBeenCalledWith(chatId);
    });

    it('should throw UnauthorizedException if user does not own the chat', async () => {
      const chatId = uuidv4();
      const expectedChat = {
        id: chatId,
        title: 'Test Chat',
        messages: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: 'different-user-id',
      };

      jest.spyOn(repository, 'findChatById').mockResolvedValue(expectedChat);

      await expect(service.getChatById(chatId, TEST_USER_ID)).rejects.toThrow(UnauthorizedException);
      expect(repository.findChatById).toHaveBeenCalledWith(chatId);
    });
  });

  describe('createChat', () => {
    it('should create a new chat', async () => {
      const chatData: CreateChatDto = {
        title: 'New Test Chat',
        messages: [],
      };

      const expectedChat = {
        id: uuidv4(),
        ...chatData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: TEST_USER_ID,
      };

      // Mock the repository to return the expected chat
      jest.spyOn(repository, 'createChat').mockResolvedValue(expectedChat);

      const result = await service.createChat(chatData, TEST_USER_ID);
      expect(result).toEqual(expectedChat);
      expect(repository.createChat).toHaveBeenCalledWith({
        ...chatData,
        userId: TEST_USER_ID,
      });
    });
  });

  describe('updateChat', () => {
    it('should update an existing chat', async () => {
      const chatId = uuidv4();
      const chatData: UpdateChatDto = {
        title: 'Updated Test Chat',
      };

      const existingChat = {
        id: chatId,
        title: 'Test Chat',
        messages: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: TEST_USER_ID,
      };

      const expectedChat = {
        ...existingChat,
        title: chatData.title || existingChat.title,
        updatedAt: new Date().toISOString(),
      };

      // Mock findChatById to return the existing chat
      jest.spyOn(repository, 'findChatById').mockResolvedValue(existingChat);
      
      // Mock updateChat to return the updated chat
      jest.spyOn(repository, 'updateChat').mockResolvedValue(expectedChat);

      const result = await service.updateChat(chatId, chatData, TEST_USER_ID);
      expect(result).toEqual(expectedChat);
      expect(repository.findChatById).toHaveBeenCalledWith(chatId);
      expect(repository.updateChat).toHaveBeenCalledWith(chatId, chatData);
    });

    it('should throw NotFoundException if chat is not found', async () => {
      const chatId = uuidv4();
      const chatData: UpdateChatDto = {
        title: 'Updated Test Chat',
      };
      
      jest.spyOn(repository, 'findChatById').mockImplementation(() => {
        throw new NotFoundException(`Chat with ID ${chatId} not found`);
      });

      await expect(service.updateChat(chatId, chatData, TEST_USER_ID)).rejects.toThrow(NotFoundException);
      expect(repository.findChatById).toHaveBeenCalledWith(chatId);
    });

    it('should throw UnauthorizedException if user does not own the chat', async () => {
      const chatId = uuidv4();
      const chatData: UpdateChatDto = {
        title: 'Updated Test Chat',
      };
      
      const existingChat = {
        id: chatId,
        title: 'Test Chat',
        messages: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: 'different-user-id',
      };

      jest.spyOn(repository, 'findChatById').mockResolvedValue(existingChat);

      await expect(service.updateChat(chatId, chatData, TEST_USER_ID)).rejects.toThrow(UnauthorizedException);
      expect(repository.findChatById).toHaveBeenCalledWith(chatId);
    });
  });

  describe('deleteChat', () => {
    it('should delete a chat', async () => {
      const chatId = uuidv4();
      
      const existingChat = {
        id: chatId,
        title: 'Test Chat',
        messages: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: TEST_USER_ID,
      };

      jest.spyOn(repository, 'findChatById').mockResolvedValue(existingChat);
      jest.spyOn(repository, 'deleteChat').mockResolvedValue(true);

      const result = await service.deleteChat(chatId, TEST_USER_ID);
      expect(result).toEqual({ message: 'Chat deleted successfully' });
      expect(repository.findChatById).toHaveBeenCalledWith(chatId);
      expect(repository.deleteChat).toHaveBeenCalledWith(chatId);
    });

    it('should throw NotFoundException if chat is not found', async () => {
      const chatId = uuidv4();
      
      jest.spyOn(repository, 'findChatById').mockImplementation(() => {
        throw new NotFoundException(`Chat with ID ${chatId} not found`);
      });

      await expect(service.deleteChat(chatId, TEST_USER_ID)).rejects.toThrow(NotFoundException);
      expect(repository.findChatById).toHaveBeenCalledWith(chatId);
    });

    it('should throw UnauthorizedException if user does not own the chat', async () => {
      const chatId = uuidv4();
      
      const existingChat = {
        id: chatId,
        title: 'Test Chat',
        messages: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: 'different-user-id',
      };

      jest.spyOn(repository, 'findChatById').mockResolvedValue(existingChat);

      await expect(service.deleteChat(chatId, TEST_USER_ID)).rejects.toThrow(UnauthorizedException);
      expect(repository.findChatById).toHaveBeenCalledWith(chatId);
    });
  });

  describe('addMessage', () => {
    it('should add a message to a chat', async () => {
      const chatId = uuidv4();
      const messageData: AddMessageDto = {
        role: 'user',
        content: 'Test message content',
        isComplete: true,
      };

      const existingChat = {
        id: chatId,
        title: 'Test Chat',
        messages: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: TEST_USER_ID,
      };

      const newMessage = {
        id: uuidv4(),
        role: messageData.role,
        content: messageData.content,
        isComplete: messageData.isComplete,
        timestamp: new Date().toISOString(),
      };

      const updatedChat = {
        id: chatId,
        updatedAt: new Date().toISOString(),
      };

      jest.spyOn(repository, 'findChatById').mockResolvedValue(existingChat);
      jest.spyOn(repository, 'addMessage').mockResolvedValue({
        message: newMessage,
        chat: updatedChat,
      });

      const result = await service.addMessage(chatId, messageData, TEST_USER_ID);
      expect(result).toEqual({
        message: newMessage,
        chat: updatedChat,
      });
      expect(repository.findChatById).toHaveBeenCalledWith(chatId);
      expect(repository.addMessage).toHaveBeenCalledWith(chatId, expect.objectContaining({
        role: messageData.role,
        content: messageData.content,
        isComplete: messageData.isComplete,
      }));
    });

    it('should throw NotFoundException if chat is not found', async () => {
      const chatId = uuidv4();
      const messageData: AddMessageDto = {
        role: 'user',
        content: 'Test message content',
        isComplete: true,
      };
      
      jest.spyOn(repository, 'findChatById').mockImplementation(() => {
        throw new NotFoundException(`Chat with ID ${chatId} not found`);
      });

      await expect(service.addMessage(chatId, messageData, TEST_USER_ID)).rejects.toThrow(NotFoundException);
      expect(repository.findChatById).toHaveBeenCalledWith(chatId);
    });

    it('should throw UnauthorizedException if user does not own the chat', async () => {
      const chatId = uuidv4();
      const messageData: AddMessageDto = {
        role: 'user',
        content: 'Test message content',
        isComplete: true,
      };
      
      const existingChat = {
        id: chatId,
        title: 'Test Chat',
        messages: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: 'different-user-id',
      };

      jest.spyOn(repository, 'findChatById').mockResolvedValue(existingChat);

      await expect(service.addMessage(chatId, messageData, TEST_USER_ID)).rejects.toThrow(UnauthorizedException);
      expect(repository.findChatById).toHaveBeenCalledWith(chatId);
    });
  });
});
