var Tb=Object.defineProperty,kb=Object.defineProperties;var Ab=Object.getOwnPropertyDescriptors;var sh=Object.getOwnPropertySymbols;var Nb=Object.prototype.hasOwnProperty,Rb=Object.prototype.propertyIsEnumerable;var ah=(e,n,t)=>n in e?Tb(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,b=(e,n)=>{for(var t in n||={})Nb.call(n,t)&&ah(e,t,n[t]);if(sh)for(var t of sh(n))Rb.call(n,t)&&ah(e,t,n[t]);return e},I=(e,n)=>kb(e,Ab(n));var Jt=(e,n,t)=>new Promise((r,o)=>{var i=l=>{try{a(t.next(l))}catch(c){o(c)}},s=l=>{try{a(t.throw(l))}catch(c){o(c)}},a=l=>l.done?r(l.value):Promise.resolve(l.value).then(i,s);a((t=t.apply(e,n)).next())});function ch(e,n){return Object.is(e,n)}var Ie=null,ss=!1,Zl=1,st=Symbol("SIGNAL");function G(e){let n=Ie;return Ie=e,n}function uh(){return Ie}var Mr={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function ls(e){if(ss)throw new Error("");if(Ie===null)return;Ie.consumerOnSignalRead(e);let n=Ie.nextProducerIndex++;if(fs(Ie),n<Ie.producerNode.length&&Ie.producerNode[n]!==e&&jo(Ie)){let t=Ie.producerNode[n];ds(t,Ie.producerIndexOfThis[n])}Ie.producerNode[n]!==e&&(Ie.producerNode[n]=e,Ie.producerIndexOfThis[n]=jo(Ie)?ph(e,Ie,n):0),Ie.producerLastReadVersion[n]=e.version}function Ob(){Zl++}function dh(e){if(!(jo(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Zl)){if(!e.producerMustRecompute(e)&&!us(e)){lh(e);return}e.producerRecomputeValue(e),lh(e)}}function fh(e){if(e.liveConsumerNode===void 0)return;let n=ss;ss=!0;try{for(let t of e.liveConsumerNode)t.dirty||Pb(t)}finally{ss=n}}function hh(){return Ie?.consumerAllowSignalWrites!==!1}function Pb(e){e.dirty=!0,fh(e),e.consumerMarkedDirty?.(e)}function lh(e){e.dirty=!1,e.lastCleanEpoch=Zl}function Bo(e){return e&&(e.nextProducerIndex=0),G(e)}function cs(e,n){if(G(n),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(jo(e))for(let t=e.nextProducerIndex;t<e.producerNode.length;t++)ds(e.producerNode[t],e.producerIndexOfThis[t]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function us(e){fs(e);for(let n=0;n<e.producerNode.length;n++){let t=e.producerNode[n],r=e.producerLastReadVersion[n];if(r!==t.version||(dh(t),r!==t.version))return!0}return!1}function $o(e){if(fs(e),jo(e))for(let n=0;n<e.producerNode.length;n++)ds(e.producerNode[n],e.producerIndexOfThis[n]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function ph(e,n,t){if(gh(e),e.liveConsumerNode.length===0&&mh(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=ph(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(t),e.liveConsumerNode.push(n)-1}function ds(e,n){if(gh(e),e.liveConsumerNode.length===1&&mh(e))for(let r=0;r<e.producerNode.length;r++)ds(e.producerNode[r],e.producerIndexOfThis[r]);let t=e.liveConsumerNode.length-1;if(e.liveConsumerNode[n]=e.liveConsumerNode[t],e.liveConsumerIndexOfThis[n]=e.liveConsumerIndexOfThis[t],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,n<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[n],o=e.liveConsumerNode[n];fs(o),o.producerIndexOfThis[r]=n}}function jo(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function fs(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function gh(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function mh(e){return e.producerNode!==void 0}function vh(e){let n=Object.create(Fb);n.computation=e;let t=()=>{if(dh(n),ls(n),n.value===as)throw n.error;return n.value};return t[st]=n,t}var ql=Symbol("UNSET"),Wl=Symbol("COMPUTING"),as=Symbol("ERRORED"),Fb=I(b({},Mr),{value:ql,dirty:!0,error:null,equal:ch,kind:"computed",producerMustRecompute(e){return e.value===ql||e.value===Wl},producerRecomputeValue(e){if(e.value===Wl)throw new Error("Detected cycle in computations.");let n=e.value;e.value=Wl;let t=Bo(e),r,o=!1;try{r=e.computation(),G(null),o=n!==ql&&n!==as&&r!==as&&e.equal(n,r)}catch(i){r=as,e.error=i}finally{cs(e,t)}if(o){e.value=n;return}e.value=r,e.version++}});function Lb(){throw new Error}var yh=Lb;function bh(e){yh(e)}function Ch(e){yh=e}var Vb=null;function wh(e){let n=Object.create(Yl);n.value=e;let t=()=>(ls(n),n.value);return t[st]=n,t}function hs(e,n){hh()||bh(e),e.equal(e.value,n)||(e.value=n,jb(e))}function Dh(e,n){hh()||bh(e),hs(e,n(e.value))}var Yl=I(b({},Mr),{equal:ch,value:void 0,kind:"signal"});function jb(e){e.version++,Ob(),fh(e),Vb?.()}var Ql;function Uo(){return Ql}function Kt(e){let n=Ql;return Ql=e,n}var Jl=Symbol("NotFound");function L(e){return typeof e=="function"}function Ir(e){let t=e(r=>{Error.call(r),r.stack=new Error().stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}var ps=Ir(e=>function(t){e(this),this.message=t?`${t.length} errors occurred during unsubscription:
${t.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=t});function Ho(e,n){if(e){let t=e.indexOf(n);0<=t&&e.splice(t,1)}}var ie=class e{constructor(n){this.initialTeardown=n,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let n;if(!this.closed){this.closed=!0;let{_parentage:t}=this;if(t)if(this._parentage=null,Array.isArray(t))for(let i of t)i.remove(this);else t.remove(this);let{initialTeardown:r}=this;if(L(r))try{r()}catch(i){n=i instanceof ps?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{_h(i)}catch(s){n=n??[],s instanceof ps?n=[...n,...s.errors]:n.push(s)}}if(n)throw new ps(n)}}add(n){var t;if(n&&n!==this)if(this.closed)_h(n);else{if(n instanceof e){if(n.closed||n._hasParent(this))return;n._addParent(this)}(this._finalizers=(t=this._finalizers)!==null&&t!==void 0?t:[]).push(n)}}_hasParent(n){let{_parentage:t}=this;return t===n||Array.isArray(t)&&t.includes(n)}_addParent(n){let{_parentage:t}=this;this._parentage=Array.isArray(t)?(t.push(n),t):t?[t,n]:n}_removeParent(n){let{_parentage:t}=this;t===n?this._parentage=null:Array.isArray(t)&&Ho(t,n)}remove(n){let{_finalizers:t}=this;t&&Ho(t,n),n instanceof e&&n._removeParent(this)}};ie.EMPTY=(()=>{let e=new ie;return e.closed=!0,e})();var Kl=ie.EMPTY;function gs(e){return e instanceof ie||e&&"closed"in e&&L(e.remove)&&L(e.add)&&L(e.unsubscribe)}function _h(e){L(e)?e():e.unsubscribe()}var Dt={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Tr={setTimeout(e,n,...t){let{delegate:r}=Tr;return r?.setTimeout?r.setTimeout(e,n,...t):setTimeout(e,n,...t)},clearTimeout(e){let{delegate:n}=Tr;return(n?.clearTimeout||clearTimeout)(e)},delegate:void 0};function ms(e){Tr.setTimeout(()=>{let{onUnhandledError:n}=Dt;if(n)n(e);else throw e})}function zo(){}var Eh=Xl("C",void 0,void 0);function xh(e){return Xl("E",void 0,e)}function Sh(e){return Xl("N",e,void 0)}function Xl(e,n,t){return{kind:e,value:n,error:t}}var Zn=null;function kr(e){if(Dt.useDeprecatedSynchronousErrorHandling){let n=!Zn;if(n&&(Zn={errorThrown:!1,error:null}),e(),n){let{errorThrown:t,error:r}=Zn;if(Zn=null,t)throw r}}else e()}function Mh(e){Dt.useDeprecatedSynchronousErrorHandling&&Zn&&(Zn.errorThrown=!0,Zn.error=e)}var Yn=class extends ie{constructor(n){super(),this.isStopped=!1,n?(this.destination=n,gs(n)&&n.add(this)):this.destination=Ub}static create(n,t,r){return new Ar(n,t,r)}next(n){this.isStopped?tc(Sh(n),this):this._next(n)}error(n){this.isStopped?tc(xh(n),this):(this.isStopped=!0,this._error(n))}complete(){this.isStopped?tc(Eh,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(n){this.destination.next(n)}_error(n){try{this.destination.error(n)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},Bb=Function.prototype.bind;function ec(e,n){return Bb.call(e,n)}var nc=class{constructor(n){this.partialObserver=n}next(n){let{partialObserver:t}=this;if(t.next)try{t.next(n)}catch(r){vs(r)}}error(n){let{partialObserver:t}=this;if(t.error)try{t.error(n)}catch(r){vs(r)}else vs(n)}complete(){let{partialObserver:n}=this;if(n.complete)try{n.complete()}catch(t){vs(t)}}},Ar=class extends Yn{constructor(n,t,r){super();let o;if(L(n)||!n)o={next:n??void 0,error:t??void 0,complete:r??void 0};else{let i;this&&Dt.useDeprecatedNextContext?(i=Object.create(n),i.unsubscribe=()=>this.unsubscribe(),o={next:n.next&&ec(n.next,i),error:n.error&&ec(n.error,i),complete:n.complete&&ec(n.complete,i)}):o=n}this.destination=new nc(o)}};function vs(e){Dt.useDeprecatedSynchronousErrorHandling?Mh(e):ms(e)}function $b(e){throw e}function tc(e,n){let{onStoppedNotification:t}=Dt;t&&Tr.setTimeout(()=>t(e,n))}var Ub={closed:!0,next:zo,error:$b,complete:zo};var Nr=typeof Symbol=="function"&&Symbol.observable||"@@observable";function at(e){return e}function rc(...e){return oc(e)}function oc(e){return e.length===0?at:e.length===1?e[0]:function(t){return e.reduce((r,o)=>o(r),t)}}var K=(()=>{class e{constructor(t){t&&(this._subscribe=t)}lift(t){let r=new e;return r.source=this,r.operator=t,r}subscribe(t,r,o){let i=zb(t)?t:new Ar(t,r,o);return kr(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(t){try{return this._subscribe(t)}catch(r){t.error(r)}}forEach(t,r){return r=Ih(r),new r((o,i)=>{let s=new Ar({next:a=>{try{t(a)}catch(l){i(l),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(t){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(t)}[Nr](){return this}pipe(...t){return oc(t)(this)}toPromise(t){return t=Ih(t),new t((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=n=>new e(n),e})();function Ih(e){var n;return(n=e??Dt.Promise)!==null&&n!==void 0?n:Promise}function Hb(e){return e&&L(e.next)&&L(e.error)&&L(e.complete)}function zb(e){return e&&e instanceof Yn||Hb(e)&&gs(e)}function ic(e){return L(e?.lift)}function X(e){return n=>{if(ic(n))return n.lift(function(t){try{return e(t,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function Y(e,n,t,r,o){return new sc(e,n,t,r,o)}var sc=class extends Yn{constructor(n,t,r,o,i,s){super(n),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=t?function(a){try{t(a)}catch(l){n.error(l)}}:super._next,this._error=o?function(a){try{o(a)}catch(l){n.error(l)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){n.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var n;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:t}=this;super.unsubscribe(),!t&&((n=this.onFinalize)===null||n===void 0||n.call(this))}}};function Rr(){return X((e,n)=>{let t=null;e._refCount++;let r=Y(n,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){t=null;return}let o=e._connection,i=t;t=null,o&&(!i||o===i)&&o.unsubscribe(),n.unsubscribe()});e.subscribe(r),r.closed||(t=e.connect())})}var Or=class extends K{constructor(n,t){super(),this.source=n,this.subjectFactory=t,this._subject=null,this._refCount=0,this._connection=null,ic(n)&&(this.lift=n.lift)}_subscribe(n){return this.getSubject().subscribe(n)}getSubject(){let n=this._subject;return(!n||n.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:n}=this;this._subject=this._connection=null,n?.unsubscribe()}connect(){let n=this._connection;if(!n){n=this._connection=new ie;let t=this.getSubject();n.add(this.source.subscribe(Y(t,void 0,()=>{this._teardown(),t.complete()},r=>{this._teardown(),t.error(r)},()=>this._teardown()))),n.closed&&(this._connection=null,n=ie.EMPTY)}return n}refCount(){return Rr()(this)}};var Th=Ir(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var pe=(()=>{class e extends K{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(t){let r=new ys(this,this);return r.operator=t,r}_throwIfClosed(){if(this.closed)throw new Th}next(t){kr(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(t)}})}error(t){kr(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=t;let{observers:r}=this;for(;r.length;)r.shift().error(t)}})}complete(){kr(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:t}=this;for(;t.length;)t.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var t;return((t=this.observers)===null||t===void 0?void 0:t.length)>0}_trySubscribe(t){return this._throwIfClosed(),super._trySubscribe(t)}_subscribe(t){return this._throwIfClosed(),this._checkFinalizedStatuses(t),this._innerSubscribe(t)}_innerSubscribe(t){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Kl:(this.currentObservers=null,i.push(t),new ie(()=>{this.currentObservers=null,Ho(i,t)}))}_checkFinalizedStatuses(t){let{hasError:r,thrownError:o,isStopped:i}=this;r?t.error(o):i&&t.complete()}asObservable(){let t=new K;return t.source=this,t}}return e.create=(n,t)=>new ys(n,t),e})(),ys=class extends pe{constructor(n,t){super(),this.destination=n,this.source=t}next(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.next)===null||r===void 0||r.call(t,n)}error(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.error)===null||r===void 0||r.call(t,n)}complete(){var n,t;(t=(n=this.destination)===null||n===void 0?void 0:n.complete)===null||t===void 0||t.call(n)}_subscribe(n){var t,r;return(r=(t=this.source)===null||t===void 0?void 0:t.subscribe(n))!==null&&r!==void 0?r:Kl}};var De=class extends pe{constructor(n){super(),this._value=n}get value(){return this.getValue()}_subscribe(n){let t=super._subscribe(n);return!t.closed&&n.next(this._value),t}getValue(){let{hasError:n,thrownError:t,_value:r}=this;if(n)throw t;return this._throwIfClosed(),r}next(n){super.next(this._value=n)}};var Ye=new K(e=>e.complete());function kh(e){return e&&L(e.schedule)}function Ah(e){return e[e.length-1]}function bs(e){return L(Ah(e))?e.pop():void 0}function Tn(e){return kh(Ah(e))?e.pop():void 0}function Rh(e,n,t,r){function o(i){return i instanceof t?i:new t(function(s){s(i)})}return new(t||(t=Promise))(function(i,s){function a(u){try{c(r.next(u))}catch(h){s(h)}}function l(u){try{c(r.throw(u))}catch(h){s(h)}}function c(u){u.done?i(u.value):o(u.value).then(a,l)}c((r=r.apply(e,n||[])).next())})}function Nh(e){var n=typeof Symbol=="function"&&Symbol.iterator,t=n&&e[n],r=0;if(t)return t.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}function Qn(e){return this instanceof Qn?(this.v=e,this):new Qn(e)}function Oh(e,n,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=t.apply(e,n||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(g){return function(v){return Promise.resolve(v).then(g,h)}}function a(g,v){r[g]&&(o[g]=function(y){return new Promise(function(_,A){i.push([g,y,_,A])>1||l(g,y)})},v&&(o[g]=v(o[g])))}function l(g,v){try{c(r[g](v))}catch(y){m(i[0][3],y)}}function c(g){g.value instanceof Qn?Promise.resolve(g.value.v).then(u,h):m(i[0][2],g)}function u(g){l("next",g)}function h(g){l("throw",g)}function m(g,v){g(v),i.shift(),i.length&&l(i[0][0],i[0][1])}}function Ph(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=e[Symbol.asyncIterator],t;return n?n.call(e):(e=typeof Nh=="function"?Nh(e):e[Symbol.iterator](),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(i){t[i]=e[i]&&function(s){return new Promise(function(a,l){s=e[i](s),o(a,l,s.done,s.value)})}}function o(i,s,a,l){Promise.resolve(l).then(function(c){i({value:c,done:a})},s)}}var Cs=e=>e&&typeof e.length=="number"&&typeof e!="function";function ws(e){return L(e?.then)}function Ds(e){return L(e[Nr])}function _s(e){return Symbol.asyncIterator&&L(e?.[Symbol.asyncIterator])}function Es(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function Gb(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var xs=Gb();function Ss(e){return L(e?.[xs])}function Ms(e){return Oh(this,arguments,function*(){let t=e.getReader();try{for(;;){let{value:r,done:o}=yield Qn(t.read());if(o)return yield Qn(void 0);yield yield Qn(r)}}finally{t.releaseLock()}})}function Is(e){return L(e?.getReader)}function _e(e){if(e instanceof K)return e;if(e!=null){if(Ds(e))return qb(e);if(Cs(e))return Wb(e);if(ws(e))return Zb(e);if(_s(e))return Fh(e);if(Ss(e))return Yb(e);if(Is(e))return Qb(e)}throw Es(e)}function qb(e){return new K(n=>{let t=e[Nr]();if(L(t.subscribe))return t.subscribe(n);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Wb(e){return new K(n=>{for(let t=0;t<e.length&&!n.closed;t++)n.next(e[t]);n.complete()})}function Zb(e){return new K(n=>{e.then(t=>{n.closed||(n.next(t),n.complete())},t=>n.error(t)).then(null,ms)})}function Yb(e){return new K(n=>{for(let t of e)if(n.next(t),n.closed)return;n.complete()})}function Fh(e){return new K(n=>{Jb(e,n).catch(t=>n.error(t))})}function Qb(e){return Fh(Ms(e))}function Jb(e,n){var t,r,o,i;return Rh(this,void 0,void 0,function*(){try{for(t=Ph(e);r=yield t.next(),!r.done;){let s=r.value;if(n.next(s),n.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=t.return)&&(yield i.call(t))}finally{if(o)throw o.error}}n.complete()})}function Qe(e,n,t,r=0,o=!1){let i=n.schedule(function(){t(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Ts(e,n=0){return X((t,r)=>{t.subscribe(Y(r,o=>Qe(r,e,()=>r.next(o),n),()=>Qe(r,e,()=>r.complete(),n),o=>Qe(r,e,()=>r.error(o),n)))})}function ks(e,n=0){return X((t,r)=>{r.add(e.schedule(()=>t.subscribe(r),n))})}function Lh(e,n){return _e(e).pipe(ks(n),Ts(n))}function Vh(e,n){return _e(e).pipe(ks(n),Ts(n))}function jh(e,n){return new K(t=>{let r=0;return n.schedule(function(){r===e.length?t.complete():(t.next(e[r++]),t.closed||this.schedule())})})}function Bh(e,n){return new K(t=>{let r;return Qe(t,n,()=>{r=e[xs](),Qe(t,n,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){t.error(s);return}i?t.complete():t.next(o)},0,!0)}),()=>L(r?.return)&&r.return()})}function As(e,n){if(!e)throw new Error("Iterable cannot be null");return new K(t=>{Qe(t,n,()=>{let r=e[Symbol.asyncIterator]();Qe(t,n,()=>{r.next().then(o=>{o.done?t.complete():t.next(o.value)})},0,!0)})})}function $h(e,n){return As(Ms(e),n)}function Uh(e,n){if(e!=null){if(Ds(e))return Lh(e,n);if(Cs(e))return jh(e,n);if(ws(e))return Vh(e,n);if(_s(e))return As(e,n);if(Ss(e))return Bh(e,n);if(Is(e))return $h(e,n)}throw Es(e)}function de(e,n){return n?Uh(e,n):_e(e)}function R(...e){let n=Tn(e);return de(e,n)}function Pr(e,n){let t=L(e)?e:()=>e,r=o=>o.error(t());return new K(n?o=>n.schedule(r,0,o):r)}function ac(e){return!!e&&(e instanceof K||L(e.lift)&&L(e.subscribe))}var _t=Ir(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function lc(e,n){let t=typeof n=="object";return new Promise((r,o)=>{let i=!1,s;e.subscribe({next:a=>{s=a,i=!0},error:o,complete:()=>{i?r(s):t?r(n.defaultValue):o(new _t)}})})}function B(e,n){return X((t,r)=>{let o=0;t.subscribe(Y(r,i=>{r.next(e.call(n,i,o++))}))})}var{isArray:Kb}=Array;function Xb(e,n){return Kb(n)?e(...n):e(n)}function Ns(e){return B(n=>Xb(e,n))}var{isArray:eC}=Array,{getPrototypeOf:tC,prototype:nC,keys:rC}=Object;function Rs(e){if(e.length===1){let n=e[0];if(eC(n))return{args:n,keys:null};if(oC(n)){let t=rC(n);return{args:t.map(r=>n[r]),keys:t}}}return{args:e,keys:null}}function oC(e){return e&&typeof e=="object"&&tC(e)===nC}function Os(e,n){return e.reduce((t,r,o)=>(t[r]=n[o],t),{})}function Go(...e){let n=Tn(e),t=bs(e),{args:r,keys:o}=Rs(e);if(r.length===0)return de([],n);let i=new K(iC(r,n,o?s=>Os(o,s):at));return t?i.pipe(Ns(t)):i}function iC(e,n,t=at){return r=>{Hh(n,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let l=0;l<o;l++)Hh(n,()=>{let c=de(e[l],n),u=!1;c.subscribe(Y(r,h=>{i[l]=h,u||(u=!0,a--),a||r.next(t(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Hh(e,n,t){e?Qe(t,e,n):n()}function zh(e,n,t,r,o,i,s,a){let l=[],c=0,u=0,h=!1,m=()=>{h&&!l.length&&!c&&n.complete()},g=y=>c<r?v(y):l.push(y),v=y=>{i&&n.next(y),c++;let _=!1;_e(t(y,u++)).subscribe(Y(n,A=>{o?.(A),i?g(A):n.next(A)},()=>{_=!0},void 0,()=>{if(_)try{for(c--;l.length&&c<r;){let A=l.shift();s?Qe(n,s,()=>v(A)):v(A)}m()}catch(A){n.error(A)}}))};return e.subscribe(Y(n,g,()=>{h=!0,m()})),()=>{a?.()}}function Ee(e,n,t=1/0){return L(n)?Ee((r,o)=>B((i,s)=>n(r,i,o,s))(_e(e(r,o))),t):(typeof n=="number"&&(t=n),X((r,o)=>zh(r,o,e,t)))}function kn(e=1/0){return Ee(at,e)}function Gh(){return kn(1)}function Fr(...e){return Gh()(de(e,Tn(e)))}function Ps(e){return new K(n=>{_e(e()).subscribe(n)})}function cc(...e){let n=bs(e),{args:t,keys:r}=Rs(e),o=new K(i=>{let{length:s}=t;if(!s){i.complete();return}let a=new Array(s),l=s,c=s;for(let u=0;u<s;u++){let h=!1;_e(t[u]).subscribe(Y(i,m=>{h||(h=!0,c--),a[u]=m},()=>l--,void 0,()=>{(!l||!h)&&(c||i.next(r?Os(r,a):a),i.complete())}))}});return n?o.pipe(Ns(n)):o}function Je(e,n){return X((t,r)=>{let o=0;t.subscribe(Y(r,i=>e.call(n,i,o++)&&r.next(i)))})}function Et(e){return X((n,t)=>{let r=null,o=!1,i;r=n.subscribe(Y(t,void 0,void 0,s=>{i=_e(e(s,Et(e)(n))),r?(r.unsubscribe(),r=null,i.subscribe(t)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(t))})}function qh(e,n,t,r,o){return(i,s)=>{let a=t,l=n,c=0;i.subscribe(Y(s,u=>{let h=c++;l=a?e(l,u,h):(a=!0,u),r&&s.next(l)},o&&(()=>{a&&s.next(l),s.complete()})))}}function Xt(e,n){return L(n)?Ee(e,n,1):Ee(e,1)}function An(e){return X((n,t)=>{let r=!1;n.subscribe(Y(t,o=>{r=!0,t.next(o)},()=>{r||t.next(e),t.complete()}))})}function en(e){return e<=0?()=>Ye:X((n,t)=>{let r=0;n.subscribe(Y(t,o=>{++r<=e&&(t.next(o),e<=r&&t.complete())}))})}function Fs(e=sC){return X((n,t)=>{let r=!1;n.subscribe(Y(t,o=>{r=!0,t.next(o)},()=>r?t.complete():t.error(e())))})}function sC(){return new _t}function Jn(e){return X((n,t)=>{try{n.subscribe(t)}finally{t.add(e)}})}function tn(e,n){let t=arguments.length>=2;return r=>r.pipe(e?Je((o,i)=>e(o,i,r)):at,en(1),t?An(n):Fs(()=>new _t))}function Lr(e){return e<=0?()=>Ye:X((n,t)=>{let r=[];n.subscribe(Y(t,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)t.next(o);t.complete()},void 0,()=>{r=null}))})}function uc(e,n){let t=arguments.length>=2;return r=>r.pipe(e?Je((o,i)=>e(o,i,r)):at,Lr(1),t?An(n):Fs(()=>new _t))}function dc(e,n){return X(qh(e,n,arguments.length>=2,!0))}function fc(...e){let n=Tn(e);return X((t,r)=>{(n?Fr(e,t,n):Fr(e,t)).subscribe(r)})}function Ke(e,n){return X((t,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();t.subscribe(Y(r,l=>{o?.unsubscribe();let c=0,u=i++;_e(e(l,u)).subscribe(o=Y(r,h=>r.next(n?n(l,h,u,c++):h),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function hc(e){return X((n,t)=>{_e(e).subscribe(Y(t,()=>t.complete(),zo)),!t.closed&&n.subscribe(t)})}function ge(e,n,t){let r=L(e)||n||t?{next:e,error:n,complete:t}:e;return r?X((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(Y(i,l=>{var c;(c=r.next)===null||c===void 0||c.call(r,l),i.next(l)},()=>{var l;a=!1,(l=r.complete)===null||l===void 0||l.call(r),i.complete()},l=>{var c;a=!1,(c=r.error)===null||c===void 0||c.call(r,l),i.error(l)},()=>{var l,c;a&&((l=r.unsubscribe)===null||l===void 0||l.call(r)),(c=r.finalize)===null||c===void 0||c.call(r)}))}):at}var Pp="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",x=class extends Error{code;constructor(n,t){super(ya(n,t)),this.code=n}};function ya(e,n){return`${`NG0${Math.abs(e)}`}${n?": "+n:""}`}var Fp=Symbol("InputSignalNode#UNSET"),aC=I(b({},Yl),{transformFn:void 0,applyValueToInputSignal(e,n){hs(e,n)}});function Lp(e,n){let t=Object.create(aC);t.value=e,t.transformFn=n?.transform;function r(){if(ls(t),t.value===Fp)throw new x(-950,!1);return t.value}return r[st]=t,r}function ba(e){return{toString:e}.toString()}var nn=globalThis;function le(e){for(let n in e)if(e[n]===le)return n;throw Error("Could not find renamed property on target object.")}function lC(e,n){for(let t in n)n.hasOwnProperty(t)&&!e.hasOwnProperty(t)&&(e[t]=n[t])}function tt(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(tt).join(", ")}]`;if(e==null)return""+e;let n=e.overriddenName||e.name;if(n)return`${n}`;let t=e.toString();if(t==null)return""+t;let r=t.indexOf(`
`);return r>=0?t.slice(0,r):t}function Tc(e,n){return e?n?`${e} ${n}`:e:n||""}var cC=le({__forward_ref__:le});function un(e){return e.__forward_ref__=un,e.toString=function(){return tt(this())},e}function Ue(e){return Vp(e)?e():e}function Vp(e){return typeof e=="function"&&e.hasOwnProperty(cC)&&e.__forward_ref__===un}function S(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function dn(e){return{providers:e.providers||[],imports:e.imports||[]}}function Ca(e){return Wh(e,Bp)||Wh(e,$p)}function jp(e){return Ca(e)!==null}function Wh(e,n){return e.hasOwnProperty(n)?e[n]:null}function uC(e){let n=e&&(e[Bp]||e[$p]);return n||null}function Zh(e){return e&&(e.hasOwnProperty(Yh)||e.hasOwnProperty(dC))?e[Yh]:null}var Bp=le({\u0275prov:le}),Yh=le({\u0275inj:le}),$p=le({ngInjectableDef:le}),dC=le({ngInjectorDef:le}),M=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(n,t){this._desc=n,this.\u0275prov=void 0,typeof t=="number"?this.__NG_ELEMENT_ID__=t:t!==void 0&&(this.\u0275prov=S({token:this,providedIn:t.providedIn||"root",factory:t.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Up(e){return e&&!!e.\u0275providers}var fC=le({\u0275cmp:le}),hC=le({\u0275dir:le}),pC=le({\u0275pipe:le}),gC=le({\u0275mod:le}),Ws=le({\u0275fac:le}),Yo=le({__NG_ELEMENT_ID__:le}),Qh=le({__NG_ENV_ID__:le});function ii(e){return typeof e=="string"?e:e==null?"":String(e)}function mC(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():ii(e)}function Hp(e,n){throw new x(-200,e)}function Nu(e,n){throw new x(-201,!1)}var z=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(z||{}),kc;function zp(){return kc}function Xe(e){let n=kc;return kc=e,n}function Gp(e,n,t){let r=Ca(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(t&z.Optional)return null;if(n!==void 0)return n;Nu(e,"Injector")}var vC={},Xn=vC,yC="__NG_DI_FLAG__",Zs=class{injector;constructor(n){this.injector=n}retrieve(n,t){let r=t;return this.injector.get(n,r.optional?Jl:Xn,r)}},Ys="ngTempTokenPath",bC="ngTokenPath",CC=/\n/gm,wC="\u0275",Jh="__source";function DC(e,n=z.Default){if(Uo()===void 0)throw new x(-203,!1);if(Uo()===null)return Gp(e,void 0,n);{let t=Uo(),r;return t instanceof Zs?r=t.injector:r=t,r.get(e,n&z.Optional?null:void 0,n)}}function T(e,n=z.Default){return(zp()||DC)(Ue(e),n)}function w(e,n=z.Default){return T(e,wa(n))}function wa(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Ac(e){let n=[];for(let t=0;t<e.length;t++){let r=Ue(e[t]);if(Array.isArray(r)){if(r.length===0)throw new x(900,!1);let o,i=z.Default;for(let s=0;s<r.length;s++){let a=r[s],l=_C(a);typeof l=="number"?l===-1?o=a.token:i|=l:o=a}n.push(T(o,i))}else n.push(T(r))}return n}function _C(e){return e[yC]}function EC(e,n,t,r){let o=e[Ys];throw n[Jh]&&o.unshift(n[Jh]),e.message=xC(`
`+e.message,o,t,r),e[bC]=o,e[Ys]=null,e}function xC(e,n,t,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==wC?e.slice(2):e;let o=tt(n);if(Array.isArray(n))o=n.map(tt).join(" -> ");else if(typeof n=="object"){let i=[];for(let s in n)if(n.hasOwnProperty(s)){let a=n[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):tt(a)))}o=`{${i.join(", ")}}`}return`${t}${r?"("+r+")":""}[${o}]: ${e.replace(CC,`
  `)}`}function tr(e,n){let t=e.hasOwnProperty(Ws);return t?e[Ws]:null}function SC(e,n,t){if(e.length!==n.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=n[r];if(t&&(o=t(o),i=t(i)),i!==o)return!1}return!0}function MC(e){return e.flat(Number.POSITIVE_INFINITY)}function Ru(e,n){e.forEach(t=>Array.isArray(t)?Ru(t,n):n(t))}function qp(e,n,t){n>=e.length?e.push(t):e.splice(n,0,t)}function Qs(e,n){return n>=e.length-1?e.pop():e.splice(n,1)[0]}function IC(e,n){let t=[];for(let r=0;r<e;r++)t.push(n);return t}function TC(e,n,t,r){let o=e.length;if(o==n)e.push(t,r);else if(o===1)e.push(r,e[0]),e[0]=t;else{for(o--,e.push(e[o-1],e[o]);o>n;){let i=o-2;e[o]=e[i],o--}e[n]=t,e[n+1]=r}}function Ou(e,n,t){let r=si(e,n);return r>=0?e[r|1]=t:(r=~r,TC(e,r,n,t)),r}function pc(e,n){let t=si(e,n);if(t>=0)return e[t|1]}function si(e,n){return kC(e,n,1)}function kC(e,n,t){let r=0,o=e.length>>t;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<t];if(n===s)return i<<t;s>n?o=i:r=i+1}return~(o<<t)}var nr={},et=[],zr=new M(""),Wp=new M("",-1),Zp=new M(""),Js=class{get(n,t=Xn){if(t===Xn){let r=new Error(`NullInjectorError: No provider for ${tt(n)}!`);throw r.name="NullInjectorError",r}return t}};function Yp(e,n){let t=e[gC]||null;if(!t&&n===!0)throw new Error(`Type ${tt(e)} does not have '\u0275mod' property.`);return t}function rr(e){return e[fC]||null}function AC(e){return e[hC]||null}function NC(e){return e[pC]||null}function no(e){return{\u0275providers:e}}function RC(...e){return{\u0275providers:Qp(!0,e),\u0275fromNgModule:!0}}function Qp(e,...n){let t=[],r=new Set,o,i=s=>{t.push(s)};return Ru(n,s=>{let a=s;Nc(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Jp(o,i),t}function Jp(e,n){for(let t=0;t<e.length;t++){let{ngModule:r,providers:o}=e[t];Pu(o,i=>{n(i,r)})}}function Nc(e,n,t,r){if(e=Ue(e),!e)return!1;let o=null,i=Zh(e),s=!i&&rr(e);if(!i&&!s){let l=e.ngModule;if(i=Zh(l),i)o=l;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let l=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let c of l)Nc(c,n,t,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let c;try{Ru(i.imports,u=>{Nc(u,n,t,r)&&(c||=[],c.push(u))})}finally{}c!==void 0&&Jp(c,n)}if(!a){let c=tr(o)||(()=>new o);n({provide:o,useFactory:c,deps:et},o),n({provide:Zp,useValue:o,multi:!0},o),n({provide:zr,useValue:()=>T(o),multi:!0},o)}let l=i.providers;if(l!=null&&!a){let c=e;Pu(l,u=>{n(u,c)})}}else return!1;return o!==e&&e.providers!==void 0}function Pu(e,n){for(let t of e)Up(t)&&(t=t.\u0275providers),Array.isArray(t)?Pu(t,n):n(t)}var OC=le({provide:String,useValue:le});function Kp(e){return e!==null&&typeof e=="object"&&OC in e}function PC(e){return!!(e&&e.useExisting)}function FC(e){return!!(e&&e.useFactory)}function Gr(e){return typeof e=="function"}function LC(e){return!!e.useClass}var Da=new M(""),$s={},Kh={},gc;function Fu(){return gc===void 0&&(gc=new Js),gc}var He=class{},Qo=class extends He{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(n,t,r,o){super(),this.parent=t,this.source=r,this.scopes=o,Oc(n,s=>this.processProvider(s)),this.records.set(Wp,Vr(void 0,this)),o.has("environment")&&this.records.set(He,Vr(void 0,this));let i=this.records.get(Da);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Zp,et,z.Self))}retrieve(n,t){let r=t;return this.get(n,r.optional?Jl:Xn,r)}destroy(){Wo(this),this._destroyed=!0;let n=G(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let t=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of t)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),G(n)}}onDestroy(n){return Wo(this),this._onDestroyHooks.push(n),()=>this.removeOnDestroy(n)}runInContext(n){Wo(this);let t=Kt(this),r=Xe(void 0),o;try{return n()}finally{Kt(t),Xe(r)}}get(n,t=Xn,r=z.Default){if(Wo(this),n.hasOwnProperty(Qh))return n[Qh](this);r=wa(r);let o,i=Kt(this),s=Xe(void 0);try{if(!(r&z.SkipSelf)){let l=this.records.get(n);if(l===void 0){let c=UC(n)&&Ca(n);c&&this.injectableDefInScope(c)?l=Vr(Rc(n),$s):l=null,this.records.set(n,l)}if(l!=null)return this.hydrate(n,l)}let a=r&z.Self?Fu():this.parent;return t=r&z.Optional&&t===Xn?null:t,a.get(n,t)}catch(a){if(a.name==="NullInjectorError"){if((a[Ys]=a[Ys]||[]).unshift(tt(n)),i)throw a;return EC(a,n,"R3InjectorError",this.source)}else throw a}finally{Xe(s),Kt(i)}}resolveInjectorInitializers(){let n=G(null),t=Kt(this),r=Xe(void 0),o;try{let i=this.get(zr,et,z.Self);for(let s of i)s()}finally{Kt(t),Xe(r),G(n)}}toString(){let n=[],t=this.records;for(let r of t.keys())n.push(tt(r));return`R3Injector[${n.join(", ")}]`}processProvider(n){n=Ue(n);let t=Gr(n)?n:Ue(n&&n.provide),r=jC(n);if(!Gr(n)&&n.multi===!0){let o=this.records.get(t);o||(o=Vr(void 0,$s,!0),o.factory=()=>Ac(o.multi),this.records.set(t,o)),t=n,o.multi.push(n)}this.records.set(t,r)}hydrate(n,t){let r=G(null);try{return t.value===Kh?Hp(tt(n)):t.value===$s&&(t.value=Kh,t.value=t.factory()),typeof t.value=="object"&&t.value&&$C(t.value)&&this._ngOnDestroyHooks.add(t.value),t.value}finally{G(r)}}injectableDefInScope(n){if(!n.providedIn)return!1;let t=Ue(n.providedIn);return typeof t=="string"?t==="any"||this.scopes.has(t):this.injectorDefTypes.has(t)}removeOnDestroy(n){let t=this._onDestroyHooks.indexOf(n);t!==-1&&this._onDestroyHooks.splice(t,1)}};function Rc(e){let n=Ca(e),t=n!==null?n.factory:tr(e);if(t!==null)return t;if(e instanceof M)throw new x(204,!1);if(e instanceof Function)return VC(e);throw new x(204,!1)}function VC(e){if(e.length>0)throw new x(204,!1);let t=uC(e);return t!==null?()=>t.factory(e):()=>new e}function jC(e){if(Kp(e))return Vr(void 0,e.useValue);{let n=Xp(e);return Vr(n,$s)}}function Xp(e,n,t){let r;if(Gr(e)){let o=Ue(e);return tr(o)||Rc(o)}else if(Kp(e))r=()=>Ue(e.useValue);else if(FC(e))r=()=>e.useFactory(...Ac(e.deps||[]));else if(PC(e))r=()=>T(Ue(e.useExisting));else{let o=Ue(e&&(e.useClass||e.provide));if(BC(e))r=()=>new o(...Ac(e.deps));else return tr(o)||Rc(o)}return r}function Wo(e){if(e.destroyed)throw new x(205,!1)}function Vr(e,n,t=!1){return{factory:e,value:n,multi:t?[]:void 0}}function BC(e){return!!e.deps}function $C(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function UC(e){return typeof e=="function"||typeof e=="object"&&e instanceof M}function Oc(e,n){for(let t of e)Array.isArray(t)?Oc(t,n):t&&Up(t)?Oc(t.\u0275providers,n):n(t)}function nt(e,n){let t;e instanceof Qo?(Wo(e),t=e):t=new Zs(e);let r,o=Kt(t),i=Xe(void 0);try{return n()}finally{Kt(o),Xe(i)}}function eg(){return zp()!==void 0||Uo()!=null}function Lu(e){if(!eg())throw new x(-203,!1)}function HC(e){return typeof e=="function"}var fn=0,j=1,F=2,Le=3,Mt=4,rt=5,qr=6,Ks=7,Te=8,Jo=9,rn=10,be=11,Ko=12,Xh=13,ro=14,lt=15,or=16,jr=17,on=18,_a=19,tg=20,Nn=21,mc=22,ir=23,mt=24,Ur=25,Re=26,ng=1;var sr=7,Xs=8,Wr=9,Fe=10;function Rn(e){return Array.isArray(e)&&typeof e[ng]=="object"}function hn(e){return Array.isArray(e)&&e[ng]===!0}function rg(e){return(e.flags&4)!==0}function oo(e){return e.componentOffset>-1}function Vu(e){return(e.flags&1)===1}function jt(e){return!!e.template}function ea(e){return(e[F]&512)!==0}function ai(e){return(e[F]&256)===256}var Pc=class{previousValue;currentValue;firstChange;constructor(n,t,r){this.previousValue=n,this.currentValue=t,this.firstChange=r}isFirstChange(){return this.firstChange}};function og(e,n,t,r){n!==null?n.applyValueToInputSignal(n,r):e[t]=r}var Ht=(()=>{let e=()=>ig;return e.ngInherit=!0,e})();function ig(e){return e.type.prototype.ngOnChanges&&(e.setInput=GC),zC}function zC(){let e=ag(this),n=e?.current;if(n){let t=e.previous;if(t===nr)e.previous=n;else for(let r in n)t[r]=n[r];e.current=null,this.ngOnChanges(n)}}function GC(e,n,t,r,o){let i=this.declaredInputs[r],s=ag(e)||qC(e,{previous:nr,current:null}),a=s.current||(s.current={}),l=s.previous,c=l[i];a[i]=new Pc(c&&c.currentValue,t,l===nr),og(e,n,o,t)}var sg="__ngSimpleChanges__";function ag(e){return e[sg]||null}function qC(e,n){return e[sg]=n}var ep=null;var se=function(e,n=null,t){ep?.(e,n,t)},lg="svg",WC="math";function Bt(e){for(;Array.isArray(e);)e=e[fn];return e}function cg(e,n){return Bt(n[e])}function zt(e,n){return Bt(n[e.index])}function ju(e,n){return e.data[n]}function ug(e,n){return e[n]}function $t(e,n){let t=n[e];return Rn(t)?t:t[fn]}function ZC(e){return(e[F]&4)===4}function Bu(e){return(e[F]&128)===128}function YC(e){return hn(e[Le])}function Zr(e,n){return n==null?null:e[n]}function dg(e){e[jr]=0}function fg(e){e[F]&1024||(e[F]|=1024,Bu(e)&&io(e))}function QC(e,n){for(;e>0;)n=n[ro],e--;return n}function Ea(e){return!!(e[F]&9216||e[mt]?.dirty)}function Fc(e){e[rn].changeDetectionScheduler?.notify(8),e[F]&64&&(e[F]|=1024),Ea(e)&&io(e)}function io(e){e[rn].changeDetectionScheduler?.notify(0);let n=ar(e);for(;n!==null&&!(n[F]&8192||(n[F]|=8192,!Bu(n)));)n=ar(n)}function hg(e,n){if(ai(e))throw new x(911,!1);e[Nn]===null&&(e[Nn]=[]),e[Nn].push(n)}function JC(e,n){if(e[Nn]===null)return;let t=e[Nn].indexOf(n);t!==-1&&e[Nn].splice(t,1)}function ar(e){let n=e[Le];return hn(n)?n[Le]:n}function pg(e){return e[Ks]??=[]}function gg(e){return e.cleanup??=[]}function KC(e,n,t,r){let o=pg(n);o.push(t),e.firstCreatePass&&gg(e).push(r,o.length-1)}var $={lFrame:Sg(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var Lc=!1;function XC(){return $.lFrame.elementDepthCount}function ew(){$.lFrame.elementDepthCount++}function tw(){$.lFrame.elementDepthCount--}function mg(){return $.bindingsEnabled}function vg(){return $.skipHydrationRootTNode!==null}function nw(e){return $.skipHydrationRootTNode===e}function rw(){$.skipHydrationRootTNode=null}function H(){return $.lFrame.lView}function xe(){return $.lFrame.tView}function Q(e){return $.lFrame.contextLView=e,e[Te]}function J(e){return $.lFrame.contextLView=null,e}function Ge(){let e=yg();for(;e!==null&&e.type===64;)e=e.parent;return e}function yg(){return $.lFrame.currentTNode}function ow(){let e=$.lFrame,n=e.currentTNode;return e.isParent?n:n.parent}function li(e,n){let t=$.lFrame;t.currentTNode=e,t.isParent=n}function bg(){return $.lFrame.isParent}function Cg(){$.lFrame.isParent=!1}function iw(){return $.lFrame.contextLView}function wg(){return Lc}function ta(e){let n=Lc;return Lc=e,n}function $u(){let e=$.lFrame,n=e.bindingRootIndex;return n===-1&&(n=e.bindingRootIndex=e.tView.bindingStartIndex),n}function sw(e){return $.lFrame.bindingIndex=e}function so(){return $.lFrame.bindingIndex++}function Dg(e){let n=$.lFrame,t=n.bindingIndex;return n.bindingIndex=n.bindingIndex+e,t}function aw(){return $.lFrame.inI18n}function lw(e,n){let t=$.lFrame;t.bindingIndex=t.bindingRootIndex=e,Vc(n)}function cw(){return $.lFrame.currentDirectiveIndex}function Vc(e){$.lFrame.currentDirectiveIndex=e}function uw(e){let n=$.lFrame.currentDirectiveIndex;return n===-1?null:e[n]}function _g(){return $.lFrame.currentQueryIndex}function Uu(e){$.lFrame.currentQueryIndex=e}function dw(e){let n=e[j];return n.type===2?n.declTNode:n.type===1?e[rt]:null}function Eg(e,n,t){if(t&z.SkipSelf){let o=n,i=e;for(;o=o.parent,o===null&&!(t&z.Host);)if(o=dw(i),o===null||(i=i[ro],o.type&10))break;if(o===null)return!1;n=o,e=i}let r=$.lFrame=xg();return r.currentTNode=n,r.lView=e,!0}function Hu(e){let n=xg(),t=e[j];$.lFrame=n,n.currentTNode=t.firstChild,n.lView=e,n.tView=t,n.contextLView=e,n.bindingIndex=t.bindingStartIndex,n.inI18n=!1}function xg(){let e=$.lFrame,n=e===null?null:e.child;return n===null?Sg(e):n}function Sg(e){let n={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=n),n}function Mg(){let e=$.lFrame;return $.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Ig=Mg;function zu(){let e=Mg();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function fw(e){return($.lFrame.contextLView=QC(e,$.lFrame.contextLView))[Te]}function On(){return $.lFrame.selectedIndex}function lr(e){$.lFrame.selectedIndex=e}function Gu(){let e=$.lFrame;return ju(e.tView,e.selectedIndex)}function k(){$.lFrame.currentNamespace=lg}function P(){hw()}function hw(){$.lFrame.currentNamespace=null}function pw(){return $.lFrame.currentNamespace}var Tg=!0;function qu(){return Tg}function Wu(e){Tg=e}function gw(e,n,t){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=n.type.prototype;if(r){let s=ig(n);(t.preOrderHooks??=[]).push(e,s),(t.preOrderCheckHooks??=[]).push(e,s)}o&&(t.preOrderHooks??=[]).push(0-e,o),i&&((t.preOrderHooks??=[]).push(e,i),(t.preOrderCheckHooks??=[]).push(e,i))}function kg(e,n){for(let t=n.directiveStart,r=n.directiveEnd;t<r;t++){let i=e.data[t].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:l,ngAfterViewChecked:c,ngOnDestroy:u}=i;s&&(e.contentHooks??=[]).push(-t,s),a&&((e.contentHooks??=[]).push(t,a),(e.contentCheckHooks??=[]).push(t,a)),l&&(e.viewHooks??=[]).push(-t,l),c&&((e.viewHooks??=[]).push(t,c),(e.viewCheckHooks??=[]).push(t,c)),u!=null&&(e.destroyHooks??=[]).push(t,u)}}function Us(e,n,t){Ag(e,n,3,t)}function Hs(e,n,t,r){(e[F]&3)===t&&Ag(e,n,t,r)}function vc(e,n){let t=e[F];(t&3)===n&&(t&=16383,t+=1,e[F]=t)}function Ag(e,n,t,r){let o=r!==void 0?e[jr]&65535:0,i=r??-1,s=n.length-1,a=0;for(let l=o;l<s;l++)if(typeof n[l+1]=="number"){if(a=n[l],r!=null&&a>=r)break}else n[l]<0&&(e[jr]+=65536),(a<i||i==-1)&&(mw(e,t,n,l),e[jr]=(e[jr]&**********)+l+2),l++}function tp(e,n){se(4,e,n);let t=G(null);try{n.call(e)}finally{G(t),se(5,e,n)}}function mw(e,n,t,r){let o=t[r]<0,i=t[r+1],s=o?-t[r]:t[r],a=e[s];o?e[F]>>14<e[jr]>>16&&(e[F]&3)===n&&(e[F]+=16384,tp(a,i)):tp(a,i)}var Hr=-1,cr=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(n,t,r){this.factory=n,this.canSeeViewProviders=t,this.injectImpl=r}};function vw(e){return(e.flags&8)!==0}function yw(e){return(e.flags&16)!==0}function bw(e,n,t){let r=0;for(;r<t.length;){let o=t[r];if(typeof o=="number"){if(o!==0)break;r++;let i=t[r++],s=t[r++],a=t[r++];e.setAttribute(n,s,a,i)}else{let i=o,s=t[++r];Cw(i)?e.setProperty(n,i,s):e.setAttribute(n,i,s),r++}}return r}function Ng(e){return e===3||e===4||e===6}function Cw(e){return e.charCodeAt(0)===64}function Xo(e,n){if(!(n===null||n.length===0))if(e===null||e.length===0)e=n.slice();else{let t=-1;for(let r=0;r<n.length;r++){let o=n[r];typeof o=="number"?t=o:t===0||(t===-1||t===2?np(e,t,o,null,n[++r]):np(e,t,o,null,null))}}return e}function np(e,n,t,r,o){let i=0,s=e.length;if(n===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===n){s=-1;break}else if(a>n){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===t){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,n),i=s+1),e.splice(i++,0,t),o!==null&&e.splice(i++,0,o)}var yc={},jc=class{injector;parentInjector;constructor(n,t){this.injector=n,this.parentInjector=t}get(n,t,r){r=wa(r);let o=this.injector.get(n,yc,r);return o!==yc||t===yc?o:this.parentInjector.get(n,t,r)}};function Rg(e){return e!==Hr}function na(e){return e&32767}function ww(e){return e>>16}function ra(e,n){let t=ww(e),r=n;for(;t>0;)r=r[ro],t--;return r}var Bc=!0;function oa(e){let n=Bc;return Bc=e,n}var Dw=256,Og=Dw-1,Pg=5,_w=0,Vt={};function Ew(e,n,t){let r;typeof t=="string"?r=t.charCodeAt(0)||0:t.hasOwnProperty(Yo)&&(r=t[Yo]),r==null&&(r=t[Yo]=_w++);let o=r&Og,i=1<<o;n.data[e+(o>>Pg)]|=i}function ia(e,n){let t=Fg(e,n);if(t!==-1)return t;let r=n[j];r.firstCreatePass&&(e.injectorIndex=n.length,bc(r.data,e),bc(n,null),bc(r.blueprint,null));let o=Zu(e,n),i=e.injectorIndex;if(Rg(o)){let s=na(o),a=ra(o,n),l=a[j].data;for(let c=0;c<8;c++)n[i+c]=a[s+c]|l[s+c]}return n[i+8]=o,i}function bc(e,n){e.push(0,0,0,0,0,0,0,0,n)}function Fg(e,n){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||n[e.injectorIndex+8]===null?-1:e.injectorIndex}function Zu(e,n){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let t=0,r=null,o=n;for(;o!==null;){if(r=$g(o),r===null)return Hr;if(t++,o=o[ro],r.injectorIndex!==-1)return r.injectorIndex|t<<16}return Hr}function $c(e,n,t){Ew(e,n,t)}function xw(e,n){if(n==="class")return e.classes;if(n==="style")return e.styles;let t=e.attrs;if(t){let r=t.length,o=0;for(;o<r;){let i=t[o];if(Ng(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof t[o]=="string";)o++;else{if(i===n)return t[o+1];o=o+2}}}return null}function Lg(e,n,t){if(t&z.Optional||e!==void 0)return e;Nu(n,"NodeInjector")}function Vg(e,n,t,r){if(t&z.Optional&&r===void 0&&(r=null),(t&(z.Self|z.Host))===0){let o=e[Jo],i=Xe(void 0);try{return o?o.get(n,r,t&z.Optional):Gp(n,r,t&z.Optional)}finally{Xe(i)}}return Lg(r,n,t)}function jg(e,n,t,r=z.Default,o){if(e!==null){if(n[F]&2048&&!(r&z.Self)){let s=Tw(e,n,t,r,Vt);if(s!==Vt)return s}let i=Bg(e,n,t,r,Vt);if(i!==Vt)return i}return Vg(n,t,r,o)}function Bg(e,n,t,r,o){let i=Mw(t);if(typeof i=="function"){if(!Eg(n,e,r))return r&z.Host?Lg(o,t,r):Vg(n,t,r,o);try{let s;if(s=i(r),s==null&&!(r&z.Optional))Nu(t);else return s}finally{Ig()}}else if(typeof i=="number"){let s=null,a=Fg(e,n),l=Hr,c=r&z.Host?n[lt][rt]:null;for((a===-1||r&z.SkipSelf)&&(l=a===-1?Zu(e,n):n[a+8],l===Hr||!op(r,!1)?a=-1:(s=n[j],a=na(l),n=ra(l,n)));a!==-1;){let u=n[j];if(rp(i,a,u.data)){let h=Sw(a,n,t,s,r,c);if(h!==Vt)return h}l=n[a+8],l!==Hr&&op(r,n[j].data[a+8]===c)&&rp(i,a,n)?(s=u,a=na(l),n=ra(l,n)):a=-1}}return o}function Sw(e,n,t,r,o,i){let s=n[j],a=s.data[e+8],l=r==null?oo(a)&&Bc:r!=s&&(a.type&3)!==0,c=o&z.Host&&i===a,u=zs(a,s,t,l,c);return u!==null?ei(n,s,u,a):Vt}function zs(e,n,t,r,o){let i=e.providerIndexes,s=n.data,a=i&1048575,l=e.directiveStart,c=e.directiveEnd,u=i>>20,h=r?a:a+u,m=o?a+u:c;for(let g=h;g<m;g++){let v=s[g];if(g<l&&t===v||g>=l&&v.type===t)return g}if(o){let g=s[l];if(g&&jt(g)&&g.type===t)return l}return null}function ei(e,n,t,r){let o=e[t],i=n.data;if(o instanceof cr){let s=o;s.resolving&&Hp(mC(i[t]));let a=oa(s.canSeeViewProviders);s.resolving=!0;let l,c=s.injectImpl?Xe(s.injectImpl):null,u=Eg(e,r,z.Default);try{o=e[t]=s.factory(void 0,i,e,r),n.firstCreatePass&&t>=r.directiveStart&&gw(t,i[t],n)}finally{c!==null&&Xe(c),oa(a),s.resolving=!1,Ig()}}return o}function Mw(e){if(typeof e=="string")return e.charCodeAt(0)||0;let n=e.hasOwnProperty(Yo)?e[Yo]:void 0;return typeof n=="number"?n>=0?n&Og:Iw:n}function rp(e,n,t){let r=1<<e;return!!(t[n+(e>>Pg)]&r)}function op(e,n){return!(e&z.Self)&&!(e&z.Host&&n)}var er=class{_tNode;_lView;constructor(n,t){this._tNode=n,this._lView=t}get(n,t,r){return jg(this._tNode,this._lView,n,wa(r),t)}};function Iw(){return new er(Ge(),H())}function Pn(e){return ba(()=>{let n=e.prototype.constructor,t=n[Ws]||Uc(n),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[Ws]||Uc(o);if(i&&i!==t)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Uc(e){return Vp(e)?()=>{let n=Uc(Ue(e));return n&&n()}:tr(e)}function Tw(e,n,t,r,o){let i=e,s=n;for(;i!==null&&s!==null&&s[F]&2048&&!ea(s);){let a=Bg(i,s,t,r|z.Self,Vt);if(a!==Vt)return a;let l=i.parent;if(!l){let c=s[tg];if(c){let u=c.get(t,Vt,r);if(u!==Vt)return u}l=$g(s),s=s[ro]}i=l}return o}function $g(e){let n=e[j],t=n.type;return t===2?n.declTNode:t===1?e[rt]:null}function Yu(e){return xw(Ge(),e)}function ip(e,n=null,t=null,r){let o=Ug(e,n,t,r);return o.resolveInjectorInitializers(),o}function Ug(e,n=null,t=null,r,o=new Set){let i=[t||et,RC(e)];return r=r||(typeof e=="object"?void 0:tt(e)),new Qo(i,n||Fu(),r||null,o)}var ze=class e{static THROW_IF_NOT_FOUND=Xn;static NULL=new Js;static create(n,t){if(Array.isArray(n))return ip({name:""},t,n,"");{let r=n.name??"";return ip({name:r},n.parent,n.providers,r)}}static \u0275prov=S({token:e,providedIn:"any",factory:()=>T(Wp)});static __NG_ELEMENT_ID__=-1};var kw=new M("");kw.__NG_ELEMENT_ID__=e=>{let n=Ge();if(n===null)throw new x(204,!1);if(n.type&2)return n.value;if(e&z.Optional)return null;throw new x(204,!1)};var Hg=!1,ao=(()=>{class e{static __NG_ELEMENT_ID__=Aw;static __NG_ENV_ID__=t=>t}return e})(),sa=class extends ao{_lView;constructor(n){super(),this._lView=n}onDestroy(n){return hg(this._lView,n),()=>JC(this._lView,n)}};function Aw(){return new sa(H())}var ur=class{},xa=new M("",{providedIn:"root",factory:()=>!1});var zg=new M(""),Gg=new M(""),Fn=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new De(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let t=this.taskId++;return this.pendingTasks.add(t),t}has(t){return this.pendingTasks.has(t)}remove(t){this.pendingTasks.delete(t),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=S({token:e,providedIn:"root",factory:()=>new e})}return e})();var Hc=class extends pe{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(n=!1){super(),this.__isAsync=n,eg()&&(this.destroyRef=w(ao,{optional:!0})??void 0,this.pendingTasks=w(Fn,{optional:!0})??void 0)}emit(n){let t=G(null);try{super.next(n)}finally{G(t)}}subscribe(n,t,r){let o=n,i=t||(()=>null),s=r;if(n&&typeof n=="object"){let l=n;o=l.next?.bind(l),i=l.error?.bind(l),s=l.complete?.bind(l)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return n instanceof ie&&n.add(a),a}wrapInTimeout(n){return t=>{let r=this.pendingTasks?.add();setTimeout(()=>{n(t),r!==void 0&&this.pendingTasks?.remove(r)})}}},me=Hc;function ti(...e){}function qg(e){let n,t;function r(){e=ti;try{t!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(t),n!==void 0&&clearTimeout(n)}catch{}}return n=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(t=requestAnimationFrame(()=>{e(),r()})),()=>r()}function sp(e){return queueMicrotask(()=>e()),()=>{e=ti}}var Qu="isAngularZone",aa=Qu+"_ID",Nw=0,ve=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new me(!1);onMicrotaskEmpty=new me(!1);onStable=new me(!1);onError=new me(!1);constructor(n){let{enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=Hg}=n;if(typeof Zone>"u")throw new x(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,Pw(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Qu)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new x(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new x(909,!1)}run(n,t,r){return this._inner.run(n,t,r)}runTask(n,t,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,n,Rw,ti,ti);try{return i.runTask(s,t,r)}finally{i.cancelTask(s)}}runGuarded(n,t,r){return this._inner.runGuarded(n,t,r)}runOutsideAngular(n){return this._outer.run(n)}},Rw={};function Ju(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Ow(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function n(){qg(()=>{e.callbackScheduled=!1,zc(e),e.isCheckStableRunning=!0,Ju(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{n()}):e._outer.run(()=>{n()}),zc(e)}function Pw(e){let n=()=>{Ow(e)},t=Nw++;e._inner=e._inner.fork({name:"angular",properties:{[Qu]:!0,[aa]:t,[aa+t]:!0},onInvokeTask:(r,o,i,s,a,l)=>{if(Fw(l))return r.invokeTask(i,s,a,l);try{return ap(e),r.invokeTask(i,s,a,l)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&n(),lp(e)}},onInvoke:(r,o,i,s,a,l,c)=>{try{return ap(e),r.invoke(i,s,a,l,c)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!Lw(l)&&n(),lp(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,zc(e),Ju(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function zc(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function ap(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function lp(e){e._nesting--,Ju(e)}var Gc=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new me;onMicrotaskEmpty=new me;onStable=new me;onError=new me;run(n,t,r){return n.apply(t,r)}runGuarded(n,t,r){return n.apply(t,r)}runOutsideAngular(n){return n()}runTask(n,t,r,o){return n.apply(t,r)}};function Fw(e){return Wg(e,"__ignore_ng_zone__")}function Lw(e){return Wg(e,"__scheduler_tick__")}function Wg(e,n){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[n]===!0}var sn=class{_console=console;handleError(n){this._console.error("ERROR",n)}},Vw=new M("",{providedIn:"root",factory:()=>{let e=w(ve),n=w(sn);return t=>e.runOutsideAngular(()=>n.handleError(t))}});function cp(e,n){return Lp(e,n)}function jw(e){return Lp(Fp,e)}var Zg=(cp.required=jw,cp);function Bw(){return lo(Ge(),H())}function lo(e,n){return new ct(zt(e,n))}var ct=(()=>{class e{nativeElement;constructor(t){this.nativeElement=t}static __NG_ELEMENT_ID__=Bw}return e})();function $w(e){return e instanceof ct?e.nativeElement:e}function Uw(e){return typeof e=="function"&&e[st]!==void 0}function ut(e,n){let t=wh(e),r=t[st];return n?.equal&&(r.equal=n.equal),t.set=o=>hs(r,o),t.update=o=>Dh(r,o),t.asReadonly=Hw.bind(t),t}function Hw(){let e=this[st];if(e.readonlyFn===void 0){let n=()=>this();n[st]=e,e.readonlyFn=n}return e.readonlyFn}function Yg(e){return Uw(e)&&typeof e.set=="function"}function zw(){return this._results[Symbol.iterator]()}var qc=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new pe}constructor(n=!1){this._emitDistinctChangesOnly=n}get(n){return this._results[n]}map(n){return this._results.map(n)}filter(n){return this._results.filter(n)}find(n){return this._results.find(n)}reduce(n,t){return this._results.reduce(n,t)}forEach(n){this._results.forEach(n)}some(n){return this._results.some(n)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(n,t){this.dirty=!1;let r=MC(n);(this._changesDetected=!SC(this._results,r,t))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(n){this._onDirty=n}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=zw};function Qg(e){return(e.flags&128)===128}var Jg=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Jg||{}),Kg=new Map,Gw=0;function qw(){return Gw++}function Ww(e){Kg.set(e[_a],e)}function Wc(e){Kg.delete(e[_a])}var up="__ngContext__";function ci(e,n){Rn(n)?(e[up]=n[_a],Ww(n)):e[up]=n}function Xg(e){return tm(e[Ko])}function em(e){return tm(e[Mt])}function tm(e){for(;e!==null&&!hn(e);)e=e[Mt];return e}var Zc;function nm(e){Zc=e}function rm(){if(Zc!==void 0)return Zc;if(typeof document<"u")return document;throw new x(210,!1)}var Ku=new M("",{providedIn:"root",factory:()=>Zw}),Zw="ng",Xu=new M(""),fr=new M("",{providedIn:"platform",factory:()=>"unknown"});var ed=new M("",{providedIn:"root",factory:()=>rm().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var Yw="h",Qw="b";var om=!1,Jw=new M("",{providedIn:"root",factory:()=>om});var td=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(td||{}),co=new M(""),dp=new Set;function hr(e){dp.has(e)||(dp.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var nd=(()=>{class e{view;node;constructor(t,r){this.view=t,this.node=r}static __NG_ELEMENT_ID__=Kw}return e})();function Kw(){return new nd(H(),Ge())}var Br=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(Br||{}),im=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=S({token:e,providedIn:"root",factory:()=>new e})}return e})(),Xw=[Br.EarlyRead,Br.Write,Br.MixedReadWrite,Br.Read],eD=(()=>{class e{ngZone=w(ve);scheduler=w(ur);errorHandler=w(sn,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){w(co,{optional:!0})}execute(){let t=this.sequences.size>0;t&&se(16),this.executing=!0;for(let r of Xw)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),t&&se(17)}register(t){let{view:r}=t;r!==void 0?((r[Ur]??=[]).push(t),io(r),r[F]|=8192):this.executing?this.deferredRegistrations.add(t):this.addSequence(t)}addSequence(t){this.sequences.add(t),this.scheduler.notify(7)}unregister(t){this.executing&&this.sequences.has(t)?(t.erroredOrDestroyed=!0,t.pipelinedValue=void 0,t.once=!0):(this.sequences.delete(t),this.deferredRegistrations.delete(t))}maybeTrace(t,r){return r?r.run(td.AFTER_NEXT_RENDER,t):t()}static \u0275prov=S({token:e,providedIn:"root",factory:()=>new e})}return e})(),Yc=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(n,t,r,o,i,s=null){this.impl=n,this.hooks=t,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let n=this.view?.[Ur];n&&(this.view[Ur]=n.filter(t=>t!==this))}};function rd(e,n){!n?.injector&&Lu(rd);let t=n?.injector??w(ze);return hr("NgAfterNextRender"),nD(e,t,n,!0)}function tD(e,n){if(e instanceof Function){let t=[void 0,void 0,void 0,void 0];return t[n]=e,t}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function nD(e,n,t,r){let o=n.get(im);o.impl??=n.get(eD);let i=n.get(co,null,{optional:!0}),s=t?.phase??Br.MixedReadWrite,a=t?.manualCleanup!==!0?n.get(ao):null,l=n.get(nd,null,{optional:!0}),c=new Yc(o.impl,tD(e,s),l?.view,r,a,i?.snapshot(null));return o.impl.register(c),c}var rD=()=>null;function sm(e,n,t=!1){return rD(e,n,t)}function am(e,n){let t=e.contentQueries;if(t!==null){let r=G(null);try{for(let o=0;o<t.length;o+=2){let i=t[o],s=t[o+1];if(s!==-1){let a=e.data[s];Uu(i),a.contentQueries(2,n[s],s)}}}finally{G(r)}}}function Qc(e,n,t){Uu(0);let r=G(null);try{n(e,t)}finally{G(r)}}function lm(e,n,t){if(rg(n)){let r=G(null);try{let o=n.directiveStart,i=n.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let l=t[s];a.contentQueries(1,l,s)}}}finally{G(r)}}}var Ut=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Ut||{}),Ls;function oD(){if(Ls===void 0&&(Ls=null,nn.trustedTypes))try{Ls=nn.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Ls}function Sa(e){return oD()?.createHTML(e)||e}var Vs;function cm(){if(Vs===void 0&&(Vs=null,nn.trustedTypes))try{Vs=nn.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Vs}function fp(e){return cm()?.createHTML(e)||e}function hp(e){return cm()?.createScriptURL(e)||e}var an=class{changingThisBreaksApplicationSecurity;constructor(n){this.changingThisBreaksApplicationSecurity=n}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Pp})`}},Jc=class extends an{getTypeName(){return"HTML"}},Kc=class extends an{getTypeName(){return"Style"}},Xc=class extends an{getTypeName(){return"Script"}},eu=class extends an{getTypeName(){return"URL"}},tu=class extends an{getTypeName(){return"ResourceURL"}};function vt(e){return e instanceof an?e.changingThisBreaksApplicationSecurity:e}function pn(e,n){let t=iD(e);if(t!=null&&t!==n){if(t==="ResourceURL"&&n==="URL")return!0;throw new Error(`Required a safe ${n}, got a ${t} (see ${Pp})`)}return t===n}function iD(e){return e instanceof an&&e.getTypeName()||null}function um(e){return new Jc(e)}function dm(e){return new Kc(e)}function fm(e){return new Xc(e)}function hm(e){return new eu(e)}function pm(e){return new tu(e)}function sD(e){let n=new ru(e);return aD()?new nu(n):n}var nu=class{inertDocumentHelper;constructor(n){this.inertDocumentHelper=n}getInertBodyElement(n){n="<body><remove></remove>"+n;try{let t=new window.DOMParser().parseFromString(Sa(n),"text/html").body;return t===null?this.inertDocumentHelper.getInertBodyElement(n):(t.firstChild?.remove(),t)}catch{return null}}},ru=class{defaultDoc;inertDocument;constructor(n){this.defaultDoc=n,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(n){let t=this.inertDocument.createElement("template");return t.innerHTML=Sa(n),t}};function aD(){try{return!!new window.DOMParser().parseFromString(Sa(""),"text/html")}catch{return!1}}var lD=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Ma(e){return e=String(e),e.match(lD)?e:"unsafe:"+e}function gn(e){let n={};for(let t of e.split(","))n[t]=!0;return n}function ui(...e){let n={};for(let t of e)for(let r in t)t.hasOwnProperty(r)&&(n[r]=!0);return n}var gm=gn("area,br,col,hr,img,wbr"),mm=gn("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),vm=gn("rp,rt"),cD=ui(vm,mm),uD=ui(mm,gn("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),dD=ui(vm,gn("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),pp=ui(gm,uD,dD,cD),ym=gn("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),fD=gn("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),hD=gn("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),pD=ui(ym,fD,hD),gD=gn("script,style,template"),ou=class{sanitizedSomething=!1;buf=[];sanitizeChildren(n){let t=n.firstChild,r=!0,o=[];for(;t;){if(t.nodeType===Node.ELEMENT_NODE?r=this.startElement(t):t.nodeType===Node.TEXT_NODE?this.chars(t.nodeValue):this.sanitizedSomething=!0,r&&t.firstChild){o.push(t),t=yD(t);continue}for(;t;){t.nodeType===Node.ELEMENT_NODE&&this.endElement(t);let i=vD(t);if(i){t=i;break}t=o.pop()}}return this.buf.join("")}startElement(n){let t=gp(n).toLowerCase();if(!pp.hasOwnProperty(t))return this.sanitizedSomething=!0,!gD.hasOwnProperty(t);this.buf.push("<"),this.buf.push(t);let r=n.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!pD.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let l=i.value;ym[a]&&(l=Ma(l)),this.buf.push(" ",s,'="',mp(l),'"')}return this.buf.push(">"),!0}endElement(n){let t=gp(n).toLowerCase();pp.hasOwnProperty(t)&&!gm.hasOwnProperty(t)&&(this.buf.push("</"),this.buf.push(t),this.buf.push(">"))}chars(n){this.buf.push(mp(n))}};function mD(e,n){return(e.compareDocumentPosition(n)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function vD(e){let n=e.nextSibling;if(n&&e!==n.previousSibling)throw bm(n);return n}function yD(e){let n=e.firstChild;if(n&&mD(e,n))throw bm(n);return n}function gp(e){let n=e.nodeName;return typeof n=="string"?n:"FORM"}function bm(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var bD=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,CD=/([^\#-~ |!])/g;function mp(e){return e.replace(/&/g,"&amp;").replace(bD,function(n){let t=n.charCodeAt(0),r=n.charCodeAt(1);return"&#"+((t-55296)*1024+(r-56320)+65536)+";"}).replace(CD,function(n){return"&#"+n.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var js;function od(e,n){let t=null;try{js=js||sD(e);let r=n?String(n):"";t=js.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=t.innerHTML,t=js.getInertBodyElement(r)}while(r!==i);let a=new ou().sanitizeChildren(vp(t)||t);return Sa(a)}finally{if(t){let r=vp(t)||t;for(;r.firstChild;)r.firstChild.remove()}}}function vp(e){return"content"in e&&wD(e)?e.content:null}function wD(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var Tt=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Tt||{});function uo(e){let n=id();return n?fp(n.sanitize(Tt.HTML,e)||""):pn(e,"HTML")?fp(vt(e)):od(rm(),ii(e))}function DD(e){let n=id();return n?n.sanitize(Tt.URL,e)||"":pn(e,"URL")?vt(e):Ma(ii(e))}function _D(e){let n=id();if(n)return hp(n.sanitize(Tt.RESOURCE_URL,e)||"");if(pn(e,"ResourceURL"))return hp(vt(e));throw new x(904,!1)}function ED(e,n){return n==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||n==="href"&&(e==="base"||e==="link")?_D:DD}function Cm(e,n,t){return ED(n,t)(e)}function id(){let e=H();return e&&e[rn].sanitizer}function wm(e){return e instanceof Function?e():e}function xD(e,n,t){let r=e.length;for(;;){let o=e.indexOf(n,t);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=n.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}t=o+1}}var Dm="ng-template";function SD(e,n,t,r){let o=0;if(r){for(;o<n.length&&typeof n[o]=="string";o+=2)if(n[o]==="class"&&xD(n[o+1].toLowerCase(),t,0)!==-1)return!0}else if(sd(e))return!1;if(o=n.indexOf(1,o),o>-1){let i;for(;++o<n.length&&typeof(i=n[o])=="string";)if(i.toLowerCase()===t)return!0}return!1}function sd(e){return e.type===4&&e.value!==Dm}function MD(e,n,t){let r=e.type===4&&!t?Dm:e.value;return n===r}function ID(e,n,t){let r=4,o=e.attrs,i=o!==null?AD(o):0,s=!1;for(let a=0;a<n.length;a++){let l=n[a];if(typeof l=="number"){if(!s&&!xt(r)&&!xt(l))return!1;if(s&&xt(l))continue;s=!1,r=l|r&1;continue}if(!s)if(r&4){if(r=2|r&1,l!==""&&!MD(e,l,t)||l===""&&n.length===1){if(xt(r))return!1;s=!0}}else if(r&8){if(o===null||!SD(e,o,l,t)){if(xt(r))return!1;s=!0}}else{let c=n[++a],u=TD(l,o,sd(e),t);if(u===-1){if(xt(r))return!1;s=!0;continue}if(c!==""){let h;if(u>i?h="":h=o[u+1].toLowerCase(),r&2&&c!==h){if(xt(r))return!1;s=!0}}}}return xt(r)||s}function xt(e){return(e&1)===0}function TD(e,n,t,r){if(n===null)return-1;let o=0;if(r||!t){let i=!1;for(;o<n.length;){let s=n[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=n[++o];for(;typeof a=="string";)a=n[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return ND(n,e)}function _m(e,n,t=!1){for(let r=0;r<n.length;r++)if(ID(e,n[r],t))return!0;return!1}function kD(e){let n=e.attrs;if(n!=null){let t=n.indexOf(5);if((t&1)===0)return n[t+1]}return null}function AD(e){for(let n=0;n<e.length;n++){let t=e[n];if(Ng(t))return n}return e.length}function ND(e,n){let t=e.indexOf(4);if(t>-1)for(t++;t<e.length;){let r=e[t];if(typeof r=="number")return-1;if(r===n)return t;t++}return-1}function RD(e,n){e:for(let t=0;t<n.length;t++){let r=n[t];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function yp(e,n){return e?":not("+n.trim()+")":n}function OD(e){let n=e[0],t=1,r=2,o="",i=!1;for(;t<e.length;){let s=e[t];if(typeof s=="string")if(r&2){let a=e[++t];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!xt(s)&&(n+=yp(i,o),o=""),r=s,i=i||!xt(r);t++}return o!==""&&(n+=yp(i,o)),n}function PD(e){return e.map(OD).join(",")}function FD(e){let n=[],t=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&n.push(i,e[++r]):o===8&&t.push(i);else{if(!xt(o))break;o=i}r++}return t.length&&n.push(1,...t),n}var kt={};function LD(e,n){return e.createText(n)}function VD(e,n,t){e.setValue(n,t)}function Em(e,n,t){return e.createElement(n,t)}function la(e,n,t,r,o){e.insertBefore(n,t,r,o)}function xm(e,n,t){e.appendChild(n,t)}function bp(e,n,t,r,o){r!==null?la(e,n,t,r,o):xm(e,n,t)}function jD(e,n,t){e.removeChild(null,n,t)}function BD(e,n,t){e.setAttribute(n,"style",t)}function $D(e,n,t){t===""?e.removeAttribute(n,"class"):e.setAttribute(n,"class",t)}function Sm(e,n,t){let{mergedAttrs:r,classes:o,styles:i}=t;r!==null&&bw(e,n,r),o!==null&&$D(e,n,o),i!==null&&BD(e,n,i)}function ad(e,n,t,r,o,i,s,a,l,c,u){let h=Re+r,m=h+o,g=UD(h,m),v=typeof c=="function"?c():c;return g[j]={type:e,blueprint:g,template:t,queries:null,viewQuery:a,declTNode:n,data:g.slice().fill(null,h),bindingStartIndex:h,expandoStartIndex:m,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:l,consts:v,incompleteFirstPass:!1,ssrId:u}}function UD(e,n){let t=[];for(let r=0;r<n;r++)t.push(r<e?null:kt);return t}function HD(e){let n=e.tView;return n===null||n.incompleteFirstPass?e.tView=ad(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):n}function ld(e,n,t,r,o,i,s,a,l,c,u){let h=n.blueprint.slice();return h[fn]=o,h[F]=r|4|128|8|64|1024,(c!==null||e&&e[F]&2048)&&(h[F]|=2048),dg(h),h[Le]=h[ro]=e,h[Te]=t,h[rn]=s||e&&e[rn],h[be]=a||e&&e[be],h[Jo]=l||e&&e[Jo]||null,h[rt]=i,h[_a]=qw(),h[qr]=u,h[tg]=c,h[lt]=n.type==2?e[lt]:h,h}function zD(e,n,t){let r=zt(n,e),o=HD(t),i=e[rn].rendererFactory,s=cd(e,ld(e,o,null,Mm(t),r,n,null,i.createRenderer(r,t),null,null,null));return e[n.index]=s}function Mm(e){let n=16;return e.signals?n=4096:e.onPush&&(n=64),n}function Im(e,n,t,r){if(t===0)return-1;let o=n.length;for(let i=0;i<t;i++)n.push(r),e.blueprint.push(r),e.data.push(null);return o}function cd(e,n){return e[Ko]?e[Xh][Mt]=n:e[Ko]=n,e[Xh]=n,n}function C(e=1){Tm(xe(),H(),On()+e,!1)}function Tm(e,n,t,r){if(!r)if((n[F]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Us(n,i,t)}else{let i=e.preOrderHooks;i!==null&&Hs(n,i,0,t)}lr(t)}var Ia=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Ia||{});function iu(e,n,t,r){let o=G(null);try{let[i,s,a]=e.inputs[t],l=null;(s&Ia.SignalBased)!==0&&(l=n[i][st]),l!==null&&l.transformFn!==void 0?r=l.transformFn(r):a!==null&&(r=a.call(n,r)),e.setInput!==null?e.setInput(n,l,r,t,i):og(n,l,i,r)}finally{G(o)}}function km(e,n,t,r,o){let i=On(),s=r&2;try{lr(-1),s&&n.length>Re&&Tm(e,n,Re,!1),se(s?2:0,o),t(r,o)}finally{lr(i),se(s?3:1,o)}}function ud(e,n,t){QD(e,n,t),(t.flags&64)===64&&JD(e,n,t)}function Am(e,n,t=zt){let r=n.localNames;if(r!==null){let o=n.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?t(n,e):e[s];e[o++]=a}}}function GD(e,n,t,r){let i=r.get(Jw,om)||t===Ut.ShadowDom,s=e.selectRootElement(n,i);return qD(s),s}function qD(e){WD(e)}var WD=()=>null;function ZD(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Nm(e,n,t,r,o,i,s,a){if(!a&&dd(n,e,t,r,o)){oo(n)&&YD(t,n.index);return}if(n.type&3){let l=zt(n,t);r=ZD(r),o=s!=null?s(o,n.value||"",r):o,i.setProperty(l,r,o)}else n.type&12}function YD(e,n){let t=$t(n,e);t[F]&16||(t[F]|=64)}function QD(e,n,t){let r=t.directiveStart,o=t.directiveEnd;oo(t)&&zD(n,t,e.data[r+t.componentOffset]),e.firstCreatePass||ia(t,n);let i=t.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],l=ei(n,e,s,t);if(ci(l,n),i!==null&&t_(n,s-r,l,a,t,i),jt(a)){let c=$t(t.index,n);c[Te]=ei(n,e,s,t)}}}function JD(e,n,t){let r=t.directiveStart,o=t.directiveEnd,i=t.index,s=cw();try{lr(i);for(let a=r;a<o;a++){let l=e.data[a],c=n[a];Vc(a),(l.hostBindings!==null||l.hostVars!==0||l.hostAttrs!==null)&&KD(l,c)}}finally{lr(-1),Vc(s)}}function KD(e,n){e.hostBindings!==null&&e.hostBindings(1,n)}function Rm(e,n){let t=e.directiveRegistry,r=null;if(t)for(let o=0;o<t.length;o++){let i=t[o];_m(n,i.selectors,!1)&&(r??=[],jt(i)?r.unshift(i):r.push(i))}return r}function XD(e,n,t,r,o,i){let s=zt(e,n);e_(n[be],s,i,e.value,t,r,o)}function e_(e,n,t,r,o,i,s){if(i==null)e.removeAttribute(n,o,t);else{let a=s==null?ii(i):s(i,r||"",o);e.setAttribute(n,o,a,t)}}function t_(e,n,t,r,o,i){let s=i[n];if(s!==null)for(let a=0;a<s.length;a+=2){let l=s[a],c=s[a+1];iu(r,t,l,c)}}function Om(e,n){let t=e[Jo],r=t?t.get(sn,null):null;r&&r.handleError(n)}function dd(e,n,t,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let l=0;l<s.length;l+=2){let c=s[l],u=s[l+1],h=n.data[c];iu(h,t[c],u,o),a=!0}if(i)for(let l of i){let c=t[l],u=n.data[l];iu(u,c,r,o),a=!0}return a}function n_(e,n){let t=$t(n,e),r=t[j];r_(r,t);let o=t[fn];o!==null&&t[qr]===null&&(t[qr]=sm(o,t[Jo])),se(18),fd(r,t,t[Te]),se(19,t[Te])}function r_(e,n){for(let t=n.length;t<e.blueprint.length;t++)n.push(e.blueprint[t])}function fd(e,n,t){Hu(n);try{let r=e.viewQuery;r!==null&&Qc(1,r,t);let o=e.template;o!==null&&km(e,n,o,1,t),e.firstCreatePass&&(e.firstCreatePass=!1),n[on]?.finishViewCreation(e),e.staticContentQueries&&am(e,n),e.staticViewQueries&&Qc(2,e.viewQuery,t);let i=e.components;i!==null&&o_(n,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{n[F]&=-5,zu()}}function o_(e,n){for(let t=0;t<n.length;t++)n_(e,n[t])}function di(e,n,t,r){let o=G(null);try{let i=n.tView,a=e[F]&4096?4096:16,l=ld(e,i,t,a,null,n,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),c=e[n.index];l[or]=c;let u=e[on];return u!==null&&(l[on]=u.createEmbeddedView(i)),fd(i,l,t),l}finally{G(o)}}function Yr(e,n){return!n||n.firstChild===null||Qg(e)}var i_;function hd(e,n){return i_(e,n)}var ln=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(ln||{});function pd(e){return(e.flags&32)===32}function $r(e,n,t,r,o){if(r!=null){let i,s=!1;hn(r)?i=r:Rn(r)&&(s=!0,r=r[fn]);let a=Bt(r);e===0&&t!==null?o==null?xm(n,t,a):la(n,t,a,o||null,!0):e===1&&t!==null?la(n,t,a,o||null,!0):e===2?jD(n,a,s):e===3&&n.destroyNode(a),i!=null&&g_(n,e,i,t,o)}}function s_(e,n){Pm(e,n),n[fn]=null,n[rt]=null}function a_(e,n,t,r,o,i){r[fn]=o,r[rt]=n,ka(e,r,t,1,o,i)}function Pm(e,n){n[rn].changeDetectionScheduler?.notify(9),ka(e,n,n[be],2,null,null)}function l_(e){let n=e[Ko];if(!n)return Cc(e[j],e);for(;n;){let t=null;if(Rn(n))t=n[Ko];else{let r=n[Fe];r&&(t=r)}if(!t){for(;n&&!n[Mt]&&n!==e;)Rn(n)&&Cc(n[j],n),n=n[Le];n===null&&(n=e),Rn(n)&&Cc(n[j],n),t=n&&n[Mt]}n=t}}function gd(e,n){let t=e[Wr],r=t.indexOf(n);t.splice(r,1)}function Ta(e,n){if(ai(n))return;let t=n[be];t.destroyNode&&ka(e,n,t,3,null,null),l_(n)}function Cc(e,n){if(ai(n))return;let t=G(null);try{n[F]&=-129,n[F]|=256,n[mt]&&$o(n[mt]),u_(e,n),c_(e,n),n[j].type===1&&n[be].destroy();let r=n[or];if(r!==null&&hn(n[Le])){r!==n[Le]&&gd(r,n);let o=n[on];o!==null&&o.detachView(e)}Wc(n)}finally{G(t)}}function c_(e,n){let t=e.cleanup,r=n[Ks];if(t!==null)for(let s=0;s<t.length-1;s+=2)if(typeof t[s]=="string"){let a=t[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[t[s+1]];t[s].call(a)}r!==null&&(n[Ks]=null);let o=n[Nn];if(o!==null){n[Nn]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=n[ir];if(i!==null){n[ir]=null;for(let s of i)s.destroy()}}function u_(e,n){let t;if(e!=null&&(t=e.destroyHooks)!=null)for(let r=0;r<t.length;r+=2){let o=n[t[r]];if(!(o instanceof cr)){let i=t[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],l=i[s+1];se(4,a,l);try{l.call(a)}finally{se(5,a,l)}}else{se(4,o,i);try{i.call(o)}finally{se(5,o,i)}}}}}function Fm(e,n,t){return d_(e,n.parent,t)}function d_(e,n,t){let r=n;for(;r!==null&&r.type&168;)n=r,r=n.parent;if(r===null)return t[fn];if(oo(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===Ut.None||o===Ut.Emulated)return null}return zt(r,t)}function Lm(e,n,t){return h_(e,n,t)}function f_(e,n,t){return e.type&40?zt(e,t):null}var h_=f_,Cp;function md(e,n,t,r){let o=Fm(e,r,n),i=n[be],s=r.parent||n[rt],a=Lm(s,r,n);if(o!=null)if(Array.isArray(t))for(let l=0;l<t.length;l++)bp(i,o,t[l],a,!1);else bp(i,o,t,a,!1);Cp!==void 0&&Cp(i,r,n,t,o)}function Zo(e,n){if(n!==null){let t=n.type;if(t&3)return zt(n,e);if(t&4)return su(-1,e[n.index]);if(t&8){let r=n.child;if(r!==null)return Zo(e,r);{let o=e[n.index];return hn(o)?su(-1,o):Bt(o)}}else{if(t&128)return Zo(e,n.next);if(t&32)return hd(n,e)()||Bt(e[n.index]);{let r=Vm(e,n);if(r!==null){if(Array.isArray(r))return r[0];let o=ar(e[lt]);return Zo(o,r)}else return Zo(e,n.next)}}}return null}function Vm(e,n){if(n!==null){let r=e[lt][rt],o=n.projection;return r.projection[o]}return null}function su(e,n){let t=Fe+e+1;if(t<n.length){let r=n[t],o=r[j].firstChild;if(o!==null)return Zo(r,o)}return n[sr]}function vd(e,n,t,r,o,i,s){for(;t!=null;){if(t.type===128){t=t.next;continue}let a=r[t.index],l=t.type;if(s&&n===0&&(a&&ci(Bt(a),r),t.flags|=2),!pd(t))if(l&8)vd(e,n,t.child,r,o,i,!1),$r(n,e,o,a,i);else if(l&32){let c=hd(t,r),u;for(;u=c();)$r(n,e,o,u,i);$r(n,e,o,a,i)}else l&16?jm(e,n,r,t,o,i):$r(n,e,o,a,i);t=s?t.projectionNext:t.next}}function ka(e,n,t,r,o,i){vd(t,r,e.firstChild,n,o,i,!1)}function p_(e,n,t){let r=n[be],o=Fm(e,t,n),i=t.parent||n[rt],s=Lm(i,t,n);jm(r,0,n,t,o,s)}function jm(e,n,t,r,o,i){let s=t[lt],l=s[rt].projection[r.projection];if(Array.isArray(l))for(let c=0;c<l.length;c++){let u=l[c];$r(n,e,o,u,i)}else{let c=l,u=s[Le];Qg(r)&&(c.flags|=128),vd(e,n,c,u,o,i,!0)}}function g_(e,n,t,r,o){let i=t[sr],s=Bt(t);i!==s&&$r(n,e,r,i,o);for(let a=Fe;a<t.length;a++){let l=t[a];ka(l[j],l,e,n,r,i)}}function m_(e,n,t,r,o){if(n)o?e.addClass(t,r):e.removeClass(t,r);else{let i=r.indexOf("-")===-1?void 0:ln.DashCase;o==null?e.removeStyle(t,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=ln.Important),e.setStyle(t,r,o,i))}}function ca(e,n,t,r,o=!1){for(;t!==null;){if(t.type===128){t=o?t.projectionNext:t.next;continue}let i=n[t.index];i!==null&&r.push(Bt(i)),hn(i)&&v_(i,r);let s=t.type;if(s&8)ca(e,n,t.child,r);else if(s&32){let a=hd(t,n),l;for(;l=a();)r.push(l)}else if(s&16){let a=Vm(n,t);if(Array.isArray(a))r.push(...a);else{let l=ar(n[lt]);ca(l[j],l,a,r,!0)}}t=o?t.projectionNext:t.next}return r}function v_(e,n){for(let t=Fe;t<e.length;t++){let r=e[t],o=r[j].firstChild;o!==null&&ca(r[j],r,o,n)}e[sr]!==e[fn]&&n.push(e[sr])}function Bm(e){if(e[Ur]!==null){for(let n of e[Ur])n.impl.addSequence(n);e[Ur].length=0}}var $m=[];function y_(e){return e[mt]??b_(e)}function b_(e){let n=$m.pop()??Object.create(w_);return n.lView=e,n}function C_(e){e.lView[mt]!==e&&(e.lView=null,$m.push(e))}var w_=I(b({},Mr),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{io(e.lView)},consumerOnSignalRead(){this.lView[mt]=this}});function D_(e){let n=e[mt]??Object.create(__);return n.lView=e,n}var __=I(b({},Mr),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let n=ar(e.lView);for(;n&&!Um(n[j]);)n=ar(n);n&&fg(n)},consumerOnSignalRead(){this.lView[mt]=this}});function Um(e){return e.type!==2}function Hm(e){if(e[ir]===null)return;let n=!0;for(;n;){let t=!1;for(let r of e[ir])r.dirty&&(t=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));n=t&&!!(e[F]&8192)}}var E_=100;function zm(e,n=!0,t=0){let o=e[rn].rendererFactory,i=!1;i||o.begin?.();try{x_(e,t)}catch(s){throw n&&Om(e,s),s}finally{i||o.end?.()}}function x_(e,n){let t=wg();try{ta(!0),au(e,n);let r=0;for(;Ea(e);){if(r===E_)throw new x(103,!1);r++,au(e,1)}}finally{ta(t)}}function S_(e,n,t,r){if(ai(n))return;let o=n[F],i=!1,s=!1;Hu(n);let a=!0,l=null,c=null;i||(Um(e)?(c=y_(n),l=Bo(c)):uh()===null?(a=!1,c=D_(n),l=Bo(c)):n[mt]&&($o(n[mt]),n[mt]=null));try{dg(n),sw(e.bindingStartIndex),t!==null&&km(e,n,t,2,r);let u=(o&3)===3;if(!i)if(u){let g=e.preOrderCheckHooks;g!==null&&Us(n,g,null)}else{let g=e.preOrderHooks;g!==null&&Hs(n,g,0,null),vc(n,0)}if(s||M_(n),Hm(n),Gm(n,0),e.contentQueries!==null&&am(e,n),!i)if(u){let g=e.contentCheckHooks;g!==null&&Us(n,g)}else{let g=e.contentHooks;g!==null&&Hs(n,g,1),vc(n,1)}T_(e,n);let h=e.components;h!==null&&Wm(n,h,0);let m=e.viewQuery;if(m!==null&&Qc(2,m,r),!i)if(u){let g=e.viewCheckHooks;g!==null&&Us(n,g)}else{let g=e.viewHooks;g!==null&&Hs(n,g,2),vc(n,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),n[mc]){for(let g of n[mc])g();n[mc]=null}i||(Bm(n),n[F]&=-73)}catch(u){throw i||io(n),u}finally{c!==null&&(cs(c,l),a&&C_(c)),zu()}}function Gm(e,n){for(let t=Xg(e);t!==null;t=em(t))for(let r=Fe;r<t.length;r++){let o=t[r];qm(o,n)}}function M_(e){for(let n=Xg(e);n!==null;n=em(n)){if(!(n[F]&2))continue;let t=n[Wr];for(let r=0;r<t.length;r++){let o=t[r];fg(o)}}}function I_(e,n,t){se(18);let r=$t(n,e);qm(r,t),se(19,r[Te])}function qm(e,n){Bu(e)&&au(e,n)}function au(e,n){let r=e[j],o=e[F],i=e[mt],s=!!(n===0&&o&16);if(s||=!!(o&64&&n===0),s||=!!(o&1024),s||=!!(i?.dirty&&us(i)),s||=!1,i&&(i.dirty=!1),e[F]&=-9217,s)S_(r,e,r.template,e[Te]);else if(o&8192){Hm(e),Gm(e,1);let a=r.components;a!==null&&Wm(e,a,1),Bm(e)}}function Wm(e,n,t){for(let r=0;r<n.length;r++)I_(e,n[r],t)}function T_(e,n){let t=e.hostBindingOpCodes;if(t!==null)try{for(let r=0;r<t.length;r++){let o=t[r];if(o<0)lr(~o);else{let i=o,s=t[++r],a=t[++r];lw(s,i);let l=n[i];se(24,l),a(2,l),se(25,l)}}}finally{lr(-1)}}function yd(e,n){let t=wg()?64:1088;for(e[rn].changeDetectionScheduler?.notify(n);e;){e[F]|=t;let r=ar(e);if(ea(e)&&!r)return e;e=r}return null}function Zm(e,n,t,r){return[e,!0,0,n,null,r,null,t,null,null]}function Ym(e,n){let t=Fe+n;if(t<e.length)return e[t]}function fi(e,n,t,r=!0){let o=n[j];if(k_(o,n,e,t),r){let s=su(t,e),a=n[be],l=a.parentNode(e[sr]);l!==null&&a_(o,e[rt],a,n,l,s)}let i=n[qr];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Qm(e,n){let t=ni(e,n);return t!==void 0&&Ta(t[j],t),t}function ni(e,n){if(e.length<=Fe)return;let t=Fe+n,r=e[t];if(r){let o=r[or];o!==null&&o!==e&&gd(o,r),n>0&&(e[t-1][Mt]=r[Mt]);let i=Qs(e,Fe+n);s_(r[j],r);let s=i[on];s!==null&&s.detachView(i[j]),r[Le]=null,r[Mt]=null,r[F]&=-129}return r}function k_(e,n,t,r){let o=Fe+r,i=t.length;r>0&&(t[o-1][Mt]=n),r<i-Fe?(n[Mt]=t[o],qp(t,Fe+r,n)):(t.push(n),n[Mt]=null),n[Le]=t;let s=n[or];s!==null&&t!==s&&Jm(s,n);let a=n[on];a!==null&&a.insertView(e),Fc(n),n[F]|=128}function Jm(e,n){let t=e[Wr],r=n[Le];if(Rn(r))e[F]|=2;else{let o=r[Le][lt];n[lt]!==o&&(e[F]|=2)}t===null?e[Wr]=[n]:t.push(n)}var ri=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let n=this._lView,t=n[j];return ca(t,n,t.firstChild,[])}constructor(n,t,r=!0){this._lView=n,this._cdRefInjectingView=t,this.notifyErrorHandler=r}get context(){return this._lView[Te]}set context(n){this._lView[Te]=n}get destroyed(){return ai(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let n=this._lView[Le];if(hn(n)){let t=n[Xs],r=t?t.indexOf(this):-1;r>-1&&(ni(n,r),Qs(t,r))}this._attachedToViewContainer=!1}Ta(this._lView[j],this._lView)}onDestroy(n){hg(this._lView,n)}markForCheck(){yd(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[F]&=-129}reattach(){Fc(this._lView),this._lView[F]|=128}detectChanges(){this._lView[F]|=1024,zm(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new x(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let n=ea(this._lView),t=this._lView[or];t!==null&&!n&&gd(t,this._lView),Pm(this._lView[j],this._lView)}attachToAppRef(n){if(this._attachedToViewContainer)throw new x(902,!1);this._appRef=n;let t=ea(this._lView),r=this._lView[or];r!==null&&!t&&Jm(r,this._lView),Fc(this._lView)}};var Qr=(()=>{class e{static __NG_ELEMENT_ID__=R_}return e})(),A_=Qr,N_=class extends A_{_declarationLView;_declarationTContainer;elementRef;constructor(n,t,r){super(),this._declarationLView=n,this._declarationTContainer=t,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(n,t){return this.createEmbeddedViewImpl(n,t)}createEmbeddedViewImpl(n,t,r){let o=di(this._declarationLView,this._declarationTContainer,n,{embeddedViewInjector:t,dehydratedView:r});return new ri(o)}};function R_(){return bd(Ge(),H())}function bd(e,n){return e.type&4?new N_(n,e,lo(e,n)):null}function Aa(e,n,t,r,o){let i=e.data[n];if(i===null)i=O_(e,n,t,r,o),aw()&&(i.flags|=32);else if(i.type&64){i.type=t,i.value=r,i.attrs=o;let s=ow();i.injectorIndex=s===null?-1:s.injectorIndex}return li(i,!0),i}function O_(e,n,t,r,o){let i=yg(),s=bg(),a=s?i:i&&i.parent,l=e.data[n]=F_(e,a,t,n,r,o);return P_(e,l,i,s),l}function P_(e,n,t,r){e.firstChild===null&&(e.firstChild=n),t!==null&&(r?t.child==null&&n.parent!==null&&(t.child=n):t.next===null&&(t.next=n,n.prev=t))}function F_(e,n,t,r,o,i){let s=n?n.injectorIndex:-1,a=0;return vg()&&(a|=128),{type:t,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:n,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var V2=new RegExp(`^(\\d+)*(${Qw}|${Yw})*(.*)`);var L_=()=>null;function Jr(e,n){return L_(e,n)}var V_=class{},Km=class{},lu=class{resolveComponentFactory(n){throw Error(`No component factory found for ${tt(n)}.`)}},Na=class{static NULL=new lu},Kr=class{},mn=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>j_()}return e})();function j_(){let e=H(),n=Ge(),t=$t(n.index,e);return(Rn(t)?t:e)[be]}var B_=(()=>{class e{static \u0275prov=S({token:e,providedIn:"root",factory:()=>null})}return e})();function wp(e,n,t){let r=t?e.styles:null,o=t?e.classes:null,i=0;if(n!==null)for(let s=0;s<n.length;s++){let a=n[s];if(typeof a=="number")i=a;else if(i==1)o=Tc(o,a);else if(i==2){let l=a,c=n[++s];r=Tc(r,l+": "+c+";")}}t?e.styles=r:e.stylesWithoutHost=r,t?e.classes=o:e.classesWithoutHost=o}function E(e,n=z.Default){let t=H();if(t===null)return T(e,n);let r=Ge();return jg(r,t,Ue(e),n)}function Xm(){let e="invalid";throw new Error(e)}function ev(e,n,t,r,o){let i=r===null?null:{"":-1},s=o(e,t);if(s!==null){let a,l=null,c=null,u=U_(s);u===null?a=s:[a,l,c]=u,G_(e,n,t,a,i,l,c)}i!==null&&r!==null&&$_(t,r,i)}function $_(e,n,t){let r=e.localNames=[];for(let o=0;o<n.length;o+=2){let i=t[n[o+1]];if(i==null)throw new x(-301,!1);r.push(n[o],i)}}function U_(e){let n=null,t=!1;for(let s=0;s<e.length;s++){let a=e[s];if(s===0&&jt(a)&&(n=a),a.findHostDirectiveDefs!==null){t=!0;break}}if(!t)return null;let r=null,o=null,i=null;for(let s of e)s.findHostDirectiveDefs!==null&&(r??=[],o??=new Map,i??=new Map,H_(s,r,i,o)),s===n&&(r??=[],r.push(s));return r!==null?(r.push(...n===null?e:e.slice(1)),[r,o,i]):null}function H_(e,n,t,r){let o=n.length;e.findHostDirectiveDefs(e,n,r),t.set(e,[o,n.length-1])}function z_(e,n,t){n.componentOffset=t,(e.components??=[]).push(n.index)}function G_(e,n,t,r,o,i,s){let a=r.length,l=!1;for(let m=0;m<a;m++){let g=r[m];!l&&jt(g)&&(l=!0,z_(e,t,m)),$c(ia(t,n),e,g.type)}J_(t,e.data.length,a);for(let m=0;m<a;m++){let g=r[m];g.providersResolver&&g.providersResolver(g)}let c=!1,u=!1,h=Im(e,n,a,null);a>0&&(t.directiveToIndex=new Map);for(let m=0;m<a;m++){let g=r[m];if(t.mergedAttrs=Xo(t.mergedAttrs,g.hostAttrs),W_(e,t,n,h,g),Q_(h,g,o),s!==null&&s.has(g)){let[y,_]=s.get(g);t.directiveToIndex.set(g.type,[h,y+t.directiveStart,_+t.directiveStart])}else(i===null||!i.has(g))&&t.directiveToIndex.set(g.type,h);g.contentQueries!==null&&(t.flags|=4),(g.hostBindings!==null||g.hostAttrs!==null||g.hostVars!==0)&&(t.flags|=64);let v=g.type.prototype;!c&&(v.ngOnChanges||v.ngOnInit||v.ngDoCheck)&&((e.preOrderHooks??=[]).push(t.index),c=!0),!u&&(v.ngOnChanges||v.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(t.index),u=!0),h++}q_(e,t,i)}function q_(e,n,t){for(let r=n.directiveStart;r<n.directiveEnd;r++){let o=e.data[r];if(t===null||!t.has(o))Dp(0,n,o,r),Dp(1,n,o,r),Ep(n,r,!1);else{let i=t.get(o);_p(0,n,i,r),_p(1,n,i,r),Ep(n,r,!0)}}}function Dp(e,n,t,r){let o=e===0?t.inputs:t.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=n.inputs??={}:s=n.outputs??={},s[i]??=[],s[i].push(r),tv(n,i)}}function _p(e,n,t,r){let o=e===0?t.inputs:t.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=n.hostDirectiveInputs??={}:a=n.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),tv(n,s)}}function tv(e,n){n==="class"?e.flags|=8:n==="style"&&(e.flags|=16)}function Ep(e,n,t){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!t&&o===null||t&&i===null||sd(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let l=r[a];if(l===0){a+=4;continue}else if(l===5){a+=2;continue}else if(typeof l=="number")break;if(!t&&o.hasOwnProperty(l)){let c=o[l];for(let u of c)if(u===n){s??=[],s.push(l,r[a+1]);break}}else if(t&&i.hasOwnProperty(l)){let c=i[l];for(let u=0;u<c.length;u+=2)if(c[u]===n){s??=[],s.push(c[u+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function W_(e,n,t,r,o){e.data[r]=o;let i=o.factory||(o.factory=tr(o.type,!0)),s=new cr(i,jt(o),E);e.blueprint[r]=s,t[r]=s,Z_(e,n,r,Im(e,t,o.hostVars,kt),o)}function Z_(e,n,t,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~n.index;Y_(s)!=a&&s.push(a),s.push(t,r,i)}}function Y_(e){let n=e.length;for(;n>0;){let t=e[--n];if(typeof t=="number"&&t<0)return t}return 0}function Q_(e,n,t){if(t){if(n.exportAs)for(let r=0;r<n.exportAs.length;r++)t[n.exportAs[r]]=e;jt(n)&&(t[""]=e)}}function J_(e,n,t){e.flags|=1,e.directiveStart=n,e.directiveEnd=n+t,e.providerIndexes=n}function nv(e,n,t,r,o,i,s,a){let l=n.consts,c=Zr(l,s),u=Aa(n,e,2,r,c);return i&&ev(n,t,u,Zr(l,a),o),u.mergedAttrs=Xo(u.mergedAttrs,u.attrs),u.attrs!==null&&wp(u,u.attrs,!1),u.mergedAttrs!==null&&wp(u,u.mergedAttrs,!0),n.queries!==null&&n.queries.elementStart(n,u),u}function rv(e,n){kg(e,n),rg(n)&&e.queries.elementEnd(n)}var ua=class extends Na{ngModule;constructor(n){super(),this.ngModule=n}resolveComponentFactory(n){let t=rr(n);return new Xr(t,this.ngModule)}};function K_(e){return Object.keys(e).map(n=>{let[t,r,o]=e[n],i={propName:t,templateName:n,isSignal:(r&Ia.SignalBased)!==0};return o&&(i.transform=o),i})}function X_(e){return Object.keys(e).map(n=>({propName:e[n],templateName:n}))}function eE(e,n,t){let r=n instanceof He?n:n?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new jc(t,r):t}function tE(e){let n=e.get(Kr,null);if(n===null)throw new x(407,!1);let t=e.get(B_,null),r=e.get(ur,null);return{rendererFactory:n,sanitizer:t,changeDetectionScheduler:r}}function nE(e,n){let t=(e.selectors[0][0]||"div").toLowerCase();return Em(n,t,t==="svg"?lg:t==="math"?WC:null)}var Xr=class extends Km{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=K_(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=X_(this.componentDef.outputs),this.cachedOutputs}constructor(n,t){super(),this.componentDef=n,this.ngModule=t,this.componentType=n.type,this.selector=PD(n.selectors),this.ngContentSelectors=n.ngContentSelectors??[],this.isBoundToModule=!!t}create(n,t,r,o){se(22);let i=G(null);try{let s=this.componentDef,a=r?["ng-version","19.2.2"]:FD(this.componentDef.selectors[0]),l=ad(0,null,null,1,0,null,null,null,null,[a],null),c=eE(s,o||this.ngModule,n),u=tE(c),h=u.rendererFactory.createRenderer(null,s),m=r?GD(h,r,s.encapsulation,c):nE(s,h),g=ld(null,l,null,512|Mm(s),null,null,u,h,c,null,sm(m,c,!0));g[Re]=m,Hu(g);let v=null;try{let y=nv(Re,l,g,"#host",()=>[this.componentDef],!0,0);m&&(Sm(h,m,y),ci(m,g)),ud(l,g,y),lm(l,y,g),rv(l,y),t!==void 0&&rE(y,this.ngContentSelectors,t),v=$t(y.index,g),g[Te]=v[Te],fd(l,g,null)}catch(y){throw v!==null&&Wc(v),Wc(g),y}finally{se(23),zu()}return new cu(this.componentType,g)}finally{G(i)}}},cu=class extends V_{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(n,t){super(),this._rootLView=t,this._tNode=ju(t[j],Re),this.location=lo(this._tNode,t),this.instance=$t(this._tNode.index,t)[Te],this.hostView=this.changeDetectorRef=new ri(t,void 0,!1),this.componentType=n}setInput(n,t){let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(n)&&Object.is(this.previousInputValues.get(n),t))return;let o=this._rootLView,i=dd(r,o[j],o,n,t);this.previousInputValues.set(n,t);let s=$t(r.index,o);yd(s,1)}get injector(){return new er(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(n){this.hostView.onDestroy(n)}};function rE(e,n,t){let r=e.projection=[];for(let o=0;o<n.length;o++){let i=t[o];r.push(i!=null&&i.length?Array.from(i):null)}}var pr=(()=>{class e{static __NG_ELEMENT_ID__=oE}return e})();function oE(){let e=Ge();return iv(e,H())}var iE=pr,ov=class extends iE{_lContainer;_hostTNode;_hostLView;constructor(n,t,r){super(),this._lContainer=n,this._hostTNode=t,this._hostLView=r}get element(){return lo(this._hostTNode,this._hostLView)}get injector(){return new er(this._hostTNode,this._hostLView)}get parentInjector(){let n=Zu(this._hostTNode,this._hostLView);if(Rg(n)){let t=ra(n,this._hostLView),r=na(n),o=t[j].data[r+8];return new er(o,t)}else return new er(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(n){let t=xp(this._lContainer);return t!==null&&t[n]||null}get length(){return this._lContainer.length-Fe}createEmbeddedView(n,t,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Jr(this._lContainer,n.ssrId),a=n.createEmbeddedViewImpl(t||{},i,s);return this.insertImpl(a,o,Yr(this._hostTNode,s)),a}createComponent(n,t,r,o,i){let s=n&&!HC(n),a;if(s)a=t;else{let v=t||{};a=v.index,r=v.injector,o=v.projectableNodes,i=v.environmentInjector||v.ngModuleRef}let l=s?n:new Xr(rr(n)),c=r||this.parentInjector;if(!i&&l.ngModule==null){let y=(s?c:this.parentInjector).get(He,null);y&&(i=y)}let u=rr(l.componentType??{}),h=Jr(this._lContainer,u?.id??null),m=h?.firstChild??null,g=l.create(c,o,m,i);return this.insertImpl(g.hostView,a,Yr(this._hostTNode,h)),g}insert(n,t){return this.insertImpl(n,t,!0)}insertImpl(n,t,r){let o=n._lView;if(YC(o)){let a=this.indexOf(n);if(a!==-1)this.detach(a);else{let l=o[Le],c=new ov(l,l[rt],l[Le]);c.detach(c.indexOf(n))}}let i=this._adjustIndex(t),s=this._lContainer;return fi(s,o,i,r),n.attachToViewContainerRef(),qp(wc(s),i,n),n}move(n,t){return this.insert(n,t)}indexOf(n){let t=xp(this._lContainer);return t!==null?t.indexOf(n):-1}remove(n){let t=this._adjustIndex(n,-1),r=ni(this._lContainer,t);r&&(Qs(wc(this._lContainer),t),Ta(r[j],r))}detach(n){let t=this._adjustIndex(n,-1),r=ni(this._lContainer,t);return r&&Qs(wc(this._lContainer),t)!=null?new ri(r):null}_adjustIndex(n,t=0){return n??this.length+t}};function xp(e){return e[Xs]}function wc(e){return e[Xs]||(e[Xs]=[])}function iv(e,n){let t,r=n[e.index];return hn(r)?t=r:(t=Zm(r,n,null,e),n[e.index]=t,cd(n,t)),aE(t,n,e,r),new ov(t,e,n)}function sE(e,n){let t=e[be],r=t.createComment(""),o=zt(n,e),i=t.parentNode(o);return la(t,i,r,t.nextSibling(o),!1),r}var aE=uE,lE=()=>!1;function cE(e,n,t){return lE(e,n,t)}function uE(e,n,t,r){if(e[sr])return;let o;t.type&8?o=Bt(r):o=sE(n,t),e[sr]=o}var uu=class e{queryList;matches=null;constructor(n){this.queryList=n}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},du=class e{queries;constructor(n=[]){this.queries=n}createEmbeddedView(n){let t=n.queries;if(t!==null){let r=n.contentQueries!==null?n.contentQueries[0]:t.length,o=[];for(let i=0;i<r;i++){let s=t.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(n){this.dirtyQueriesWithMatches(n)}detachView(n){this.dirtyQueriesWithMatches(n)}finishViewCreation(n){this.dirtyQueriesWithMatches(n)}dirtyQueriesWithMatches(n){for(let t=0;t<this.queries.length;t++)Cd(n,t).matches!==null&&this.queries[t].setDirty()}},da=class{flags;read;predicate;constructor(n,t,r=null){this.flags=t,this.read=r,typeof n=="string"?this.predicate=yE(n):this.predicate=n}},fu=class e{queries;constructor(n=[]){this.queries=n}elementStart(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(n,t)}elementEnd(n){for(let t=0;t<this.queries.length;t++)this.queries[t].elementEnd(n)}embeddedTView(n){let t=null;for(let r=0;r<this.length;r++){let o=t!==null?t.length:0,i=this.getByIndex(r).embeddedTView(n,o);i&&(i.indexInDeclarationView=r,t!==null?t.push(i):t=[i])}return t!==null?new e(t):null}template(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].template(n,t)}getByIndex(n){return this.queries[n]}get length(){return this.queries.length}track(n){this.queries.push(n)}},hu=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(n,t=-1){this.metadata=n,this._declarationNodeIndex=t}elementStart(n,t){this.isApplyingToNode(t)&&this.matchTNode(n,t)}elementEnd(n){this._declarationNodeIndex===n.index&&(this._appliesToNextNode=!1)}template(n,t){this.elementStart(n,t)}embeddedTView(n,t){return this.isApplyingToNode(n)?(this.crossesNgTemplate=!0,this.addMatch(-n.index,t),new e(this.metadata)):null}isApplyingToNode(n){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let t=this._declarationNodeIndex,r=n.parent;for(;r!==null&&r.type&8&&r.index!==t;)r=r.parent;return t===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(n,t){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(n,t,dE(t,i)),this.matchTNodeWithReadOption(n,t,zs(t,n,i,!1,!1))}else r===Qr?t.type&4&&this.matchTNodeWithReadOption(n,t,-1):this.matchTNodeWithReadOption(n,t,zs(t,n,r,!1,!1))}matchTNodeWithReadOption(n,t,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===ct||o===pr||o===Qr&&t.type&4)this.addMatch(t.index,-2);else{let i=zs(t,n,o,!1,!1);i!==null&&this.addMatch(t.index,i)}else this.addMatch(t.index,r)}}addMatch(n,t){this.matches===null?this.matches=[n,t]:this.matches.push(n,t)}};function dE(e,n){let t=e.localNames;if(t!==null){for(let r=0;r<t.length;r+=2)if(t[r]===n)return t[r+1]}return null}function fE(e,n){return e.type&11?lo(e,n):e.type&4?bd(e,n):null}function hE(e,n,t,r){return t===-1?fE(n,e):t===-2?pE(e,n,r):ei(e,e[j],t,n)}function pE(e,n,t){if(t===ct)return lo(n,e);if(t===Qr)return bd(n,e);if(t===pr)return iv(n,e)}function sv(e,n,t,r){let o=n[on].queries[r];if(o.matches===null){let i=e.data,s=t.matches,a=[];for(let l=0;s!==null&&l<s.length;l+=2){let c=s[l];if(c<0)a.push(null);else{let u=i[c];a.push(hE(n,u,s[l+1],t.metadata.read))}}o.matches=a}return o.matches}function pu(e,n,t,r){let o=e.queries.getByIndex(t),i=o.matches;if(i!==null){let s=sv(e,n,o,t);for(let a=0;a<i.length;a+=2){let l=i[a];if(l>0)r.push(s[a/2]);else{let c=i[a+1],u=n[-l];for(let h=Fe;h<u.length;h++){let m=u[h];m[or]===m[Le]&&pu(m[j],m,c,r)}if(u[Wr]!==null){let h=u[Wr];for(let m=0;m<h.length;m++){let g=h[m];pu(g[j],g,c,r)}}}}}return r}function gE(e,n){return e[on].queries[n].queryList}function av(e,n,t){let r=new qc((t&4)===4);return KC(e,n,r,r.destroy),(n[on]??=new du).queries.push(new uu(r))-1}function mE(e,n,t){let r=xe();return r.firstCreatePass&&(lv(r,new da(e,n,t),-1),(n&2)===2&&(r.staticViewQueries=!0)),av(r,H(),n)}function vE(e,n,t,r){let o=xe();if(o.firstCreatePass){let i=Ge();lv(o,new da(n,t,r),i.index),bE(o,e),(t&2)===2&&(o.staticContentQueries=!0)}return av(o,H(),t)}function yE(e){return e.split(",").map(n=>n.trim())}function lv(e,n,t){e.queries===null&&(e.queries=new fu),e.queries.track(new hu(n,t))}function bE(e,n){let t=e.contentQueries||(e.contentQueries=[]),r=t.length?t[t.length-1]:-1;n!==r&&t.push(e.queries.length-1,n)}function Cd(e,n){return e.queries.getByIndex(n)}function CE(e,n){let t=e[j],r=Cd(t,n);return r.crossesNgTemplate?pu(t,e,n,[]):sv(t,e,r,n)}var eo=class{},wd=class{};var gu=class extends eo{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new ua(this);constructor(n,t,r,o=!0){super(),this.ngModuleType=n,this._parent=t;let i=Yp(n);this._bootstrapComponents=wm(i.bootstrap),this._r3Injector=Ug(n,t,[{provide:eo,useValue:this},{provide:Na,useValue:this.componentFactoryResolver},...r],tt(n),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let n=this._r3Injector;!n.destroyed&&n.destroy(),this.destroyCbs.forEach(t=>t()),this.destroyCbs=null}onDestroy(n){this.destroyCbs.push(n)}},mu=class extends wd{moduleType;constructor(n){super(),this.moduleType=n}create(n){return new gu(this.moduleType,n,[])}};var fa=class extends eo{injector;componentFactoryResolver=new ua(this);instance=null;constructor(n){super();let t=new Qo([...n.providers,{provide:eo,useValue:this},{provide:Na,useValue:this.componentFactoryResolver}],n.parent||Fu(),n.debugName,new Set(["environment"]));this.injector=t,n.runEnvironmentInitializers&&t.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(n){this.injector.onDestroy(n)}};function Ra(e,n,t=null){return new fa({providers:e,parent:n,debugName:t,runEnvironmentInitializers:!0}).injector}var wE=(()=>{class e{_injector;cachedInjectors=new Map;constructor(t){this._injector=t}getOrCreateStandaloneInjector(t){if(!t.standalone)return null;if(!this.cachedInjectors.has(t)){let r=Qp(!1,t.type),o=r.length>0?Ra([r],this._injector,`Standalone[${t.type.name}]`):null;this.cachedInjectors.set(t,o)}return this.cachedInjectors.get(t)}ngOnDestroy(){try{for(let t of this.cachedInjectors.values())t!==null&&t.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=S({token:e,providedIn:"environment",factory:()=>new e(T(He))})}return e})();function q(e){return ba(()=>{let n=uv(e),t=I(b({},n),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Jg.OnPush,directiveDefs:null,pipeDefs:null,dependencies:n.standalone&&e.dependencies||null,getStandaloneInjector:n.standalone?o=>o.get(wE).getOrCreateStandaloneInjector(t):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Ut.Emulated,styles:e.styles||et,_:null,schemas:e.schemas||null,tView:null,id:""});n.standalone&&hr("NgStandalone"),dv(t);let r=e.dependencies;return t.directiveDefs=Sp(r,!1),t.pipeDefs=Sp(r,!0),t.id=SE(t),t})}function DE(e){return rr(e)||AC(e)}function _E(e){return e!==null}function vn(e){return ba(()=>({type:e.type,bootstrap:e.bootstrap||et,declarations:e.declarations||et,imports:e.imports||et,exports:e.exports||et,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function EE(e,n){if(e==null)return nr;let t={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,l;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,l=o[3]||null):(i=o,s=o,a=Ia.None,l=null),t[i]=[r,a,l],n[i]=s}return t}function xE(e){if(e==null)return nr;let n={};for(let t in e)e.hasOwnProperty(t)&&(n[e[t]]=t);return n}function Se(e){return ba(()=>{let n=uv(e);return dv(n),n})}function cv(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function uv(e){let n={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:n,inputConfig:e.inputs||nr,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||et,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:EE(e.inputs,n),outputs:xE(e.outputs),debugInfo:null}}function dv(e){e.features?.forEach(n=>n(e))}function Sp(e,n){if(!e)return null;let t=n?NC:DE;return()=>(typeof e=="function"?e():e).map(r=>t(r)).filter(_E)}function SE(e){let n=0,t=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,t,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))n=Math.imul(31,n)+i.charCodeAt(0)<<0;return n+=2147483648,"c"+n}function ME(e){return Object.getPrototypeOf(e.prototype).constructor}function At(e){let n=ME(e.type),t=!0,r=[e];for(;n;){let o;if(jt(e))o=n.\u0275cmp||n.\u0275dir;else{if(n.\u0275cmp)throw new x(903,!1);o=n.\u0275dir}if(o){if(t){r.push(o);let s=e;s.inputs=Dc(e.inputs),s.declaredInputs=Dc(e.declaredInputs),s.outputs=Dc(e.outputs);let a=o.hostBindings;a&&NE(e,a);let l=o.viewQuery,c=o.contentQueries;if(l&&kE(e,l),c&&AE(e,c),IE(e,o),lC(e.outputs,o.outputs),jt(o)&&o.data.animation){let u=e.data;u.animation=(u.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===At&&(t=!1)}}n=Object.getPrototypeOf(n)}TE(r)}function IE(e,n){for(let t in n.inputs){if(!n.inputs.hasOwnProperty(t)||e.inputs.hasOwnProperty(t))continue;let r=n.inputs[t];r!==void 0&&(e.inputs[t]=r,e.declaredInputs[t]=n.declaredInputs[t])}}function TE(e){let n=0,t=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=n+=o.hostVars,o.hostAttrs=Xo(o.hostAttrs,t=Xo(t,o.hostAttrs))}}function Dc(e){return e===nr?{}:e===et?[]:e}function kE(e,n){let t=e.viewQuery;t?e.viewQuery=(r,o)=>{n(r,o),t(r,o)}:e.viewQuery=n}function AE(e,n){let t=e.contentQueries;t?e.contentQueries=(r,o,i)=>{n(r,o,i),t(r,o,i)}:e.contentQueries=n}function NE(e,n){let t=e.hostBindings;t?e.hostBindings=(r,o)=>{n(r,o),t(r,o)}:e.hostBindings=n}function Dd(e,n,t){return e[n]=t}function RE(e,n){return e[n]}function It(e,n,t){let r=e[n];return Object.is(r,t)?!1:(e[n]=t,!0)}function fv(e,n,t,r){let o=It(e,n,t);return It(e,n+1,r)||o}function OE(e,n,t,r,o){let i=fv(e,n,t,r);return It(e,n+2,o)||i}function PE(e,n,t,r,o,i,s,a,l){let c=n.consts,u=Aa(n,e,4,s||null,a||null);mg()&&ev(n,t,u,Zr(c,l),Rm),u.mergedAttrs=Xo(u.mergedAttrs,u.attrs),kg(n,u);let h=u.tView=ad(2,u,r,o,i,n.directiveRegistry,n.pipeRegistry,null,n.schemas,c,null);return n.queries!==null&&(n.queries.template(n,u),h.queries=n.queries.embeddedTView(u)),u}function ha(e,n,t,r,o,i,s,a,l,c){let u=t+Re,h=n.firstCreatePass?PE(u,n,e,r,o,i,s,a,l):n.data[u];li(h,!1);let m=FE(n,e,h,t);qu()&&md(n,e,m,h),ci(m,e);let g=Zm(m,e,m,h);return e[u]=g,cd(e,g),cE(g,h,e),Vu(h)&&ud(n,e,h),l!=null&&Am(e,h,c),h}function W(e,n,t,r,o,i,s,a){let l=H(),c=xe(),u=Zr(c.consts,i);return ha(l,c,e,n,t,r,o,u,s,a),W}var FE=LE;function LE(e,n,t,r){return Wu(!0),n[be].createComment("")}var _d=(()=>{class e{log(t){console.log(t)}warn(t){console.warn(t)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var hv=new M("");var pv=(()=>{class e{static \u0275prov=S({token:e,providedIn:"root",factory:()=>new vu})}return e})(),vu=class{queuedEffectCount=0;queues=new Map;schedule(n){this.enqueue(n)}remove(n){let t=n.zone,r=this.queues.get(t);r.has(n)&&(r.delete(n),this.queuedEffectCount--)}enqueue(n){let t=n.zone;this.queues.has(t)||this.queues.set(t,new Set);let r=this.queues.get(t);r.has(n)||(this.queuedEffectCount++,r.add(n))}flush(){for(;this.queuedEffectCount>0;)for(let[n,t]of this.queues)n===null?this.flushQueue(t):n.run(()=>this.flushQueue(t))}flushQueue(n){for(let t of n)n.delete(t),this.queuedEffectCount--,t.run()}};function gr(e){return!!e&&typeof e.then=="function"}function gv(e){return!!e&&typeof e.subscribe=="function"}var Oa=new M("");var mv=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((t,r)=>{this.resolve=t,this.reject=r});appInits=w(Oa,{optional:!0})??[];injector=w(ze);constructor(){}runInitializers(){if(this.initialized)return;let t=[];for(let o of this.appInits){let i=nt(this.injector,o);if(gr(i))t.push(i);else if(gv(i)){let s=new Promise((a,l)=>{i.subscribe({complete:a,error:l})});t.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(t).then(()=>{r()}).catch(o=>{this.reject(o)}),t.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),hi=new M("");function VE(){Ch(()=>{throw new x(600,!1)})}function jE(e){return e.isBoundToModule}var BE=10;var cn=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=w(Vw);afterRenderManager=w(im);zonelessEnabled=w(xa);rootEffectScheduler=w(pv);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new pe;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=w(Fn).hasPendingTasks.pipe(B(t=>!t));constructor(){w(co,{optional:!0})}whenStable(){let t;return new Promise(r=>{t=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{t.unsubscribe()})}_injector=w(He);_rendererFactory=null;get injector(){return this._injector}bootstrap(t,r){se(10);let o=t instanceof Km;if(!this._injector.get(mv).done){let m="";throw new x(405,m)}let s;o?s=t:s=this._injector.get(Na).resolveComponentFactory(t),this.componentTypes.push(s.componentType);let a=jE(s)?void 0:this._injector.get(eo),l=r||s.selector,c=s.create(ze.NULL,[],l,a),u=c.location.nativeElement,h=c.injector.get(hv,null);return h?.registerApplication(u),c.onDestroy(()=>{this.detachView(c.hostView),Gs(this.components,c),h?.unregisterApplication(u)}),this._loadComponent(c),se(11,c),c}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){se(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(td.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new x(101,!1);let t=G(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,G(t),this.afterTick.next(),se(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Kr,null,{optional:!0}));let t=0;for(;this.dirtyFlags!==0&&t++<BE;)se(14),this.synchronizeOnce(),se(15)}synchronizeOnce(){if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let t=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)$E(r,o,t,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:t})=>Ea(t))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(t){let r=t;this._views.push(r),r.attachToAppRef(this)}detachView(t){let r=t;Gs(this._views,r),r.detachFromAppRef()}_loadComponent(t){this.attachView(t.hostView),this.tick(),this.components.push(t),this._injector.get(hi,[]).forEach(o=>o(t))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(t=>t()),this._views.slice().forEach(t=>t.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(t){return this._destroyListeners.push(t),()=>Gs(this._destroyListeners,t)}destroy(){if(this._destroyed)throw new x(406,!1);let t=this._injector;t.destroy&&!t.destroyed&&t.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Gs(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function $E(e,n,t,r){if(!t&&!Ea(e))return;zm(e,n,t&&!r?0:1)}function Gt(e,n,t,r){let o=H(),i=so();if(It(o,i,n)){let s=xe(),a=Gu();XD(a,o,e,n,t,r)}return Gt}function UE(e,n,t,r){return It(e,so(),t)?n+ii(t)+r:kt}function Bs(e,n){return e<<17|n<<2}function dr(e){return e>>17&32767}function HE(e){return(e&2)==2}function zE(e,n){return e&131071|n<<17}function yu(e){return e|2}function to(e){return(e&131068)>>2}function _c(e,n){return e&-131069|n<<2}function GE(e){return(e&1)===1}function bu(e){return e|1}function qE(e,n,t,r,o,i){let s=i?n.classBindings:n.styleBindings,a=dr(s),l=to(s);e[r]=t;let c=!1,u;if(Array.isArray(t)){let h=t;u=h[1],(u===null||si(h,u)>0)&&(c=!0)}else u=t;if(o)if(l!==0){let m=dr(e[a+1]);e[r+1]=Bs(m,a),m!==0&&(e[m+1]=_c(e[m+1],r)),e[a+1]=zE(e[a+1],r)}else e[r+1]=Bs(a,0),a!==0&&(e[a+1]=_c(e[a+1],r)),a=r;else e[r+1]=Bs(l,0),a===0?a=r:e[l+1]=_c(e[l+1],r),l=r;c&&(e[r+1]=yu(e[r+1])),Mp(e,u,r,!0),Mp(e,u,r,!1),WE(n,u,e,r,i),s=Bs(a,l),i?n.classBindings=s:n.styleBindings=s}function WE(e,n,t,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof n=="string"&&si(i,n)>=0&&(t[r+1]=bu(t[r+1]))}function Mp(e,n,t,r){let o=e[t+1],i=n===null,s=r?dr(o):to(o),a=!1;for(;s!==0&&(a===!1||i);){let l=e[s],c=e[s+1];ZE(l,n)&&(a=!0,e[s+1]=r?bu(c):yu(c)),s=r?dr(c):to(c)}a&&(e[t+1]=r?yu(o):bu(o))}function ZE(e,n){return e===null||n==null||(Array.isArray(e)?e[1]:e)===n?!0:Array.isArray(e)&&typeof n=="string"?si(e,n)>=0:!1}var St={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function YE(e){return e.substring(St.key,St.keyEnd)}function QE(e){return JE(e),vv(e,yv(e,0,St.textEnd))}function vv(e,n){let t=St.textEnd;return t===n?-1:(n=St.keyEnd=KE(e,St.key=n,t),yv(e,n,t))}function JE(e){St.key=0,St.keyEnd=0,St.value=0,St.valueEnd=0,St.textEnd=e.length}function yv(e,n,t){for(;n<t&&e.charCodeAt(n)<=32;)n++;return n}function KE(e,n,t){for(;n<t&&e.charCodeAt(n)>32;)n++;return n}function te(e,n,t){let r=H(),o=so();if(It(r,o,n)){let i=xe(),s=Gu();Nm(i,s,r,e,n,r[be],t,!1)}return te}function Cu(e,n,t,r,o){dd(n,e,t,o?"class":"style",r)}function dt(e,n){return ex(e,n,null,!0),dt}function bv(e){tx(ax,XE,e,!0)}function XE(e,n){for(let t=QE(n);t>=0;t=vv(n,t))Ou(e,YE(n),!0)}function ex(e,n,t,r){let o=H(),i=xe(),s=Dg(2);if(i.firstUpdatePass&&wv(i,e,s,r),n!==kt&&It(o,s,n)){let a=i.data[On()];Dv(i,a,o,o[be],e,o[s+1]=cx(n,t),r,s)}}function tx(e,n,t,r){let o=xe(),i=Dg(2);o.firstUpdatePass&&wv(o,null,i,r);let s=H();if(t!==kt&&It(s,i,t)){let a=o.data[On()];if(_v(a,r)&&!Cv(o,i)){let l=r?a.classesWithoutHost:a.stylesWithoutHost;l!==null&&(t=Tc(l,t||"")),Cu(o,a,s,t,r)}else lx(o,a,s,s[be],s[i+1],s[i+1]=sx(e,n,t),r,i)}}function Cv(e,n){return n>=e.expandoStartIndex}function wv(e,n,t,r){let o=e.data;if(o[t+1]===null){let i=o[On()],s=Cv(e,t);_v(i,r)&&n===null&&!s&&(n=!1),n=nx(o,i,n,r),qE(o,i,n,t,s,r)}}function nx(e,n,t,r){let o=uw(e),i=r?n.residualClasses:n.residualStyles;if(o===null)(r?n.classBindings:n.styleBindings)===0&&(t=Ec(null,e,n,t,r),t=oi(t,n.attrs,r),i=null);else{let s=n.directiveStylingLast;if(s===-1||e[s]!==o)if(t=Ec(o,e,n,t,r),i===null){let l=rx(e,n,r);l!==void 0&&Array.isArray(l)&&(l=Ec(null,e,n,l[1],r),l=oi(l,n.attrs,r),ox(e,n,r,l))}else i=ix(e,n,r)}return i!==void 0&&(r?n.residualClasses=i:n.residualStyles=i),t}function rx(e,n,t){let r=t?n.classBindings:n.styleBindings;if(to(r)!==0)return e[dr(r)]}function ox(e,n,t,r){let o=t?n.classBindings:n.styleBindings;e[dr(o)]=r}function ix(e,n,t){let r,o=n.directiveEnd;for(let i=1+n.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=oi(r,s,t)}return oi(r,n.attrs,t)}function Ec(e,n,t,r,o){let i=null,s=t.directiveEnd,a=t.directiveStylingLast;for(a===-1?a=t.directiveStart:a++;a<s&&(i=n[a],r=oi(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(t.directiveStylingLast=a),r}function oi(e,n,t){let r=t?1:2,o=-1;if(n!==null)for(let i=0;i<n.length;i++){let s=n[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),Ou(e,s,t?!0:n[++i]))}return e===void 0?null:e}function sx(e,n,t){if(t==null||t==="")return et;let r=[],o=vt(t);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&n(r,o);return r}function ax(e,n,t){let r=String(n);r!==""&&!r.includes(" ")&&Ou(e,r,t)}function lx(e,n,t,r,o,i,s,a){o===kt&&(o=et);let l=0,c=0,u=0<o.length?o[0]:null,h=0<i.length?i[0]:null;for(;u!==null||h!==null;){let m=l<o.length?o[l+1]:void 0,g=c<i.length?i[c+1]:void 0,v=null,y;u===h?(l+=2,c+=2,m!==g&&(v=h,y=g)):h===null||u!==null&&u<h?(l+=2,v=u):(c+=2,v=h,y=g),v!==null&&Dv(e,n,t,r,v,y,s,a),u=l<o.length?o[l]:null,h=c<i.length?i[c]:null}}function Dv(e,n,t,r,o,i,s,a){if(!(n.type&3))return;let l=e.data,c=l[a+1],u=GE(c)?Ip(l,n,t,o,to(c),s):void 0;if(!pa(u)){pa(i)||HE(c)&&(i=Ip(l,null,t,o,a,s));let h=cg(On(),t);m_(r,s,h,o,i)}}function Ip(e,n,t,r,o,i){let s=n===null,a;for(;o>0;){let l=e[o],c=Array.isArray(l),u=c?l[1]:l,h=u===null,m=t[o+1];m===kt&&(m=h?et:void 0);let g=h?pc(m,r):u===r?m:void 0;if(c&&!pa(g)&&(g=pc(l,r)),pa(g)&&(a=g,s))return a;let v=e[o+1];o=s?dr(v):to(v)}if(n!==null){let l=i?n.residualClasses:n.residualStyles;l!=null&&(a=pc(l,r))}return a}function pa(e){return e!==void 0}function cx(e,n){return e==null||e===""||(typeof n=="string"?e=e+n:typeof e=="object"&&(e=tt(vt(e)))),e}function _v(e,n){return(e.flags&(n?8:16))!==0}var wu=class{destroy(n){}updateValue(n,t){}swap(n,t){let r=Math.min(n,t),o=Math.max(n,t),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(n,t){this.attach(t,this.detach(n))}};function xc(e,n,t,r,o){return e===t&&Object.is(n,r)?1:Object.is(o(e,n),o(t,r))?-1:0}function ux(e,n,t){let r,o,i=0,s=e.length-1,a=void 0;if(Array.isArray(n)){let l=n.length-1;for(;i<=s&&i<=l;){let c=e.at(i),u=n[i],h=xc(i,c,i,u,t);if(h!==0){h<0&&e.updateValue(i,u),i++;continue}let m=e.at(s),g=n[l],v=xc(s,m,l,g,t);if(v!==0){v<0&&e.updateValue(s,g),s--,l--;continue}let y=t(i,c),_=t(s,m),A=t(i,u);if(Object.is(A,_)){let ae=t(l,g);Object.is(ae,y)?(e.swap(i,s),e.updateValue(s,g),l--,s--):e.move(s,i),e.updateValue(i,u),i++;continue}if(r??=new ga,o??=kp(e,i,s,t),Du(e,r,i,A))e.updateValue(i,u),i++,s++;else if(o.has(A))r.set(y,e.detach(i)),s--;else{let ae=e.create(i,n[i]);e.attach(i,ae),i++,s++}}for(;i<=l;)Tp(e,r,t,i,n[i]),i++}else if(n!=null){let l=n[Symbol.iterator](),c=l.next();for(;!c.done&&i<=s;){let u=e.at(i),h=c.value,m=xc(i,u,i,h,t);if(m!==0)m<0&&e.updateValue(i,h),i++,c=l.next();else{r??=new ga,o??=kp(e,i,s,t);let g=t(i,h);if(Du(e,r,i,g))e.updateValue(i,h),i++,s++,c=l.next();else if(!o.has(g))e.attach(i,e.create(i,h)),i++,s++,c=l.next();else{let v=t(i,u);r.set(v,e.detach(i)),s--}}}for(;!c.done;)Tp(e,r,t,e.length,c.value),c=l.next()}for(;i<=s;)e.destroy(e.detach(s--));r?.forEach(l=>{e.destroy(l)})}function Du(e,n,t,r){return n!==void 0&&n.has(r)?(e.attach(t,n.get(r)),n.delete(r),!0):!1}function Tp(e,n,t,r,o){if(Du(e,n,r,t(r,o)))e.updateValue(r,o);else{let i=e.create(r,o);e.attach(r,i)}}function kp(e,n,t,r){let o=new Set;for(let i=n;i<=t;i++)o.add(r(i,e.at(i)));return o}var ga=class{kvMap=new Map;_vMap=void 0;has(n){return this.kvMap.has(n)}delete(n){if(!this.has(n))return!1;let t=this.kvMap.get(n);return this._vMap!==void 0&&this._vMap.has(t)?(this.kvMap.set(n,this._vMap.get(t)),this._vMap.delete(t)):this.kvMap.delete(n),!0}get(n){return this.kvMap.get(n)}set(n,t){if(this.kvMap.has(n)){let r=this.kvMap.get(n);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,t)}else this.kvMap.set(n,t)}forEach(n){for(let[t,r]of this.kvMap)if(n(r,t),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),n(r,t)}}};function O(e,n){hr("NgControlFlow");let t=H(),r=so(),o=t[r]!==kt?t[r]:-1,i=o!==-1?ma(t,Re+o):void 0,s=0;if(It(t,r,e)){let a=G(null);try{if(i!==void 0&&Qm(i,s),e!==-1){let l=Re+e,c=ma(t,l),u=Su(t[j],l),h=Jr(c,u.tView.ssrId),m=di(t,u,n,{dehydratedView:h});fi(c,m,s,Yr(u,h))}}finally{G(a)}}else if(i!==void 0){let a=Ym(i,s);a!==void 0&&(a[Te]=n)}}var _u=class{lContainer;$implicit;$index;constructor(n,t,r){this.lContainer=n,this.$implicit=t,this.$index=r}get $count(){return this.lContainer.length-Fe}};function Ed(e){return e}function Pa(e,n){return n}var Eu=class{hasEmptyBlock;trackByFn;liveCollection;constructor(n,t,r){this.hasEmptyBlock=n,this.trackByFn=t,this.liveCollection=r}};function qt(e,n,t,r,o,i,s,a,l,c,u,h,m){hr("NgControlFlow");let g=H(),v=xe(),y=l!==void 0,_=H(),A=a?s.bind(_[lt][Te]):s,ae=new Eu(y,A);_[Re+e]=ae,ha(g,v,e+1,n,t,r,o,Zr(v.consts,i)),y&&ha(g,v,e+2,l,c,u,h,Zr(v.consts,m))}var xu=class extends wu{lContainer;hostLView;templateTNode;operationsCounter=void 0;needsIndexUpdate=!1;constructor(n,t,r){super(),this.lContainer=n,this.hostLView=t,this.templateTNode=r}get length(){return this.lContainer.length-Fe}at(n){return this.getLView(n)[Te].$implicit}attach(n,t){let r=t[qr];this.needsIndexUpdate||=n!==this.length,fi(this.lContainer,t,n,Yr(this.templateTNode,r))}detach(n){return this.needsIndexUpdate||=n!==this.length-1,dx(this.lContainer,n)}create(n,t){let r=Jr(this.lContainer,this.templateTNode.tView.ssrId),o=di(this.hostLView,this.templateTNode,new _u(this.lContainer,t,n),{dehydratedView:r});return this.operationsCounter?.recordCreate(),o}destroy(n){Ta(n[j],n),this.operationsCounter?.recordDestroy()}updateValue(n,t){this.getLView(n)[Te].$implicit=t}reset(){this.needsIndexUpdate=!1,this.operationsCounter?.reset()}updateIndexes(){if(this.needsIndexUpdate)for(let n=0;n<this.length;n++)this.getLView(n)[Te].$index=n}getLView(n){return fx(this.lContainer,n)}};function Wt(e){let n=G(null),t=On();try{let r=H(),o=r[j],i=r[t],s=t+1,a=ma(r,s);if(i.liveCollection===void 0){let c=Su(o,s);i.liveCollection=new xu(a,r,c)}else i.liveCollection.reset();let l=i.liveCollection;if(ux(l,e,i.trackByFn),l.updateIndexes(),i.hasEmptyBlock){let c=so(),u=l.length===0;if(It(r,c,u)){let h=t+2,m=ma(r,h);if(u){let g=Su(o,h),v=Jr(m,g.tView.ssrId),y=di(r,g,void 0,{dehydratedView:v});fi(m,y,0,Yr(g,v))}else Qm(m,0)}}}finally{G(n)}}function ma(e,n){return e[n]}function dx(e,n){return ni(e,n)}function fx(e,n){return Ym(e,n)}function Su(e,n){return ju(e,n)}function d(e,n,t,r){let o=H(),i=xe(),s=Re+e,a=o[be],l=i.firstCreatePass?nv(s,i,o,n,Rm,mg(),t,r):i.data[s],c=hx(i,o,l,a,n,e);o[s]=c;let u=Vu(l);return li(l,!0),Sm(a,c,l),!pd(l)&&qu()&&md(i,o,c,l),(XC()===0||u)&&ci(c,o),ew(),u&&(ud(i,o,l),lm(i,l,o)),r!==null&&Am(o,l),d}function f(){let e=Ge();bg()?Cg():(e=e.parent,li(e,!1));let n=e;nw(n)&&rw(),tw();let t=xe();return t.firstCreatePass&&rv(t,n),n.classesWithoutHost!=null&&vw(n)&&Cu(t,n,H(),n.classesWithoutHost,!0),n.stylesWithoutHost!=null&&yw(n)&&Cu(t,n,H(),n.stylesWithoutHost,!1),f}function D(e,n,t,r){return d(e,n,t,r),f(),D}var hx=(e,n,t,r,o,i)=>(Wu(!0),Em(r,o,pw()));function Ve(){return H()}var Kn=void 0;function px(e){let n=Math.floor(Math.abs(e)),t=e.toString().replace(/^[^.]*\.?/,"").length;return n===1&&t===0?1:5}var gx=["en",[["a","p"],["AM","PM"],Kn],[["AM","PM"],Kn,Kn],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],Kn,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],Kn,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",Kn,"{1} 'at' {0}",Kn],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",px],Sc={};function yt(e){let n=mx(e),t=Ap(n);if(t)return t;let r=n.split("-")[0];if(t=Ap(r),t)return t;if(r==="en")return gx;throw new x(701,!1)}function Ap(e){return e in Sc||(Sc[e]=nn.ng&&nn.ng.common&&nn.ng.common.locales&&nn.ng.common.locales[e]),Sc[e]}var Ce=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(Ce||{});function mx(e){return e.toLowerCase().replace(/_/g,"-")}var va="en-US";var vx=va;function yx(e){typeof e=="string"&&(vx=e.toLowerCase().replace(/_/g,"-"))}var bx=(e,n,t)=>{};function V(e,n,t,r){let o=H(),i=xe(),s=Ge();return Ev(i,o,o[be],s,e,n,r),V}function Cx(e,n,t,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===t&&o[i+1]===r){let a=n[Ks],l=o[i+2];return a.length>l?a[l]:null}typeof s=="string"&&(i+=2)}return null}function Ev(e,n,t,r,o,i,s){let a=Vu(r),c=e.firstCreatePass?gg(e):null,u=n[Te],h=pg(n),m=!0;if(r.type&3||s){let g=zt(r,n),v=s?s(g):g,y=h.length,_=s?ae=>s(Bt(ae[r.index])):r.index,A=null;if(!s&&a&&(A=Cx(e,n,o,r.index)),A!==null){let ae=A.__ngLastListenerFn__||A;ae.__ngNextListenerFn__=i,A.__ngLastListenerFn__=i,m=!1}else{i=Op(r,n,u,i),bx(v,o,i);let ae=t.listen(v,o,i);h.push(i,ae),c&&c.push(o,_,y,y+1)}}else i=Op(r,n,u,i);if(m){let g=r.outputs?.[o],v=r.hostDirectiveOutputs?.[o];if(v&&v.length)for(let y=0;y<v.length;y+=2){let _=v[y],A=v[y+1];Np(r,e,n,_,A,o,i,h,c)}if(g&&g.length)for(let y of g)Np(r,e,n,y,o,o,i,h,c)}}function Np(e,n,t,r,o,i,s,a,l){let c=t[r],h=n.data[r].outputs[o],g=c[h].subscribe(s),v=a.length;a.push(s,g),l&&l.push(i,e.index,v,-(v+1))}function Rp(e,n,t,r){let o=G(null);try{return se(6,n,t),t(r)!==!1}catch(i){return Om(e,i),!1}finally{se(7,n,t),G(o)}}function Op(e,n,t,r){return function o(i){if(i===Function)return r;let s=oo(e)?$t(e.index,n):n;yd(s,5);let a=Rp(n,t,r,i),l=o.__ngNextListenerFn__;for(;l;)a=Rp(n,t,l,i)&&a,l=l.__ngNextListenerFn__;return a}}function N(e=1){return fw(e)}function wx(e,n){let t=null,r=kD(e);for(let o=0;o<n.length;o++){let i=n[o];if(i==="*"){t=o;continue}if(r===null?_m(e,i,!0):RD(r,i))return o}return t}function xv(e){let n=H()[lt][rt];if(!n.projection){let t=e?e.length:1,r=n.projection=IC(t,null),o=r.slice(),i=n.child;for(;i!==null;){if(i.type!==128){let s=e?wx(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function Sv(e,n=0,t,r,o,i){let s=H(),a=xe(),l=r?e+1:null;l!==null&&ha(s,a,l,r,o,i,null,t);let c=Aa(a,Re+e,16,null,t||null);c.projection===null&&(c.projection=n),Cg();let h=!s[qr]||vg();s[lt][rt].projection[c.projection]===null&&l!==null?Dx(s,a,l):h&&!pd(c)&&p_(a,s,c)}function Dx(e,n,t){let r=Re+t,o=n.data[r],i=e[r],s=Jr(i,o.tView.ssrId),a=di(e,o,void 0,{dehydratedView:s});fi(i,a,0,Yr(o,s))}function Mv(e,n,t,r){vE(e,n,t,r)}function Iv(e,n,t){mE(e,n,t)}function Fa(e){let n=H(),t=xe(),r=_g();Uu(r+1);let o=Cd(t,r);if(e.dirty&&ZC(n)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=CE(n,r);e.reset(i,$w),e.notifyOnChanges()}return!0}return!1}function La(){return gE(H(),_g())}function _x(e,n,t,r){t>=e.data.length&&(e.data[t]=null,e.blueprint[t]=null),n[t]=r}function ot(e){let n=iw();return ug(n,Re+e)}function p(e,n=""){let t=H(),r=xe(),o=e+Re,i=r.firstCreatePass?Aa(r,o,1,n,null):r.data[o],s=Ex(r,t,i,n,e);t[o]=s,qu()&&md(r,t,s,i),li(i,!1)}var Ex=(e,n,t,r,o)=>(Wu(!0),LD(n[be],r));function fe(e){return ke("",e,""),fe}function ke(e,n,t){let r=H(),o=UE(r,e,n,t);return o!==kt&&xx(r,On(),o),ke}function xx(e,n,t){let r=cg(n,e);VD(e[be],r,t)}function yn(e,n,t){Yg(n)&&(n=n());let r=H(),o=so();if(It(r,o,n)){let i=xe(),s=Gu();Nm(i,s,r,e,n,r[be],t,!1)}return yn}function Ln(e,n){let t=Yg(e);return t&&e.set(n),t}function bn(e,n){let t=H(),r=xe(),o=Ge();return Ev(r,t,t[be],o,e,n),bn}function Sx(e,n,t){let r=xe();if(r.firstCreatePass){let o=jt(e);Mu(t,r.data,r.blueprint,o,!0),Mu(n,r.data,r.blueprint,o,!1)}}function Mu(e,n,t,r,o){if(e=Ue(e),Array.isArray(e))for(let i=0;i<e.length;i++)Mu(e[i],n,t,r,o);else{let i=xe(),s=H(),a=Ge(),l=Gr(e)?e:Ue(e.provide),c=Xp(e),u=a.providerIndexes&1048575,h=a.directiveStart,m=a.providerIndexes>>20;if(Gr(e)||!e.multi){let g=new cr(c,o,E),v=Ic(l,n,o?u:u+m,h);v===-1?($c(ia(a,s),i,l),Mc(i,e,n.length),n.push(l),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(g),s.push(g)):(t[v]=g,s[v]=g)}else{let g=Ic(l,n,u+m,h),v=Ic(l,n,u,u+m),y=g>=0&&t[g],_=v>=0&&t[v];if(o&&!_||!o&&!y){$c(ia(a,s),i,l);let A=Tx(o?Ix:Mx,t.length,o,r,c);!o&&_&&(t[v].providerFactory=A),Mc(i,e,n.length,0),n.push(l),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(A),s.push(A)}else{let A=Tv(t[o?v:g],c,!o&&r);Mc(i,e,g>-1?g:v,A)}!o&&r&&_&&t[v].componentProviders++}}}function Mc(e,n,t,r){let o=Gr(n),i=LC(n);if(o||i){let l=(i?Ue(n.useClass):n).prototype.ngOnDestroy;if(l){let c=e.destroyHooks||(e.destroyHooks=[]);if(!o&&n.multi){let u=c.indexOf(t);u===-1?c.push(t,[r,l]):c[u+1].push(r,l)}else c.push(t,l)}}}function Tv(e,n,t){return t&&e.componentProviders++,e.multi.push(n)-1}function Ic(e,n,t,r){for(let o=t;o<r;o++)if(n[o]===e)return o;return-1}function Mx(e,n,t,r){return Iu(this.multi,[])}function Ix(e,n,t,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=ei(t,t[j],this.providerFactory.index,r);i=a.slice(0,s),Iu(o,i);for(let l=s;l<a.length;l++)i.push(a[l])}else i=[],Iu(o,i);return i}function Iu(e,n){for(let t=0;t<e.length;t++){let r=e[t];n.push(r())}return n}function Tx(e,n,t,r,o){let i=new cr(e,t,E);return i.multi=[],i.index=n,i.componentProviders=0,Tv(i,o,r&&!t),i}function mr(e,n=[]){return t=>{t.providersResolver=(r,o)=>Sx(r,o?o(e):e,n)}}function xd(e,n,t){let r=$u()+e,o=H();return o[r]===kt?Dd(o,r,t?n.call(t):n()):RE(o,r)}function kv(e,n,t,r,o,i){return Ax(H(),$u(),e,n,t,r,o,i)}function Av(e,n){let t=e[n];return t===kt?void 0:t}function kx(e,n,t,r,o,i,s){let a=n+t;return fv(e,a,o,i)?Dd(e,a+2,s?r.call(s,o,i):r(o,i)):Av(e,a+2)}function Ax(e,n,t,r,o,i,s,a){let l=n+t;return OE(e,l,o,i,s)?Dd(e,l+3,a?r.call(a,o,i,s):r(o,i,s)):Av(e,l+3)}function Va(e,n){let t=xe(),r,o=e+Re;t.firstCreatePass?(r=Nx(n,t.pipeRegistry),t.data[o]=r,r.onDestroy&&(t.destroyHooks??=[]).push(o,r.onDestroy)):r=t.data[o];let i=r.factory||(r.factory=tr(r.type,!0)),s,a=Xe(E);try{let l=oa(!1),c=i();return oa(l),_x(t,H(),o,c),c}finally{Xe(a)}}function Nx(e,n){if(n)for(let t=n.length-1;t>=0;t--){let r=n[t];if(e===r.name)return r}}function ja(e,n,t,r){let o=e+Re,i=H(),s=ug(i,o);return Rx(i,o)?kx(i,$u(),n,s.transform,t,r,s):s.transform(t,r)}function Rx(e,n){return e[j].data[n].pure}var Tu=class{ngModuleFactory;componentFactories;constructor(n,t){this.ngModuleFactory=n,this.componentFactories=t}},Ba=(()=>{class e{compileModuleSync(t){return new mu(t)}compileModuleAsync(t){return Promise.resolve(this.compileModuleSync(t))}compileModuleAndAllComponentsSync(t){let r=this.compileModuleSync(t),o=Yp(t),i=wm(o.declarations).reduce((s,a)=>{let l=rr(a);return l&&s.push(new Xr(l)),s},[]);return new Tu(r,i)}compileModuleAndAllComponentsAsync(t){return Promise.resolve(this.compileModuleAndAllComponentsSync(t))}clearCache(){}clearCacheFor(t){}getModuleId(t){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Ox=(()=>{class e{zone=w(ve);changeDetectionScheduler=w(ur);applicationRef=w(cn);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Px=new M("",{factory:()=>!1});function Nv({ngZoneFactory:e,ignoreChangesOutsideZone:n,scheduleInRootZone:t}){return e??=()=>new ve(I(b({},Ov()),{scheduleInRootZone:t})),[{provide:ve,useFactory:e},{provide:zr,multi:!0,useFactory:()=>{let r=w(Ox,{optional:!0});return()=>r.initialize()}},{provide:zr,multi:!0,useFactory:()=>{let r=w(Fx);return()=>{r.initialize()}}},n===!0?{provide:zg,useValue:!0}:[],{provide:Gg,useValue:t??Hg}]}function Rv(e){let n=e?.ignoreChangesOutsideZone,t=e?.scheduleInRootZone,r=Nv({ngZoneFactory:()=>{let o=Ov(e);return o.scheduleInRootZone=t,o.shouldCoalesceEventChangeDetection&&hr("NgZone_CoalesceEvent"),new ve(o)},ignoreChangesOutsideZone:n,scheduleInRootZone:t});return no([{provide:Px,useValue:!0},{provide:xa,useValue:!1},r])}function Ov(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var Fx=(()=>{class e{subscription=new ie;initialized=!1;zone=w(ve);pendingTasks=w(Fn);initialize(){if(this.initialized)return;this.initialized=!0;let t=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(t=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{ve.assertNotInAngularZone(),queueMicrotask(()=>{t!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(t),t=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{ve.assertInAngularZone(),t??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Lx=(()=>{class e{appRef=w(cn);taskService=w(Fn);ngZone=w(ve);zonelessEnabled=w(xa);tracing=w(co,{optional:!0});disableScheduling=w(zg,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new ie;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(aa):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(w(Gg,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Gc||!this.zoneIsDefined)}notify(t){if(!this.zonelessEnabled&&t===5)return;let r=!1;switch(t){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?sp:qg;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(t){return!(this.disableScheduling&&!t||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(aa+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let t=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(t),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,sp(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(t)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let t=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(t)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Vx(){return typeof $localize<"u"&&$localize.locale||va}var $a=new M("",{providedIn:"root",factory:()=>w($a,z.Optional|z.SkipSelf)||Vx()});var ku=new M(""),jx=new M("");function qo(e){return!e.moduleRef}function Bx(e){let n=qo(e)?e.r3Injector:e.moduleRef.injector,t=n.get(ve);return t.run(()=>{qo(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=n.get(sn,null),o;if(t.runOutsideAngular(()=>{o=t.onError.subscribe({next:i=>{r.handleError(i)}})}),qo(e)){let i=()=>n.destroy(),s=e.platformInjector.get(ku);s.add(i),n.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(ku);s.add(i),e.moduleRef.onDestroy(()=>{Gs(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return Ux(r,t,()=>{let i=n.get(mv);return i.runInitializers(),i.donePromise.then(()=>{let s=n.get($a,va);if(yx(s||va),!n.get(jx,!0))return qo(e)?n.get(cn):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(qo(e)){let l=n.get(cn);return e.rootComponent!==void 0&&l.bootstrap(e.rootComponent),l}else return $x(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function $x(e,n){let t=e.injector.get(cn);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>t.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(t);else throw new x(-403,!1);n.push(e)}function Ux(e,n,t){try{let r=t();return gr(r)?r.catch(o=>{throw n.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw n.runOutsideAngular(()=>e.handleError(r)),r}}var qs=null;function Hx(e=[],n){return ze.create({name:n,providers:[{provide:Da,useValue:"platform"},{provide:ku,useValue:new Set([()=>qs=null])},...e]})}function zx(e=[]){if(qs)return qs;let n=Hx(e);return qs=n,VE(),Gx(n),n}function Gx(e){let n=e.get(Xu,null);nt(e,()=>{n?.forEach(t=>t())})}var ye=(()=>{class e{static __NG_ELEMENT_ID__=qx}return e})();function qx(e){return Wx(Ge(),H(),(e&16)===16)}function Wx(e,n,t){if(oo(e)&&!t){let r=$t(e.index,n);return new ri(r,r)}else if(e.type&175){let r=n[lt];return new ri(r,n)}return null}function Pv(e){se(8);try{let{rootComponent:n,appProviders:t,platformProviders:r}=e,o=zx(r),i=[Nv({}),{provide:ur,useExisting:Lx},...t||[]],s=new fa({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return Bx({r3Injector:s.injector,platformInjector:o,rootComponent:n})}catch(n){return Promise.reject(n)}finally{se(9)}}function Vn(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function Cn(e){let n=G(null);try{return e()}finally{G(n)}}function qe(e,n){let t=vh(e);return n?.equal&&(t[st].equal=n.equal),t}var Au=class{[st];constructor(n){this[st]=n}destroy(){this[st].destroy()}};function wn(e,n){!n?.injector&&Lu(wn);let t=n?.injector??w(ze),r=n?.manualCleanup!==!0?t.get(ao):null,o,i=t.get(nd,null,{optional:!0}),s=t.get(ur);return i!==null&&!n?.forceRoot?(o=Qx(i.view,s,e),r instanceof sa&&r._lView===i.view&&(r=null)):o=Jx(e,t.get(pv),s),o.injector=t,r!==null&&(o.onDestroyFn=r.onDestroy(()=>o.destroy())),new Au(o)}var Fv=I(b({},Mr),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,dirty:!0,hasRun:!1,cleanupFns:void 0,zone:null,kind:"effect",onDestroyFn:ti,run(){if(this.dirty=!1,this.hasRun&&!us(this))return;this.hasRun=!0;let e=r=>(this.cleanupFns??=[]).push(r),n=Bo(this),t=ta(!1);try{this.maybeCleanup(),this.fn(e)}finally{ta(t),cs(this,n)}},maybeCleanup(){if(this.cleanupFns?.length)try{for(;this.cleanupFns.length;)this.cleanupFns.pop()()}finally{this.cleanupFns=[]}}}),Zx=I(b({},Fv),{consumerMarkedDirty(){this.scheduler.schedule(this),this.notifier.notify(12)},destroy(){$o(this),this.onDestroyFn(),this.maybeCleanup(),this.scheduler.remove(this)}}),Yx=I(b({},Fv),{consumerMarkedDirty(){this.view[F]|=8192,io(this.view),this.notifier.notify(13)},destroy(){$o(this),this.onDestroyFn(),this.maybeCleanup(),this.view[ir]?.delete(this)}});function Qx(e,n,t){let r=Object.create(Yx);return r.view=e,r.zone=typeof Zone<"u"?Zone.current:null,r.notifier=n,r.fn=t,e[ir]??=new Set,e[ir].add(r),r.consumerMarkedDirty(r),r}function Jx(e,n,t){let r=Object.create(Zx);return r.fn=e,r.scheduler=n,r.notifier=t,r.zone=typeof Zone<"u"?Zone.current:null,r.scheduler.schedule(r),r.notifier.notify(12),r}function Lv(e){let n=rr(e);if(!n)return null;let t=new Xr(n);return{get selector(){return t.selector},get type(){return t.componentType},get inputs(){return t.inputs},get outputs(){return t.outputs},get ngContentSelectors(){return t.ngContentSelectors},get isStandalone(){return n.standalone},get isSignal(){return n.signals}}}var Hv=null;function En(){return Hv}function zv(e){Hv??=e}var Qa=class{};var Oe=new M(""),Rd=(()=>{class e{historyGo(t){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:()=>w(Kx),providedIn:"platform"})}return e})(),Gv=new M(""),Kx=(()=>{class e extends Rd{_location;_history;_doc=w(Oe);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return En().getBaseHref(this._doc)}onPopState(t){let r=En().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",t,!1),()=>r.removeEventListener("popstate",t)}onHashChange(t){let r=En().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",t,!1),()=>r.removeEventListener("hashchange",t)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(t){this._location.pathname=t}pushState(t,r,o){this._history.pushState(t,r,o)}replaceState(t,r,o){this._history.replaceState(t,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(t=0){this._history.go(t)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function Od(e,n){return e?n?e.endsWith("/")?n.startsWith("/")?e+n.slice(1):e+n:n.startsWith("/")?e+n:`${e}/${n}`:e:n}function Vv(e){let n=e.search(/#|\?|$/);return e[n-1]==="/"?e.slice(0,n-1)+e.slice(n):e}function _n(e){return e&&e[0]!=="?"?`?${e}`:e}var xn=(()=>{class e{historyGo(t){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:()=>w(Pd),providedIn:"root"})}return e})(),qv=new M(""),Pd=(()=>{class e extends xn{_platformLocation;_baseHref;_removeListenerFns=[];constructor(t,r){super(),this._platformLocation=t,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??w(Oe).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}prepareExternalUrl(t){return Od(this._baseHref,t)}path(t=!1){let r=this._platformLocation.pathname+_n(this._platformLocation.search),o=this._platformLocation.hash;return o&&t?`${r}${o}`:r}pushState(t,r,o,i){let s=this.prepareExternalUrl(o+_n(i));this._platformLocation.pushState(t,r,s)}replaceState(t,r,o,i){let s=this.prepareExternalUrl(o+_n(i));this._platformLocation.replaceState(t,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}static \u0275fac=function(r){return new(r||e)(T(Rd),T(qv,8))};static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Wv=(()=>{class e extends xn{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(t,r){super(),this._platformLocation=t,r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}path(t=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(t){let r=Od(this._baseHref,t);return r.length>0?"#"+r:r}pushState(t,r,o,i){let s=this.prepareExternalUrl(o+_n(i))||this._platformLocation.pathname;this._platformLocation.pushState(t,r,s)}replaceState(t,r,o,i){let s=this.prepareExternalUrl(o+_n(i))||this._platformLocation.pathname;this._platformLocation.replaceState(t,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}static \u0275fac=function(r){return new(r||e)(T(Rd),T(qv,8))};static \u0275prov=S({token:e,factory:e.\u0275fac})}return e})(),ho=(()=>{class e{_subject=new pe;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(t){this._locationStrategy=t;let r=this._locationStrategy.getBaseHref();this._basePath=tS(Vv(jv(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(t=!1){return this.normalize(this._locationStrategy.path(t))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(t,r=""){return this.path()==this.normalize(t+_n(r))}normalize(t){return e.stripTrailingSlash(eS(this._basePath,jv(t)))}prepareExternalUrl(t){return t&&t[0]!=="/"&&(t="/"+t),this._locationStrategy.prepareExternalUrl(t)}go(t,r="",o=null){this._locationStrategy.pushState(o,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+_n(r)),o)}replaceState(t,r="",o=null){this._locationStrategy.replaceState(o,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+_n(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(t=0){this._locationStrategy.historyGo?.(t)}onUrlChange(t){return this._urlChangeListeners.push(t),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(t);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(t="",r){this._urlChangeListeners.forEach(o=>o(t,r))}subscribe(t,r,o){return this._subject.subscribe({next:t,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=_n;static joinWithSlash=Od;static stripTrailingSlash=Vv;static \u0275fac=function(r){return new(r||e)(T(xn))};static \u0275prov=S({token:e,factory:()=>Xx(),providedIn:"root"})}return e})();function Xx(){return new ho(T(xn))}function eS(e,n){if(!e||!n.startsWith(e))return n;let t=n.substring(e.length);return t===""||["/",";","?","#"].includes(t[0])?t:n}function jv(e){return e.replace(/\/index.html$/,"")}function tS(e){if(new RegExp("^(https?:)?//").test(e)){let[,t]=e.split(/\/\/[^\/]+/);return t}return e}var We=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(We||{}),ue=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(ue||{}),ft=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(ft||{}),jn={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function nS(e){return yt(e)[Ce.LocaleId]}function rS(e,n,t){let r=yt(e),o=[r[Ce.DayPeriodsFormat],r[Ce.DayPeriodsStandalone]],i=bt(o,n);return bt(i,t)}function oS(e,n,t){let r=yt(e),o=[r[Ce.DaysFormat],r[Ce.DaysStandalone]],i=bt(o,n);return bt(i,t)}function iS(e,n,t){let r=yt(e),o=[r[Ce.MonthsFormat],r[Ce.MonthsStandalone]],i=bt(o,n);return bt(i,t)}function sS(e,n){let r=yt(e)[Ce.Eras];return bt(r,n)}function Ua(e,n){let t=yt(e);return bt(t[Ce.DateFormat],n)}function Ha(e,n){let t=yt(e);return bt(t[Ce.TimeFormat],n)}function za(e,n){let r=yt(e)[Ce.DateTimeFormat];return bt(r,n)}function Ka(e,n){let t=yt(e),r=t[Ce.NumberSymbols][n];if(typeof r>"u"){if(n===jn.CurrencyDecimal)return t[Ce.NumberSymbols][jn.Decimal];if(n===jn.CurrencyGroup)return t[Ce.NumberSymbols][jn.Group]}return r}function Zv(e){if(!e[Ce.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[Ce.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function aS(e){let n=yt(e);return Zv(n),(n[Ce.ExtraData][2]||[]).map(r=>typeof r=="string"?Sd(r):[Sd(r[0]),Sd(r[1])])}function lS(e,n,t){let r=yt(e);Zv(r);let o=[r[Ce.ExtraData][0],r[Ce.ExtraData][1]],i=bt(o,n)||[];return bt(i,t)||[]}function bt(e,n){for(let t=n;t>-1;t--)if(typeof e[t]<"u")return e[t];throw new Error("Locale data API: locale data undefined")}function Sd(e){let[n,t]=e.split(":");return{hours:+n,minutes:+t}}var cS=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,Ga={},uS=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;function dS(e,n,t,r){let o=CS(e);n=Dn(t,n)||n;let s=[],a;for(;n;)if(a=uS.exec(n),a){s=s.concat(a.slice(1));let u=s.pop();if(!u)break;n=u}else{s.push(n);break}let l=o.getTimezoneOffset();r&&(l=Qv(r,l),o=bS(o,r));let c="";return s.forEach(u=>{let h=vS(u);c+=h?h(o,t,l):u==="''"?"'":u.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),c}function Ja(e,n,t){let r=new Date(0);return r.setFullYear(e,n,t),r.setHours(0,0,0),r}function Dn(e,n){let t=nS(e);if(Ga[t]??={},Ga[t][n])return Ga[t][n];let r="";switch(n){case"shortDate":r=Ua(e,ft.Short);break;case"mediumDate":r=Ua(e,ft.Medium);break;case"longDate":r=Ua(e,ft.Long);break;case"fullDate":r=Ua(e,ft.Full);break;case"shortTime":r=Ha(e,ft.Short);break;case"mediumTime":r=Ha(e,ft.Medium);break;case"longTime":r=Ha(e,ft.Long);break;case"fullTime":r=Ha(e,ft.Full);break;case"short":let o=Dn(e,"shortTime"),i=Dn(e,"shortDate");r=qa(za(e,ft.Short),[o,i]);break;case"medium":let s=Dn(e,"mediumTime"),a=Dn(e,"mediumDate");r=qa(za(e,ft.Medium),[s,a]);break;case"long":let l=Dn(e,"longTime"),c=Dn(e,"longDate");r=qa(za(e,ft.Long),[l,c]);break;case"full":let u=Dn(e,"fullTime"),h=Dn(e,"fullDate");r=qa(za(e,ft.Full),[u,h]);break}return r&&(Ga[t][n]=r),r}function qa(e,n){return n&&(e=e.replace(/\{([^}]+)}/g,function(t,r){return n!=null&&r in n?n[r]:t})),e}function Nt(e,n,t="-",r,o){let i="";(e<0||o&&e<=0)&&(o?e=-e+1:(e=-e,i=t));let s=String(e);for(;s.length<n;)s="0"+s;return r&&(s=s.slice(s.length-n)),i+s}function fS(e,n){return Nt(e,3).substring(0,n)}function Me(e,n,t=0,r=!1,o=!1){return function(i,s){let a=hS(e,i);if((t>0||a>-t)&&(a+=t),e===3)a===0&&t===-12&&(a=12);else if(e===6)return fS(a,n);let l=Ka(s,jn.MinusSign);return Nt(a,n,l,r,o)}}function hS(e,n){switch(e){case 0:return n.getFullYear();case 1:return n.getMonth();case 2:return n.getDate();case 3:return n.getHours();case 4:return n.getMinutes();case 5:return n.getSeconds();case 6:return n.getMilliseconds();case 7:return n.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}function he(e,n,t=We.Format,r=!1){return function(o,i){return pS(o,i,e,n,t,r)}}function pS(e,n,t,r,o,i){switch(t){case 2:return iS(n,o,r)[e.getMonth()];case 1:return oS(n,o,r)[e.getDay()];case 0:let s=e.getHours(),a=e.getMinutes();if(i){let c=aS(n),u=lS(n,o,r),h=c.findIndex(m=>{if(Array.isArray(m)){let[g,v]=m,y=s>=g.hours&&a>=g.minutes,_=s<v.hours||s===v.hours&&a<v.minutes;if(g.hours<v.hours){if(y&&_)return!0}else if(y||_)return!0}else if(m.hours===s&&m.minutes===a)return!0;return!1});if(h!==-1)return u[h]}return rS(n,o,r)[s<12?0:1];case 3:return sS(n,r)[e.getFullYear()<=0?0:1];default:let l=t;throw new Error(`unexpected translation type ${l}`)}}function Wa(e){return function(n,t,r){let o=-1*r,i=Ka(t,jn.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(e){case 0:return(o>=0?"+":"")+Nt(s,2,i)+Nt(Math.abs(o%60),2,i);case 1:return"GMT"+(o>=0?"+":"")+Nt(s,1,i);case 2:return"GMT"+(o>=0?"+":"")+Nt(s,2,i)+":"+Nt(Math.abs(o%60),2,i);case 3:return r===0?"Z":(o>=0?"+":"")+Nt(s,2,i)+":"+Nt(Math.abs(o%60),2,i);default:throw new Error(`Unknown zone width "${e}"`)}}}var gS=0,Ya=4;function mS(e){let n=Ja(e,gS,1).getDay();return Ja(e,0,1+(n<=Ya?Ya:Ya+7)-n)}function Yv(e){let n=e.getDay(),t=n===0?-3:Ya-n;return Ja(e.getFullYear(),e.getMonth(),e.getDate()+t)}function Md(e,n=!1){return function(t,r){let o;if(n){let i=new Date(t.getFullYear(),t.getMonth(),1).getDay()-1,s=t.getDate();o=1+Math.floor((s+i)/7)}else{let i=Yv(t),s=mS(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return Nt(o,e,Ka(r,jn.MinusSign))}}function Za(e,n=!1){return function(t,r){let i=Yv(t).getFullYear();return Nt(i,e,Ka(r,jn.MinusSign),n)}}var Id={};function vS(e){if(Id[e])return Id[e];let n;switch(e){case"G":case"GG":case"GGG":n=he(3,ue.Abbreviated);break;case"GGGG":n=he(3,ue.Wide);break;case"GGGGG":n=he(3,ue.Narrow);break;case"y":n=Me(0,1,0,!1,!0);break;case"yy":n=Me(0,2,0,!0,!0);break;case"yyy":n=Me(0,3,0,!1,!0);break;case"yyyy":n=Me(0,4,0,!1,!0);break;case"Y":n=Za(1);break;case"YY":n=Za(2,!0);break;case"YYY":n=Za(3);break;case"YYYY":n=Za(4);break;case"M":case"L":n=Me(1,1,1);break;case"MM":case"LL":n=Me(1,2,1);break;case"MMM":n=he(2,ue.Abbreviated);break;case"MMMM":n=he(2,ue.Wide);break;case"MMMMM":n=he(2,ue.Narrow);break;case"LLL":n=he(2,ue.Abbreviated,We.Standalone);break;case"LLLL":n=he(2,ue.Wide,We.Standalone);break;case"LLLLL":n=he(2,ue.Narrow,We.Standalone);break;case"w":n=Md(1);break;case"ww":n=Md(2);break;case"W":n=Md(1,!0);break;case"d":n=Me(2,1);break;case"dd":n=Me(2,2);break;case"c":case"cc":n=Me(7,1);break;case"ccc":n=he(1,ue.Abbreviated,We.Standalone);break;case"cccc":n=he(1,ue.Wide,We.Standalone);break;case"ccccc":n=he(1,ue.Narrow,We.Standalone);break;case"cccccc":n=he(1,ue.Short,We.Standalone);break;case"E":case"EE":case"EEE":n=he(1,ue.Abbreviated);break;case"EEEE":n=he(1,ue.Wide);break;case"EEEEE":n=he(1,ue.Narrow);break;case"EEEEEE":n=he(1,ue.Short);break;case"a":case"aa":case"aaa":n=he(0,ue.Abbreviated);break;case"aaaa":n=he(0,ue.Wide);break;case"aaaaa":n=he(0,ue.Narrow);break;case"b":case"bb":case"bbb":n=he(0,ue.Abbreviated,We.Standalone,!0);break;case"bbbb":n=he(0,ue.Wide,We.Standalone,!0);break;case"bbbbb":n=he(0,ue.Narrow,We.Standalone,!0);break;case"B":case"BB":case"BBB":n=he(0,ue.Abbreviated,We.Format,!0);break;case"BBBB":n=he(0,ue.Wide,We.Format,!0);break;case"BBBBB":n=he(0,ue.Narrow,We.Format,!0);break;case"h":n=Me(3,1,-12);break;case"hh":n=Me(3,2,-12);break;case"H":n=Me(3,1);break;case"HH":n=Me(3,2);break;case"m":n=Me(4,1);break;case"mm":n=Me(4,2);break;case"s":n=Me(5,1);break;case"ss":n=Me(5,2);break;case"S":n=Me(6,1);break;case"SS":n=Me(6,2);break;case"SSS":n=Me(6,3);break;case"Z":case"ZZ":case"ZZZ":n=Wa(0);break;case"ZZZZZ":n=Wa(3);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":n=Wa(1);break;case"OOOO":case"ZZZZ":case"zzzz":n=Wa(2);break;default:return null}return Id[e]=n,n}function Qv(e,n){e=e.replace(/:/g,"");let t=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(t)?n:t}function yS(e,n){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+n),e}function bS(e,n,t){let o=e.getTimezoneOffset(),i=Qv(n,o);return yS(e,-1*(i-o))}function CS(e){if(Bv(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[o,i=1,s=1]=e.split("-").map(a=>+a);return Ja(o,i-1,s)}let t=parseFloat(e);if(!isNaN(e-t))return new Date(t);let r;if(r=e.match(cS))return wS(r)}let n=new Date(e);if(!Bv(n))throw new Error(`Unable to convert "${e}" into a date`);return n}function wS(e){let n=new Date(0),t=0,r=0,o=e[8]?n.setUTCFullYear:n.setFullYear,i=e[8]?n.setUTCHours:n.setHours;e[9]&&(t=Number(e[9]+e[10]),r=Number(e[9]+e[11])),o.call(n,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-t,a=Number(e[5]||0)-r,l=Number(e[6]||0),c=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return i.call(n,s,a,l,c),n}function Bv(e){return e instanceof Date&&!isNaN(e.valueOf())}function Xa(e,n){n=encodeURIComponent(n);for(let t of e.split(";")){let r=t.indexOf("="),[o,i]=r==-1?[t,""]:[t.slice(0,r),t.slice(r+1)];if(o.trim()===n)return decodeURIComponent(i)}return null}var Td=/\s+/,$v=[],el=(()=>{class e{_ngEl;_renderer;initialClasses=$v;rawClass;stateMap=new Map;constructor(t,r){this._ngEl=t,this._renderer=r}set klass(t){this.initialClasses=t!=null?t.trim().split(Td):$v}set ngClass(t){this.rawClass=typeof t=="string"?t.trim().split(Td):t}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let t=this.rawClass;if(Array.isArray(t)||t instanceof Set)for(let r of t)this._updateState(r,!0);else if(t!=null)for(let r of Object.keys(t))this._updateState(r,!!t[r]);this._applyStateDiff()}_updateState(t,r){let o=this.stateMap.get(t);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(t,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let t of this.stateMap){let r=t[0],o=t[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(t,r){t=t.trim(),t.length>0&&t.split(Td).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static \u0275fac=function(r){return new(r||e)(E(ct),E(mn))};static \u0275dir=Se({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return e})();var Jv=(()=>{class e{_viewContainer;_context=new kd;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(t,r){this._viewContainer=t,this._thenTemplateRef=r}set ngIf(t){this._context.$implicit=this._context.ngIf=t,this._updateView()}set ngIfThen(t){Uv(t,!1),this._thenTemplateRef=t,this._thenViewRef=null,this._updateView()}set ngIfElse(t){Uv(t,!1),this._elseTemplateRef=t,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(t,r){return!0}static \u0275fac=function(r){return new(r||e)(E(pr),E(Qr))};static \u0275dir=Se({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),kd=class{$implicit=null;ngIf=null};function Uv(e,n){if(e&&!e.createEmbeddedView)throw new x(2020,!1)}function DS(e,n){return new x(2100,!1)}var _S="mediumDate",ES=new M(""),xS=new M(""),tl=(()=>{class e{locale;defaultTimezone;defaultOptions;constructor(t,r,o){this.locale=t,this.defaultTimezone=r,this.defaultOptions=o}transform(t,r,o,i){if(t==null||t===""||t!==t)return null;try{let s=r??this.defaultOptions?.dateFormat??_S,a=o??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return dS(t,s,i||this.locale,a)}catch(s){throw DS(e,s.message)}}static \u0275fac=function(r){return new(r||e)(E($a,16),E(ES,24),E(xS,24))};static \u0275pipe=cv({name:"date",type:e,pure:!0})}return e})();var Z=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=vn({type:e});static \u0275inj=dn({})}return e})(),Kv="browser",SS="server";function Fd(e){return e===SS}var Xv=(()=>{class e{static \u0275prov=S({token:e,providedIn:"root",factory:()=>new Ad(w(Oe),window)})}return e})(),Ad=class{document;window;offset=()=>[0,0];constructor(n,t){this.document=n,this.window=t}setOffset(n){Array.isArray(n)?this.offset=()=>n:this.offset=n}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(n){this.window.scrollTo(n[0],n[1])}scrollToAnchor(n){let t=MS(this.document,n);t&&(this.scrollToElement(t),t.focus())}setHistoryScrollRestoration(n){this.window.history.scrollRestoration=n}scrollToElement(n){let t=n.getBoundingClientRect(),r=t.left+this.window.pageXOffset,o=t.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}};function MS(e,n){let t=e.getElementById(n)||e.getElementsByName(n)[0];if(t)return t;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(n)||i.querySelector(`[name="${n}"]`);if(s)return s}o=r.nextNode()}}return null}var fo=class{};var mi=class{},vi=class{},it=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(n){n?typeof n=="string"?this.lazyInit=()=>{this.headers=new Map,n.split(`
`).forEach(t=>{let r=t.indexOf(":");if(r>0){let o=t.slice(0,r),i=t.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&n instanceof Headers?(this.headers=new Map,n.forEach((t,r)=>{this.addHeaderEntry(r,t)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(n).forEach(([t,r])=>{this.setHeaderEntries(t,r)})}:this.headers=new Map}has(n){return this.init(),this.headers.has(n.toLowerCase())}get(n){this.init();let t=this.headers.get(n.toLowerCase());return t&&t.length>0?t[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(n){return this.init(),this.headers.get(n.toLowerCase())||null}append(n,t){return this.clone({name:n,value:t,op:"a"})}set(n,t){return this.clone({name:n,value:t,op:"s"})}delete(n,t){return this.clone({name:n,value:t,op:"d"})}maybeSetNormalizedName(n,t){this.normalizedNames.has(t)||this.normalizedNames.set(t,n)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(n=>this.applyUpdate(n)),this.lazyUpdate=null))}copyFrom(n){n.init(),Array.from(n.headers.keys()).forEach(t=>{this.headers.set(t,n.headers.get(t)),this.normalizedNames.set(t,n.normalizedNames.get(t))})}clone(n){let t=new e;return t.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,t.lazyUpdate=(this.lazyUpdate||[]).concat([n]),t}applyUpdate(n){let t=n.name.toLowerCase();switch(n.op){case"a":case"s":let r=n.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(n.name,t);let o=(n.op==="a"?this.headers.get(t):void 0)||[];o.push(...r),this.headers.set(t,o);break;case"d":let i=n.value;if(!i)this.headers.delete(t),this.normalizedNames.delete(t);else{let s=this.headers.get(t);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(t),this.normalizedNames.delete(t)):this.headers.set(t,s)}break}}addHeaderEntry(n,t){let r=n.toLowerCase();this.maybeSetNormalizedName(n,r),this.headers.has(r)?this.headers.get(r).push(t):this.headers.set(r,[t])}setHeaderEntries(n,t){let r=(Array.isArray(t)?t:[t]).map(i=>i.toString()),o=n.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(n,o)}forEach(n){this.init(),Array.from(this.normalizedNames.keys()).forEach(t=>n(this.normalizedNames.get(t),this.headers.get(t)))}};var jd=class{encodeKey(n){return e0(n)}encodeValue(n){return e0(n)}decodeKey(n){return decodeURIComponent(n)}decodeValue(n){return decodeURIComponent(n)}};function IS(e,n){let t=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[n.decodeKey(o),""]:[n.decodeKey(o.slice(0,i)),n.decodeValue(o.slice(i+1))],l=t.get(s)||[];l.push(a),t.set(s,l)}),t}var TS=/%(\d[a-f0-9])/gi,kS={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function e0(e){return encodeURIComponent(e).replace(TS,(n,t)=>kS[t]??n)}function nl(e){return`${e}`}var $n=class e{map;encoder;updates=null;cloneFrom=null;constructor(n={}){if(this.encoder=n.encoder||new jd,n.fromString){if(n.fromObject)throw new x(2805,!1);this.map=IS(n.fromString,this.encoder)}else n.fromObject?(this.map=new Map,Object.keys(n.fromObject).forEach(t=>{let r=n.fromObject[t],o=Array.isArray(r)?r.map(nl):[nl(r)];this.map.set(t,o)})):this.map=null}has(n){return this.init(),this.map.has(n)}get(n){this.init();let t=this.map.get(n);return t?t[0]:null}getAll(n){return this.init(),this.map.get(n)||null}keys(){return this.init(),Array.from(this.map.keys())}append(n,t){return this.clone({param:n,value:t,op:"a"})}appendAll(n){let t=[];return Object.keys(n).forEach(r=>{let o=n[r];Array.isArray(o)?o.forEach(i=>{t.push({param:r,value:i,op:"a"})}):t.push({param:r,value:o,op:"a"})}),this.clone(t)}set(n,t){return this.clone({param:n,value:t,op:"s"})}delete(n,t){return this.clone({param:n,value:t,op:"d"})}toString(){return this.init(),this.keys().map(n=>{let t=this.encoder.encodeKey(n);return this.map.get(n).map(r=>t+"="+this.encoder.encodeValue(r)).join("&")}).filter(n=>n!=="").join("&")}clone(n){let t=new e({encoder:this.encoder});return t.cloneFrom=this.cloneFrom||this,t.updates=(this.updates||[]).concat(n),t}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(n=>this.map.set(n,this.cloneFrom.map.get(n))),this.updates.forEach(n=>{switch(n.op){case"a":case"s":let t=(n.op==="a"?this.map.get(n.param):void 0)||[];t.push(nl(n.value)),this.map.set(n.param,t);break;case"d":if(n.value!==void 0){let r=this.map.get(n.param)||[],o=r.indexOf(nl(n.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(n.param,r):this.map.delete(n.param)}else{this.map.delete(n.param);break}}}),this.cloneFrom=this.updates=null)}};var Bd=class{map=new Map;set(n,t){return this.map.set(n,t),this}get(n){return this.map.has(n)||this.map.set(n,n.defaultValue()),this.map.get(n)}delete(n){return this.map.delete(n),this}has(n){return this.map.has(n)}keys(){return this.map.keys()}};function AS(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function t0(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function n0(e){return typeof Blob<"u"&&e instanceof Blob}function r0(e){return typeof FormData<"u"&&e instanceof FormData}function NS(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var pi="Content-Type",rl="Accept",Hd="X-Request-URL",a0="text/plain",l0="application/json",c0=`${l0}, ${a0}, */*`,gi=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(n,t,r,o){this.url=t,this.method=n.toUpperCase();let i;if(AS(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new it,this.context??=new Bd,!this.params)this.params=new $n,this.urlWithParams=t;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=t;else{let a=t.indexOf("?"),l=a===-1?"?":a<t.length-1?"&":"";this.urlWithParams=t+l+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||t0(this.body)||n0(this.body)||r0(this.body)||NS(this.body)?this.body:this.body instanceof $n?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||r0(this.body)?null:n0(this.body)?this.body.type||null:t0(this.body)?null:typeof this.body=="string"?a0:this.body instanceof $n?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?l0:null}clone(n={}){let t=n.method||this.method,r=n.url||this.url,o=n.responseType||this.responseType,i=n.transferCache??this.transferCache,s=n.body!==void 0?n.body:this.body,a=n.withCredentials??this.withCredentials,l=n.reportProgress??this.reportProgress,c=n.headers||this.headers,u=n.params||this.params,h=n.context??this.context;return n.setHeaders!==void 0&&(c=Object.keys(n.setHeaders).reduce((m,g)=>m.set(g,n.setHeaders[g]),c)),n.setParams&&(u=Object.keys(n.setParams).reduce((m,g)=>m.set(g,n.setParams[g]),u)),new e(t,r,s,{params:u,headers:c,context:h,reportProgress:l,responseType:o,withCredentials:a,transferCache:i})}},Un=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(Un||{}),yi=class{headers;status;statusText;url;ok;type;constructor(n,t=200,r="OK"){this.headers=n.headers||new it,this.status=n.status!==void 0?n.status:t,this.statusText=n.statusText||r,this.url=n.url||null,this.ok=this.status>=200&&this.status<300}},ol=class e extends yi{constructor(n={}){super(n)}type=Un.ResponseHeader;clone(n={}){return new e({headers:n.headers||this.headers,status:n.status!==void 0?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}},bi=class e extends yi{body;constructor(n={}){super(n),this.body=n.body!==void 0?n.body:null}type=Un.Response;clone(n={}){return new e({body:n.body!==void 0?n.body:this.body,headers:n.headers||this.headers,status:n.status!==void 0?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}},Bn=class extends yi{name="HttpErrorResponse";message;error;ok=!1;constructor(n){super(n,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${n.url||"(unknown url)"}`:this.message=`Http failure response for ${n.url||"(unknown url)"}: ${n.status} ${n.statusText}`,this.error=n.error||null}},u0=200,RS=204;function Ld(e,n){return{body:n,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var vr=(()=>{class e{handler;constructor(t){this.handler=t}request(t,r,o={}){let i;if(t instanceof gi)i=t;else{let l;o.headers instanceof it?l=o.headers:l=new it(o.headers);let c;o.params&&(o.params instanceof $n?c=o.params:c=new $n({fromObject:o.params})),i=new gi(t,r,o.body!==void 0?o.body:null,{headers:l,context:o.context,params:c,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache})}let s=R(i).pipe(Xt(l=>this.handler.handle(l)));if(t instanceof gi||o.observe==="events")return s;let a=s.pipe(Je(l=>l instanceof bi));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(B(l=>{if(l.body!==null&&!(l.body instanceof ArrayBuffer))throw new x(2806,!1);return l.body}));case"blob":return a.pipe(B(l=>{if(l.body!==null&&!(l.body instanceof Blob))throw new x(2807,!1);return l.body}));case"text":return a.pipe(B(l=>{if(l.body!==null&&typeof l.body!="string")throw new x(2808,!1);return l.body}));case"json":default:return a.pipe(B(l=>l.body))}case"response":return a;default:throw new x(2809,!1)}}delete(t,r={}){return this.request("DELETE",t,r)}get(t,r={}){return this.request("GET",t,r)}head(t,r={}){return this.request("HEAD",t,r)}jsonp(t,r){return this.request("JSONP",t,{params:new $n().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(t,r={}){return this.request("OPTIONS",t,r)}patch(t,r,o={}){return this.request("PATCH",t,Ld(o,r))}post(t,r,o={}){return this.request("POST",t,Ld(o,r))}put(t,r,o={}){return this.request("PUT",t,Ld(o,r))}static \u0275fac=function(r){return new(r||e)(T(mi))};static \u0275prov=S({token:e,factory:e.\u0275fac})}return e})(),OS=/^\)\]\}',?\n/;function o0(e){if(e.url)return e.url;let n=Hd.toLocaleLowerCase();return e.headers.get(n)}var d0=new M(""),Vd=(()=>{class e{fetchImpl=w($d,{optional:!0})?.fetch??((...t)=>globalThis.fetch(...t));ngZone=w(ve);handle(t){return new K(r=>{let o=new AbortController;return this.doRequest(t,o.signal,r).then(Ud,i=>r.error(new Bn({error:i}))),()=>o.abort()})}doRequest(t,r,o){return Jt(this,null,function*(){let i=this.createRequestInit(t),s;try{let g=this.ngZone.runOutsideAngular(()=>this.fetchImpl(t.urlWithParams,b({signal:r},i)));PS(g),o.next({type:Un.Sent}),s=yield g}catch(g){o.error(new Bn({error:g,status:g.status??0,statusText:g.statusText,url:t.urlWithParams,headers:g.headers}));return}let a=new it(s.headers),l=s.statusText,c=o0(s)??t.urlWithParams,u=s.status,h=null;if(t.reportProgress&&o.next(new ol({headers:a,status:u,statusText:l,url:c})),s.body){let g=s.headers.get("content-length"),v=[],y=s.body.getReader(),_=0,A,ae,oe=typeof Zone<"u"&&Zone.current;yield this.ngZone.runOutsideAngular(()=>Jt(this,null,function*(){for(;;){let{done:Lt,value:Wn}=yield y.read();if(Lt)break;if(v.push(Wn),_+=Wn.length,t.reportProgress){ae=t.responseType==="text"?(ae??"")+(A??=new TextDecoder).decode(Wn,{stream:!0}):void 0;let Sr=()=>o.next({type:Un.DownloadProgress,total:g?+g:void 0,loaded:_,partialText:ae});oe?oe.run(Sr):Sr()}}}));let wt=this.concatChunks(v,_);try{let Lt=s.headers.get(pi)??"";h=this.parseBody(t,wt,Lt)}catch(Lt){o.error(new Bn({error:Lt,headers:new it(s.headers),status:s.status,statusText:s.statusText,url:o0(s)??t.urlWithParams}));return}}u===0&&(u=h?u0:0),u>=200&&u<300?(o.next(new bi({body:h,headers:a,status:u,statusText:l,url:c})),o.complete()):o.error(new Bn({error:h,headers:a,status:u,statusText:l,url:c}))})}parseBody(t,r,o){switch(t.responseType){case"json":let i=new TextDecoder().decode(r).replace(OS,"");return i===""?null:JSON.parse(i);case"text":return new TextDecoder().decode(r);case"blob":return new Blob([r],{type:o});case"arraybuffer":return r.buffer}}createRequestInit(t){let r={},o=t.withCredentials?"include":void 0;if(t.headers.forEach((i,s)=>r[i]=s.join(",")),t.headers.has(rl)||(r[rl]=c0),!t.headers.has(pi)){let i=t.detectContentTypeHeader();i!==null&&(r[pi]=i)}return{body:t.serializeBody(),method:t.method,headers:r,credentials:o}}concatChunks(t,r){let o=new Uint8Array(r),i=0;for(let s of t)o.set(s,i),i+=s.length;return o}static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:e.\u0275fac})}return e})(),$d=class{};function Ud(){}function PS(e){e.then(Ud,Ud)}function FS(e,n){return n(e)}function LS(e,n,t){return(r,o)=>nt(t,()=>n(r,i=>e(i,o)))}var f0=new M(""),VS=new M(""),jS=new M("",{providedIn:"root",factory:()=>!0});var i0=(()=>{class e extends mi{backend;injector;chain=null;pendingTasks=w(Fn);contributeToStability=w(jS);constructor(t,r){super(),this.backend=t,this.injector=r}handle(t){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(f0),...this.injector.get(VS,[])]));this.chain=r.reduceRight((o,i)=>LS(o,i,this.injector),FS)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(t,o=>this.backend.handle(o)).pipe(Jn(()=>this.pendingTasks.remove(r)))}else return this.chain(t,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(T(vi),T(He))};static \u0275prov=S({token:e,factory:e.\u0275fac})}return e})();var BS=/^\)\]\}',?\n/,$S=RegExp(`^${Hd}:`,"m");function US(e){return"responseURL"in e&&e.responseURL?e.responseURL:$S.test(e.getAllResponseHeaders())?e.getResponseHeader(Hd):null}var s0=(()=>{class e{xhrFactory;constructor(t){this.xhrFactory=t}handle(t){if(t.method==="JSONP")throw new x(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?de(r.\u0275loadImpl()):R(null)).pipe(Ke(()=>new K(i=>{let s=r.build();if(s.open(t.method,t.urlWithParams),t.withCredentials&&(s.withCredentials=!0),t.headers.forEach((y,_)=>s.setRequestHeader(y,_.join(","))),t.headers.has(rl)||s.setRequestHeader(rl,c0),!t.headers.has(pi)){let y=t.detectContentTypeHeader();y!==null&&s.setRequestHeader(pi,y)}if(t.responseType){let y=t.responseType.toLowerCase();s.responseType=y!=="json"?y:"text"}let a=t.serializeBody(),l=null,c=()=>{if(l!==null)return l;let y=s.statusText||"OK",_=new it(s.getAllResponseHeaders()),A=US(s)||t.url;return l=new ol({headers:_,status:s.status,statusText:y,url:A}),l},u=()=>{let{headers:y,status:_,statusText:A,url:ae}=c(),oe=null;_!==RS&&(oe=typeof s.response>"u"?s.responseText:s.response),_===0&&(_=oe?u0:0);let wt=_>=200&&_<300;if(t.responseType==="json"&&typeof oe=="string"){let Lt=oe;oe=oe.replace(BS,"");try{oe=oe!==""?JSON.parse(oe):null}catch(Wn){oe=Lt,wt&&(wt=!1,oe={error:Wn,text:oe})}}wt?(i.next(new bi({body:oe,headers:y,status:_,statusText:A,url:ae||void 0})),i.complete()):i.error(new Bn({error:oe,headers:y,status:_,statusText:A,url:ae||void 0}))},h=y=>{let{url:_}=c(),A=new Bn({error:y,status:s.status||0,statusText:s.statusText||"Unknown Error",url:_||void 0});i.error(A)},m=!1,g=y=>{m||(i.next(c()),m=!0);let _={type:Un.DownloadProgress,loaded:y.loaded};y.lengthComputable&&(_.total=y.total),t.responseType==="text"&&s.responseText&&(_.partialText=s.responseText),i.next(_)},v=y=>{let _={type:Un.UploadProgress,loaded:y.loaded};y.lengthComputable&&(_.total=y.total),i.next(_)};return s.addEventListener("load",u),s.addEventListener("error",h),s.addEventListener("timeout",h),s.addEventListener("abort",h),t.reportProgress&&(s.addEventListener("progress",g),a!==null&&s.upload&&s.upload.addEventListener("progress",v)),s.send(a),i.next({type:Un.Sent}),()=>{s.removeEventListener("error",h),s.removeEventListener("abort",h),s.removeEventListener("load",u),s.removeEventListener("timeout",h),t.reportProgress&&(s.removeEventListener("progress",g),a!==null&&s.upload&&s.upload.removeEventListener("progress",v)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(T(fo))};static \u0275prov=S({token:e,factory:e.\u0275fac})}return e})(),h0=new M(""),HS="XSRF-TOKEN",zS=new M("",{providedIn:"root",factory:()=>HS}),GS="X-XSRF-TOKEN",qS=new M("",{providedIn:"root",factory:()=>GS}),il=class{},WS=(()=>{class e{doc;platform;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(t,r,o){this.doc=t,this.platform=r,this.cookieName=o}getToken(){if(this.platform==="server")return null;let t=this.doc.cookie||"";return t!==this.lastCookieString&&(this.parseCount++,this.lastToken=Xa(t,this.cookieName),this.lastCookieString=t),this.lastToken}static \u0275fac=function(r){return new(r||e)(T(Oe),T(fr),T(zS))};static \u0275prov=S({token:e,factory:e.\u0275fac})}return e})();function ZS(e,n){let t=e.url.toLowerCase();if(!w(h0)||e.method==="GET"||e.method==="HEAD"||t.startsWith("http://")||t.startsWith("https://"))return n(e);let r=w(il).getToken(),o=w(qS);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),n(e)}var p0=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(p0||{});function YS(e,n){return{\u0275kind:e,\u0275providers:n}}function g0(...e){let n=[vr,s0,i0,{provide:mi,useExisting:i0},{provide:vi,useFactory:()=>w(d0,{optional:!0})??w(s0)},{provide:f0,useValue:ZS,multi:!0},{provide:h0,useValue:!0},{provide:il,useClass:WS}];for(let t of e)n.push(...t.\u0275providers);return no(n)}function m0(){return YS(p0.Fetch,[Vd,{provide:d0,useExisting:Vd},{provide:vi,useExisting:Vd}])}var qd=class extends Qa{supportsDOMEvents=!0},Wd=class e extends qd{static makeCurrent(){zv(new e)}onAndCancel(n,t,r,o){return n.addEventListener(t,r,o),()=>{n.removeEventListener(t,r,o)}}dispatchEvent(n,t){n.dispatchEvent(t)}remove(n){n.remove()}createElement(n,t){return t=t||this.getDefaultDocument(),t.createElement(n)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(n){return n.nodeType===Node.ELEMENT_NODE}isShadowRoot(n){return n instanceof DocumentFragment}getGlobalEventTarget(n,t){return t==="window"?window:t==="document"?n:t==="body"?n.body:null}getBaseHref(n){let t=QS();return t==null?null:JS(t)}resetBaseElement(){Ci=null}getUserAgent(){return window.navigator.userAgent}getCookie(n){return Xa(document.cookie,n)}},Ci=null;function QS(){return Ci=Ci||document.querySelector("base"),Ci?Ci.getAttribute("href"):null}function JS(e){return new URL(e,document.baseURI).pathname}var KS=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:e.\u0275fac})}return e})(),Zd=new M(""),D0=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(t,r){this._zone=r,t.forEach(o=>{o.manager=this}),this._plugins=t.slice().reverse()}addEventListener(t,r,o,i){return this._findPluginFor(r).addEventListener(t,r,o,i)}getZone(){return this._zone}_findPluginFor(t){let r=this._eventNameToPlugin.get(t);if(r)return r;if(r=this._plugins.find(i=>i.supports(t)),!r)throw new x(5101,!1);return this._eventNameToPlugin.set(t,r),r}static \u0275fac=function(r){return new(r||e)(T(Zd),T(ve))};static \u0275prov=S({token:e,factory:e.\u0275fac})}return e})(),al=class{_doc;constructor(n){this._doc=n}manager},sl="ng-app-id";function v0(e){for(let n of e)n.remove()}function y0(e,n){let t=n.createElement("style");return t.textContent=e,t}function XS(e,n,t,r){let o=e.head?.querySelectorAll(`style[${sl}="${n}"],link[${sl}="${n}"]`);if(o)for(let i of o)i.removeAttribute(sl),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&t.set(i.textContent,{usage:0,elements:[i]})}function Yd(e,n){let t=n.createElement("link");return t.setAttribute("rel","stylesheet"),t.setAttribute("href",e),t}var _0=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(t,r,o,i={}){this.doc=t,this.appId=r,this.nonce=o,this.isServer=Fd(i),XS(t,r,this.inline,this.external),this.hosts.add(t.head)}addStyles(t,r){for(let o of t)this.addUsage(o,this.inline,y0);r?.forEach(o=>this.addUsage(o,this.external,Yd))}removeStyles(t,r){for(let o of t)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(t,r,o){let i=r.get(t);i?i.usage++:r.set(t,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(t,this.doc)))})}removeUsage(t,r){let o=r.get(t);o&&(o.usage--,o.usage<=0&&(v0(o.elements),r.delete(t)))}ngOnDestroy(){for(let[,{elements:t}]of[...this.inline,...this.external])v0(t);this.hosts.clear()}addHost(t){this.hosts.add(t);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(t,y0(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(t,Yd(r,this.doc)))}removeHost(t){this.hosts.delete(t)}addElement(t,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(sl,this.appId),t.appendChild(r)}static \u0275fac=function(r){return new(r||e)(T(Oe),T(Ku),T(ed,8),T(fr))};static \u0275prov=S({token:e,factory:e.\u0275fac})}return e})(),Gd={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Jd=/%COMP%/g;var E0="%COMP%",eM=`_nghost-${E0}`,tM=`_ngcontent-${E0}`,nM=!0,rM=new M("",{providedIn:"root",factory:()=>nM});function oM(e){return tM.replace(Jd,e)}function iM(e){return eM.replace(Jd,e)}function x0(e,n){return n.map(t=>t.replace(Jd,e))}var b0=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(t,r,o,i,s,a,l,c=null,u=null){this.eventManager=t,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=l,this.nonce=c,this.tracingService=u,this.platformIsServer=Fd(a),this.defaultRenderer=new wi(t,s,l,this.platformIsServer,this.tracingService)}createRenderer(t,r){if(!t||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===Ut.ShadowDom&&(r=I(b({},r),{encapsulation:Ut.Emulated}));let o=this.getOrCreateRenderer(t,r);return o instanceof ll?o.applyToHost(t):o instanceof Di&&o.applyStyles(),o}getOrCreateRenderer(t,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,l=this.eventManager,c=this.sharedStylesHost,u=this.removeStylesOnCompDestroy,h=this.platformIsServer,m=this.tracingService;switch(r.encapsulation){case Ut.Emulated:i=new ll(l,c,r,this.appId,u,s,a,h,m);break;case Ut.ShadowDom:return new Qd(l,c,t,r,s,a,this.nonce,h,m);default:i=new Di(l,c,r,u,s,a,h,m);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(t){this.rendererByCompId.delete(t)}static \u0275fac=function(r){return new(r||e)(T(D0),T(_0),T(Ku),T(rM),T(Oe),T(fr),T(ve),T(ed),T(co,8))};static \u0275prov=S({token:e,factory:e.\u0275fac})}return e})(),wi=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(n,t,r,o,i){this.eventManager=n,this.doc=t,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(n,t){return t?this.doc.createElementNS(Gd[t]||t,n):this.doc.createElement(n)}createComment(n){return this.doc.createComment(n)}createText(n){return this.doc.createTextNode(n)}appendChild(n,t){(C0(n)?n.content:n).appendChild(t)}insertBefore(n,t,r){n&&(C0(n)?n.content:n).insertBefore(t,r)}removeChild(n,t){t.remove()}selectRootElement(n,t){let r=typeof n=="string"?this.doc.querySelector(n):n;if(!r)throw new x(-5104,!1);return t||(r.textContent=""),r}parentNode(n){return n.parentNode}nextSibling(n){return n.nextSibling}setAttribute(n,t,r,o){if(o){t=o+":"+t;let i=Gd[o];i?n.setAttributeNS(i,t,r):n.setAttribute(t,r)}else n.setAttribute(t,r)}removeAttribute(n,t,r){if(r){let o=Gd[r];o?n.removeAttributeNS(o,t):n.removeAttribute(`${r}:${t}`)}else n.removeAttribute(t)}addClass(n,t){n.classList.add(t)}removeClass(n,t){n.classList.remove(t)}setStyle(n,t,r,o){o&(ln.DashCase|ln.Important)?n.style.setProperty(t,r,o&ln.Important?"important":""):n.style[t]=r}removeStyle(n,t,r){r&ln.DashCase?n.style.removeProperty(t):n.style[t]=""}setProperty(n,t,r){n!=null&&(n[t]=r)}setValue(n,t){n.nodeValue=t}listen(n,t,r,o){if(typeof n=="string"&&(n=En().getGlobalEventTarget(this.doc,n),!n))throw new x(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(n,t,i)),this.eventManager.addEventListener(n,t,i,o)}decoratePreventDefault(n){return t=>{if(t==="__ngUnwrap__")return n;(this.platformIsServer?this.ngZone.runGuarded(()=>n(t)):n(t))===!1&&t.preventDefault()}}};function C0(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var Qd=class extends wi{sharedStylesHost;hostEl;shadowRoot;constructor(n,t,r,o,i,s,a,l,c){super(n,i,s,l,c),this.sharedStylesHost=t,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let u=o.styles;u=x0(o.id,u);for(let m of u){let g=document.createElement("style");a&&g.setAttribute("nonce",a),g.textContent=m,this.shadowRoot.appendChild(g)}let h=o.getExternalStyles?.();if(h)for(let m of h){let g=Yd(m,i);a&&g.setAttribute("nonce",a),this.shadowRoot.appendChild(g)}}nodeOrShadowRoot(n){return n===this.hostEl?this.shadowRoot:n}appendChild(n,t){return super.appendChild(this.nodeOrShadowRoot(n),t)}insertBefore(n,t,r){return super.insertBefore(this.nodeOrShadowRoot(n),t,r)}removeChild(n,t){return super.removeChild(null,t)}parentNode(n){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(n)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Di=class extends wi{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(n,t,r,o,i,s,a,l,c){super(n,i,s,a,l),this.sharedStylesHost=t,this.removeStylesOnCompDestroy=o;let u=r.styles;this.styles=c?x0(c,u):u,this.styleUrls=r.getExternalStyles?.(c)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},ll=class extends Di{contentAttr;hostAttr;constructor(n,t,r,o,i,s,a,l,c){let u=o+"-"+r.id;super(n,t,r,i,s,a,l,c,u),this.contentAttr=oM(u),this.hostAttr=iM(u)}applyToHost(n){this.applyStyles(),this.setAttribute(n,this.hostAttr,"")}createElement(n,t){let r=super.createElement(n,t);return super.setAttribute(r,this.contentAttr,""),r}},sM=(()=>{class e extends al{constructor(t){super(t)}supports(t){return!0}addEventListener(t,r,o,i){return t.addEventListener(r,o,i),()=>this.removeEventListener(t,r,o,i)}removeEventListener(t,r,o,i){return t.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(T(Oe))};static \u0275prov=S({token:e,factory:e.\u0275fac})}return e})(),w0=["alt","control","meta","shift"],aM={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},lM={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},cM=(()=>{class e extends al{constructor(t){super(t)}supports(t){return e.parseEventName(t)!=null}addEventListener(t,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>En().onAndCancel(t,s.domEventName,a,i))}static parseEventName(t){let r=t.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),w0.forEach(c=>{let u=r.indexOf(c);u>-1&&(r.splice(u,1),s+=c+".")}),s+=i,r.length!=0||i.length===0)return null;let l={};return l.domEventName=o,l.fullKey=s,l}static matchEventFullKeyCode(t,r){let o=aM[t.key]||t.key,i="";return r.indexOf("code.")>-1&&(o=t.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),w0.forEach(s=>{if(s!==o){let a=lM[s];a(t)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(t,r,o){return i=>{e.matchEventFullKeyCode(i,t)&&o.runGuarded(()=>r(i))}}static _normalizeKey(t){return t==="esc"?"escape":t}static \u0275fac=function(r){return new(r||e)(T(Oe))};static \u0275prov=S({token:e,factory:e.\u0275fac})}return e})();function S0(e,n){return Pv(b({rootComponent:e},uM(n)))}function uM(e){return{appProviders:[...gM,...e?.providers??[]],platformProviders:pM}}function dM(){Wd.makeCurrent()}function fM(){return new sn}function hM(){return nm(document),document}var pM=[{provide:fr,useValue:Kv},{provide:Xu,useValue:dM,multi:!0},{provide:Oe,useFactory:hM,deps:[]}];var gM=[{provide:Da,useValue:"root"},{provide:sn,useFactory:fM,deps:[]},{provide:Zd,useClass:sM,multi:!0,deps:[Oe]},{provide:Zd,useClass:cM,multi:!0,deps:[Oe]},b0,_0,D0,{provide:Kr,useExisting:b0},{provide:fo,useClass:KS,deps:[]},[]];var M0=(()=>{class e{_doc;constructor(t){this._doc=t}getTitle(){return this._doc.title}setTitle(t){this._doc.title=t||""}static \u0275fac=function(r){return new(r||e)(T(Oe))};static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Kd=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:function(r){let o=null;return r?o=new(r||e):o=T(mM),o},providedIn:"root"})}return e})(),mM=(()=>{class e extends Kd{_doc;constructor(t){super(),this._doc=t}sanitize(t,r){if(r==null)return null;switch(t){case Tt.NONE:return r;case Tt.HTML:return pn(r,"HTML")?vt(r):od(this._doc,String(r)).toString();case Tt.STYLE:return pn(r,"Style")?vt(r):r;case Tt.SCRIPT:if(pn(r,"Script"))return vt(r);throw new x(5200,!1);case Tt.URL:return pn(r,"URL")?vt(r):Ma(String(r));case Tt.RESOURCE_URL:if(pn(r,"ResourceURL"))return vt(r);throw new x(5201,!1);default:throw new x(5202,!1)}}bypassSecurityTrustHtml(t){return um(t)}bypassSecurityTrustStyle(t){return dm(t)}bypassSecurityTrustScript(t){return fm(t)}bypassSecurityTrustUrl(t){return hm(t)}bypassSecurityTrustResourceUrl(t){return pm(t)}static \u0275fac=function(r){return new(r||e)(T(Oe))};static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var U="primary",Fi=Symbol("RouteTitle"),rf=class{params;constructor(n){this.params=n||{}}has(n){return Object.prototype.hasOwnProperty.call(this.params,n)}get(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t[0]:t}return null}getAll(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t:[t]}return[]}get keys(){return Object.keys(this.params)}};function Co(e){return new rf(e)}function vM(e,n,t){let r=t.path.split("/");if(r.length>e.length||t.pathMatch==="full"&&(n.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function yM(e,n){if(e.length!==n.length)return!1;for(let t=0;t<e.length;++t)if(!Zt(e[t],n[t]))return!1;return!0}function Zt(e,n){let t=e?of(e):void 0,r=n?of(n):void 0;if(!t||!r||t.length!=r.length)return!1;let o;for(let i=0;i<t.length;i++)if(o=t[i],!V0(e[o],n[o]))return!1;return!0}function of(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function V0(e,n){if(Array.isArray(e)&&Array.isArray(n)){if(e.length!==n.length)return!1;let t=[...e].sort(),r=[...n].sort();return t.every((o,i)=>r[i]===o)}else return e===n}function j0(e){return e.length>0?e[e.length-1]:null}function Gn(e){return ac(e)?e:gr(e)?de(Promise.resolve(e)):R(e)}var bM={exact:$0,subset:U0},B0={exact:CM,subset:wM,ignored:()=>!0};function T0(e,n,t){return bM[t.paths](e.root,n.root,t.matrixParams)&&B0[t.queryParams](e.queryParams,n.queryParams)&&!(t.fragment==="exact"&&e.fragment!==n.fragment)}function CM(e,n){return Zt(e,n)}function $0(e,n,t){if(!br(e.segments,n.segments)||!dl(e.segments,n.segments,t)||e.numberOfChildren!==n.numberOfChildren)return!1;for(let r in n.children)if(!e.children[r]||!$0(e.children[r],n.children[r],t))return!1;return!0}function wM(e,n){return Object.keys(n).length<=Object.keys(e).length&&Object.keys(n).every(t=>V0(e[t],n[t]))}function U0(e,n,t){return H0(e,n,n.segments,t)}function H0(e,n,t,r){if(e.segments.length>t.length){let o=e.segments.slice(0,t.length);return!(!br(o,t)||n.hasChildren()||!dl(o,t,r))}else if(e.segments.length===t.length){if(!br(e.segments,t)||!dl(e.segments,t,r))return!1;for(let o in n.children)if(!e.children[o]||!U0(e.children[o],n.children[o],r))return!1;return!0}else{let o=t.slice(0,e.segments.length),i=t.slice(e.segments.length);return!br(e.segments,o)||!dl(e.segments,o,r)||!e.children[U]?!1:H0(e.children[U],n,i,r)}}function dl(e,n,t){return n.every((r,o)=>B0[t](e[o].parameters,r.parameters))}var Mn=class{root;queryParams;fragment;_queryParamMap;constructor(n=new ne([],{}),t={},r=null){this.root=n,this.queryParams=t,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Co(this.queryParams),this._queryParamMap}toString(){return EM.serialize(this)}},ne=class{segments;children;parent=null;constructor(n,t){this.segments=n,this.children=t,Object.values(t).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return fl(this)}},yr=class{path;parameters;_parameterMap;constructor(n,t){this.path=n,this.parameters=t}get parameterMap(){return this._parameterMap??=Co(this.parameters),this._parameterMap}toString(){return G0(this)}};function DM(e,n){return br(e,n)&&e.every((t,r)=>Zt(t.parameters,n[r].parameters))}function br(e,n){return e.length!==n.length?!1:e.every((t,r)=>t.path===n[r].path)}function _M(e,n){let t=[];return Object.entries(e.children).forEach(([r,o])=>{r===U&&(t=t.concat(n(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==U&&(t=t.concat(n(o,r)))}),t}var Li=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:()=>new wo,providedIn:"root"})}return e})(),wo=class{parse(n){let t=new af(n);return new Mn(t.parseRootSegment(),t.parseQueryParams(),t.parseFragment())}serialize(n){let t=`/${_i(n.root,!0)}`,r=MM(n.queryParams),o=typeof n.fragment=="string"?`#${xM(n.fragment)}`:"";return`${t}${r}${o}`}},EM=new wo;function fl(e){return e.segments.map(n=>G0(n)).join("/")}function _i(e,n){if(!e.hasChildren())return fl(e);if(n){let t=e.children[U]?_i(e.children[U],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==U&&r.push(`${o}:${_i(i,!1)}`)}),r.length>0?`${t}(${r.join("//")})`:t}else{let t=_M(e,(r,o)=>o===U?[_i(e.children[U],!1)]:[`${o}:${_i(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[U]!=null?`${fl(e)}/${t[0]}`:`${fl(e)}/(${t.join("//")})`}}function z0(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function cl(e){return z0(e).replace(/%3B/gi,";")}function xM(e){return encodeURI(e)}function sf(e){return z0(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function hl(e){return decodeURIComponent(e)}function k0(e){return hl(e.replace(/\+/g,"%20"))}function G0(e){return`${sf(e.path)}${SM(e.parameters)}`}function SM(e){return Object.entries(e).map(([n,t])=>`;${sf(n)}=${sf(t)}`).join("")}function MM(e){let n=Object.entries(e).map(([t,r])=>Array.isArray(r)?r.map(o=>`${cl(t)}=${cl(o)}`).join("&"):`${cl(t)}=${cl(r)}`).filter(t=>t);return n.length?`?${n.join("&")}`:""}var IM=/^[^\/()?;#]+/;function Xd(e){let n=e.match(IM);return n?n[0]:""}var TM=/^[^\/()?;=#]+/;function kM(e){let n=e.match(TM);return n?n[0]:""}var AM=/^[^=?&#]+/;function NM(e){let n=e.match(AM);return n?n[0]:""}var RM=/^[^&#]+/;function OM(e){let n=e.match(RM);return n?n[0]:""}var af=class{url;remaining;constructor(n){this.url=n,this.remaining=n}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new ne([],{}):new ne([],this.parseChildren())}parseQueryParams(){let n={};if(this.consumeOptional("?"))do this.parseQueryParam(n);while(this.consumeOptional("&"));return n}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let n=[];for(this.peekStartsWith("(")||n.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),n.push(this.parseSegment());let t={};this.peekStartsWith("/(")&&(this.capture("/"),t=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(n.length>0||Object.keys(t).length>0)&&(r[U]=new ne(n,t)),r}parseSegment(){let n=Xd(this.remaining);if(n===""&&this.peekStartsWith(";"))throw new x(4009,!1);return this.capture(n),new yr(hl(n),this.parseMatrixParams())}parseMatrixParams(){let n={};for(;this.consumeOptional(";");)this.parseParam(n);return n}parseParam(n){let t=kM(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let o=Xd(this.remaining);o&&(r=o,this.capture(r))}n[hl(t)]=hl(r)}parseQueryParam(n){let t=NM(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let s=OM(this.remaining);s&&(r=s,this.capture(r))}let o=k0(t),i=k0(r);if(n.hasOwnProperty(o)){let s=n[o];Array.isArray(s)||(s=[s],n[o]=s),s.push(i)}else n[o]=i}parseParens(n){let t={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=Xd(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new x(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):n&&(i=U);let s=this.parseChildren();t[i]=Object.keys(s).length===1?s[U]:new ne([],s),this.consumeOptional("//")}return t}peekStartsWith(n){return this.remaining.startsWith(n)}consumeOptional(n){return this.peekStartsWith(n)?(this.remaining=this.remaining.substring(n.length),!0):!1}capture(n){if(!this.consumeOptional(n))throw new x(4011,!1)}};function q0(e){return e.segments.length>0?new ne([],{[U]:e}):e}function W0(e){let n={};for(let[r,o]of Object.entries(e.children)){let i=W0(o);if(r===U&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))n[s]=a;else(i.segments.length>0||i.hasChildren())&&(n[r]=i)}let t=new ne(e.segments,n);return PM(t)}function PM(e){if(e.numberOfChildren===1&&e.children[U]){let n=e.children[U];return new ne(e.segments.concat(n.segments),n.children)}return e}function Cr(e){return e instanceof Mn}function FM(e,n,t=null,r=null){let o=Z0(e);return Y0(o,n,t,r)}function Z0(e){let n;function t(i){let s={};for(let l of i.children){let c=t(l);s[l.outlet]=c}let a=new ne(i.url,s);return i===e&&(n=a),a}let r=t(e.root),o=q0(r);return n??o}function Y0(e,n,t,r){let o=e;for(;o.parent;)o=o.parent;if(n.length===0)return ef(o,o,o,t,r);let i=LM(n);if(i.toRoot())return ef(o,o,new ne([],{}),t,r);let s=VM(i,o,e),a=s.processChildren?xi(s.segmentGroup,s.index,i.commands):J0(s.segmentGroup,s.index,i.commands);return ef(o,s.segmentGroup,a,t,r)}function pl(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function Ii(e){return typeof e=="object"&&e!=null&&e.outlets}function ef(e,n,t,r,o){let i={};r&&Object.entries(r).forEach(([l,c])=>{i[l]=Array.isArray(c)?c.map(u=>`${u}`):`${c}`});let s;e===n?s=t:s=Q0(e,n,t);let a=q0(W0(s));return new Mn(a,i,o)}function Q0(e,n,t){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===n?r[o]=t:r[o]=Q0(i,n,t)}),new ne(e.segments,r)}var gl=class{isAbsolute;numberOfDoubleDots;commands;constructor(n,t,r){if(this.isAbsolute=n,this.numberOfDoubleDots=t,this.commands=r,n&&r.length>0&&pl(r[0]))throw new x(4003,!1);let o=r.find(Ii);if(o&&o!==j0(r))throw new x(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function LM(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new gl(!0,0,e);let n=0,t=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([l,c])=>{a[l]=typeof c=="string"?c.split("/"):c}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,l)=>{l==0&&a==="."||(l==0&&a===""?t=!0:a===".."?n++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new gl(t,n,r)}var vo=class{segmentGroup;processChildren;index;constructor(n,t,r){this.segmentGroup=n,this.processChildren=t,this.index=r}};function VM(e,n,t){if(e.isAbsolute)return new vo(n,!0,0);if(!t)return new vo(n,!1,NaN);if(t.parent===null)return new vo(t,!0,0);let r=pl(e.commands[0])?0:1,o=t.segments.length-1+r;return jM(t,o,e.numberOfDoubleDots)}function jM(e,n,t){let r=e,o=n,i=t;for(;i>o;){if(i-=o,r=r.parent,!r)throw new x(4005,!1);o=r.segments.length}return new vo(r,!1,o-i)}function BM(e){return Ii(e[0])?e[0].outlets:{[U]:e}}function J0(e,n,t){if(e??=new ne([],{}),e.segments.length===0&&e.hasChildren())return xi(e,n,t);let r=$M(e,n,t),o=t.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new ne(e.segments.slice(0,r.pathIndex),{});return i.children[U]=new ne(e.segments.slice(r.pathIndex),e.children),xi(i,0,o)}else return r.match&&o.length===0?new ne(e.segments,{}):r.match&&!e.hasChildren()?lf(e,n,t):r.match?xi(e,0,o):lf(e,n,t)}function xi(e,n,t){if(t.length===0)return new ne(e.segments,{});{let r=BM(t),o={};if(Object.keys(r).some(i=>i!==U)&&e.children[U]&&e.numberOfChildren===1&&e.children[U].segments.length===0){let i=xi(e.children[U],n,t);return new ne(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=J0(e.children[i],n,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new ne(e.segments,o)}}function $M(e,n,t){let r=0,o=n,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=t.length)return i;let s=e.segments[o],a=t[r];if(Ii(a))break;let l=`${a}`,c=r<t.length-1?t[r+1]:null;if(o>0&&l===void 0)break;if(l&&c&&typeof c=="object"&&c.outlets===void 0){if(!N0(l,c,s))return i;r+=2}else{if(!N0(l,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function lf(e,n,t){let r=e.segments.slice(0,n),o=0;for(;o<t.length;){let i=t[o];if(Ii(i)){let l=UM(i.outlets);return new ne(r,l)}if(o===0&&pl(t[0])){let l=e.segments[n];r.push(new yr(l.path,A0(t[0]))),o++;continue}let s=Ii(i)?i.outlets[U]:`${i}`,a=o<t.length-1?t[o+1]:null;s&&a&&pl(a)?(r.push(new yr(s,A0(a))),o+=2):(r.push(new yr(s,{})),o++)}return new ne(r,{})}function UM(e){let n={};return Object.entries(e).forEach(([t,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(n[t]=lf(new ne([],{}),0,r))}),n}function A0(e){let n={};return Object.entries(e).forEach(([t,r])=>n[t]=`${r}`),n}function N0(e,n,t){return e==t.path&&Zt(n,t.parameters)}var Si="imperative",Ne=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(Ne||{}),Ct=class{id;url;constructor(n,t){this.id=n,this.url=t}},Do=class extends Ct{type=Ne.NavigationStart;navigationTrigger;restoredState;constructor(n,t,r="imperative",o=null){super(n,t),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},Ot=class extends Ct{urlAfterRedirects;type=Ne.NavigationEnd;constructor(n,t,r){super(n,t),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},pt=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(pt||{}),ml=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(ml||{}),Sn=class extends Ct{reason;code;type=Ne.NavigationCancel;constructor(n,t,r,o){super(n,t),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},Hn=class extends Ct{reason;code;type=Ne.NavigationSkipped;constructor(n,t,r,o){super(n,t),this.reason=r,this.code=o}},Ti=class extends Ct{error;target;type=Ne.NavigationError;constructor(n,t,r,o){super(n,t),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},vl=class extends Ct{urlAfterRedirects;state;type=Ne.RoutesRecognized;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},cf=class extends Ct{urlAfterRedirects;state;type=Ne.GuardsCheckStart;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},uf=class extends Ct{urlAfterRedirects;state;shouldActivate;type=Ne.GuardsCheckEnd;constructor(n,t,r,o,i){super(n,t),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},df=class extends Ct{urlAfterRedirects;state;type=Ne.ResolveStart;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ff=class extends Ct{urlAfterRedirects;state;type=Ne.ResolveEnd;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},hf=class{route;type=Ne.RouteConfigLoadStart;constructor(n){this.route=n}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},pf=class{route;type=Ne.RouteConfigLoadEnd;constructor(n){this.route=n}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},gf=class{snapshot;type=Ne.ChildActivationStart;constructor(n){this.snapshot=n}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},mf=class{snapshot;type=Ne.ChildActivationEnd;constructor(n){this.snapshot=n}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},vf=class{snapshot;type=Ne.ActivationStart;constructor(n){this.snapshot=n}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},yf=class{snapshot;type=Ne.ActivationEnd;constructor(n){this.snapshot=n}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},yl=class{routerEvent;position;anchor;type=Ne.Scroll;constructor(n,t,r){this.routerEvent=n,this.position=t,this.anchor=r}toString(){let n=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${n}')`}},ki=class{},_o=class{url;navigationBehaviorOptions;constructor(n,t){this.url=n,this.navigationBehaviorOptions=t}};function HM(e,n){return e.providers&&!e._injector&&(e._injector=Ra(e.providers,n,`Route: ${e.path}`)),e._injector??n}function Rt(e){return e.outlet||U}function zM(e,n){let t=e.filter(r=>Rt(r)===n);return t.push(...e.filter(r=>Rt(r)!==n)),t}function Vi(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let n=e.parent;n;n=n.parent){let t=n.routeConfig;if(t?._loadedInjector)return t._loadedInjector;if(t?._injector)return t._injector}return null}var bf=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return Vi(this.route?.snapshot)??this.rootInjector}constructor(n){this.rootInjector=n,this.children=new ji(this.rootInjector)}},ji=(()=>{class e{rootInjector;contexts=new Map;constructor(t){this.rootInjector=t}onChildOutletCreated(t,r){let o=this.getOrCreateContext(t);o.outlet=r,this.contexts.set(t,o)}onChildOutletDestroyed(t){let r=this.getContext(t);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let t=this.contexts;return this.contexts=new Map,t}onOutletReAttached(t){this.contexts=t}getOrCreateContext(t){let r=this.getContext(t);return r||(r=new bf(this.rootInjector),this.contexts.set(t,r)),r}getContext(t){return this.contexts.get(t)||null}static \u0275fac=function(r){return new(r||e)(T(He))};static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),bl=class{_root;constructor(n){this._root=n}get root(){return this._root.value}parent(n){let t=this.pathFromRoot(n);return t.length>1?t[t.length-2]:null}children(n){let t=Cf(n,this._root);return t?t.children.map(r=>r.value):[]}firstChild(n){let t=Cf(n,this._root);return t&&t.children.length>0?t.children[0].value:null}siblings(n){let t=wf(n,this._root);return t.length<2?[]:t[t.length-2].children.map(o=>o.value).filter(o=>o!==n)}pathFromRoot(n){return wf(n,this._root).map(t=>t.value)}};function Cf(e,n){if(e===n.value)return n;for(let t of n.children){let r=Cf(e,t);if(r)return r}return null}function wf(e,n){if(e===n.value)return[n];for(let t of n.children){let r=wf(e,t);if(r.length)return r.unshift(n),r}return[]}var ht=class{value;children;constructor(n,t){this.value=n,this.children=t}toString(){return`TreeNode(${this.value})`}};function mo(e){let n={};return e&&e.children.forEach(t=>n[t.value.outlet]=t),n}var Cl=class extends bl{snapshot;constructor(n,t){super(n),this.snapshot=t,kf(this,n)}toString(){return this.snapshot.toString()}};function K0(e){let n=GM(e),t=new De([new yr("",{})]),r=new De({}),o=new De({}),i=new De({}),s=new De(""),a=new zn(t,r,i,s,o,U,e,n.root);return a.snapshot=n.root,new Cl(new ht(a,[]),n)}function GM(e){let n={},t={},r={},o="",i=new yo([],n,r,o,t,U,e,null,{});return new Dl("",new ht(i,[]))}var zn=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(n,t,r,o,i,s,a,l){this.urlSubject=n,this.paramsSubject=t,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=l,this.title=this.dataSubject?.pipe(B(c=>c[Fi]))??R(void 0),this.url=n,this.params=t,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(B(n=>Co(n))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(B(n=>Co(n))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function wl(e,n,t="emptyOnly"){let r,{routeConfig:o}=e;return n!==null&&(t==="always"||o?.path===""||!n.component&&!n.routeConfig?.loadComponent)?r={params:b(b({},n.params),e.params),data:b(b({},n.data),e.data),resolve:b(b(b(b({},e.data),n.data),o?.data),e._resolvedData)}:r={params:b({},e.params),data:b({},e.data),resolve:b(b({},e.data),e._resolvedData??{})},o&&ey(o)&&(r.resolve[Fi]=o.title),r}var yo=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[Fi]}constructor(n,t,r,o,i,s,a,l,c){this.url=n,this.params=t,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=l,this._resolve=c}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Co(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Co(this.queryParams),this._queryParamMap}toString(){let n=this.url.map(r=>r.toString()).join("/"),t=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${n}', path:'${t}')`}},Dl=class extends bl{url;constructor(n,t){super(t),this.url=n,kf(this,t)}toString(){return X0(this._root)}};function kf(e,n){n.value._routerState=e,n.children.forEach(t=>kf(e,t))}function X0(e){let n=e.children.length>0?` { ${e.children.map(X0).join(", ")} } `:"";return`${e.value}${n}`}function tf(e){if(e.snapshot){let n=e.snapshot,t=e._futureSnapshot;e.snapshot=t,Zt(n.queryParams,t.queryParams)||e.queryParamsSubject.next(t.queryParams),n.fragment!==t.fragment&&e.fragmentSubject.next(t.fragment),Zt(n.params,t.params)||e.paramsSubject.next(t.params),yM(n.url,t.url)||e.urlSubject.next(t.url),Zt(n.data,t.data)||e.dataSubject.next(t.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function Df(e,n){let t=Zt(e.params,n.params)&&DM(e.url,n.url),r=!e.parent!=!n.parent;return t&&!r&&(!e.parent||Df(e.parent,n.parent))}function ey(e){return typeof e.title=="string"||e.title===null}var qM=new M(""),Af=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=U;activateEvents=new me;deactivateEvents=new me;attachEvents=new me;detachEvents=new me;routerOutletData=Zg(void 0);parentContexts=w(ji);location=w(pr);changeDetector=w(ye);inputBinder=w(Sl,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(t){if(t.name){let{firstChange:r,previousValue:o}=t.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(t){return this.parentContexts.getContext(t)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let t=this.parentContexts.getContext(this.name);t?.route&&(t.attachRef?this.attach(t.attachRef,t.route):this.activateWith(t.route,t.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new x(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new x(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new x(4012,!1);this.location.detach();let t=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(t.instance),t}attach(t,r){this.activated=t,this._activatedRoute=r,this.location.insert(t.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(t.instance)}deactivate(){if(this.activated){let t=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new x(4013,!1);this._activatedRoute=t;let o=this.location,s=t.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,l=new _f(t,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:l,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=Se({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[Ht]})}return e})(),_f=class e{route;childContexts;parent;outletData;__ngOutletInjector(n){return new e(this.route,this.childContexts,n,this.outletData)}constructor(n,t,r,o){this.route=n,this.childContexts=t,this.parent=r,this.outletData=o}get(n,t){return n===zn?this.route:n===ji?this.childContexts:n===qM?this.outletData:this.parent.get(n,t)}},Sl=new M(""),R0=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(t){this.unsubscribeFromRouteData(t),this.subscribeToRouteData(t)}unsubscribeFromRouteData(t){this.outletDataSubscriptions.get(t)?.unsubscribe(),this.outletDataSubscriptions.delete(t)}subscribeToRouteData(t){let{activatedRoute:r}=t,o=Go([r.queryParams,r.params,r.data]).pipe(Ke(([i,s,a],l)=>(a=b(b(b({},i),s),a),l===0?R(a):Promise.resolve(a)))).subscribe(i=>{if(!t.isActivated||!t.activatedComponentRef||t.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(t);return}let s=Lv(r.component);if(!s){this.unsubscribeFromRouteData(t);return}for(let{templateName:a}of s.inputs)t.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(t,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:e.\u0275fac})}return e})();function WM(e,n,t){let r=Ai(e,n._root,t?t._root:void 0);return new Cl(r,n)}function Ai(e,n,t){if(t&&e.shouldReuseRoute(n.value,t.value.snapshot)){let r=t.value;r._futureSnapshot=n.value;let o=ZM(e,n,t);return new ht(r,o)}else{if(e.shouldAttach(n.value)){let i=e.retrieve(n.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=n.value,s.children=n.children.map(a=>Ai(e,a)),s}}let r=YM(n.value),o=n.children.map(i=>Ai(e,i));return new ht(r,o)}}function ZM(e,n,t){return n.children.map(r=>{for(let o of t.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return Ai(e,r,o);return Ai(e,r)})}function YM(e){return new zn(new De(e.url),new De(e.params),new De(e.queryParams),new De(e.fragment),new De(e.data),e.outlet,e.component,e)}var Ni=class{redirectTo;navigationBehaviorOptions;constructor(n,t){this.redirectTo=n,this.navigationBehaviorOptions=t}},ty="ngNavigationCancelingError";function _l(e,n){let{redirectTo:t,navigationBehaviorOptions:r}=Cr(n)?{redirectTo:n,navigationBehaviorOptions:void 0}:n,o=ny(!1,pt.Redirect);return o.url=t,o.navigationBehaviorOptions=r,o}function ny(e,n){let t=new Error(`NavigationCancelingError: ${e||""}`);return t[ty]=!0,t.cancellationCode=n,t}function QM(e){return ry(e)&&Cr(e.url)}function ry(e){return!!e&&e[ty]}var JM=(e,n,t,r)=>B(o=>(new Ef(n,o.targetRouterState,o.currentRouterState,t,r).activate(e),o)),Ef=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(n,t,r,o,i){this.routeReuseStrategy=n,this.futureState=t,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(n){let t=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(t,r,n),tf(this.futureState.root),this.activateChildRoutes(t,r,n)}deactivateChildRoutes(n,t,r){let o=mo(t);n.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(n,t,r){let o=n.value,i=t?t.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(n,t,s.children)}else this.deactivateChildRoutes(n,t,r);else i&&this.deactivateRouteAndItsChildren(t,r)}deactivateRouteAndItsChildren(n,t){n.value.component&&this.routeReuseStrategy.shouldDetach(n.value.snapshot)?this.detachAndStoreRouteSubtree(n,t):this.deactivateRouteAndOutlet(n,t)}detachAndStoreRouteSubtree(n,t){let r=t.getContext(n.value.outlet),o=r&&n.value.component?r.children:t,i=mo(n);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(n.value.snapshot,{componentRef:s,route:n,contexts:a})}}deactivateRouteAndOutlet(n,t){let r=t.getContext(n.value.outlet),o=r&&n.value.component?r.children:t,i=mo(n);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(n,t,r){let o=mo(t);n.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new yf(i.value.snapshot))}),n.children.length&&this.forwardEvent(new mf(n.value.snapshot))}activateRoutes(n,t,r){let o=n.value,i=t?t.value:null;if(tf(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(n,t,s.children)}else this.activateChildRoutes(n,t,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),tf(a.route.value),this.activateChildRoutes(n,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(n,null,s.children)}else this.activateChildRoutes(n,null,r)}},El=class{path;route;constructor(n){this.path=n,this.route=this.path[this.path.length-1]}},bo=class{component;route;constructor(n,t){this.component=n,this.route=t}};function KM(e,n,t){let r=e._root,o=n?n._root:null;return Ei(r,o,t,[r.value])}function XM(e){let n=e.routeConfig?e.routeConfig.canActivateChild:null;return!n||n.length===0?null:{node:e,guards:n}}function xo(e,n){let t=Symbol(),r=n.get(e,t);return r===t?typeof e=="function"&&!jp(e)?e:n.get(e):r}function Ei(e,n,t,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=mo(n);return e.children.forEach(s=>{e1(s,i[s.value.outlet],t,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>Mi(a,t.getContext(s),o)),o}function e1(e,n,t,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=n?n.value:null,a=t?t.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let l=t1(s,i,i.routeConfig.runGuardsAndResolvers);l?o.canActivateChecks.push(new El(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?Ei(e,n,a?a.children:null,r,o):Ei(e,n,t,r,o),l&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new bo(a.outlet.component,s))}else s&&Mi(n,a,o),o.canActivateChecks.push(new El(r)),i.component?Ei(e,null,a?a.children:null,r,o):Ei(e,null,t,r,o);return o}function t1(e,n,t){if(typeof t=="function")return t(e,n);switch(t){case"pathParamsChange":return!br(e.url,n.url);case"pathParamsOrQueryParamsChange":return!br(e.url,n.url)||!Zt(e.queryParams,n.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Df(e,n)||!Zt(e.queryParams,n.queryParams);case"paramsChange":default:return!Df(e,n)}}function Mi(e,n,t){let r=mo(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?n?Mi(s,n.children.getContext(i),t):Mi(s,null,t):Mi(s,n,t)}),o.component?n&&n.outlet&&n.outlet.isActivated?t.canDeactivateChecks.push(new bo(n.outlet.component,o)):t.canDeactivateChecks.push(new bo(null,o)):t.canDeactivateChecks.push(new bo(null,o))}function Bi(e){return typeof e=="function"}function n1(e){return typeof e=="boolean"}function r1(e){return e&&Bi(e.canLoad)}function o1(e){return e&&Bi(e.canActivate)}function i1(e){return e&&Bi(e.canActivateChild)}function s1(e){return e&&Bi(e.canDeactivate)}function a1(e){return e&&Bi(e.canMatch)}function oy(e){return e instanceof _t||e?.name==="EmptyError"}var ul=Symbol("INITIAL_VALUE");function Eo(){return Ke(e=>Go(e.map(n=>n.pipe(en(1),fc(ul)))).pipe(B(n=>{for(let t of n)if(t!==!0){if(t===ul)return ul;if(t===!1||l1(t))return t}return!0}),Je(n=>n!==ul),en(1)))}function l1(e){return Cr(e)||e instanceof Ni}function c1(e,n){return Ee(t=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=t;return s.length===0&&i.length===0?R(I(b({},t),{guardsResult:!0})):u1(s,r,o,e).pipe(Ee(a=>a&&n1(a)?d1(r,i,e,n):R(a)),B(a=>I(b({},t),{guardsResult:a})))})}function u1(e,n,t,r){return de(e).pipe(Ee(o=>m1(o.component,o.route,t,n,r)),tn(o=>o!==!0,!0))}function d1(e,n,t,r){return de(n).pipe(Xt(o=>Fr(h1(o.route.parent,r),f1(o.route,r),g1(e,o.path,t),p1(e,o.route,t))),tn(o=>o!==!0,!0))}function f1(e,n){return e!==null&&n&&n(new vf(e)),R(!0)}function h1(e,n){return e!==null&&n&&n(new gf(e)),R(!0)}function p1(e,n,t){let r=n.routeConfig?n.routeConfig.canActivate:null;if(!r||r.length===0)return R(!0);let o=r.map(i=>Ps(()=>{let s=Vi(n)??t,a=xo(i,s),l=o1(a)?a.canActivate(n,e):nt(s,()=>a(n,e));return Gn(l).pipe(tn())}));return R(o).pipe(Eo())}function g1(e,n,t){let r=n[n.length-1],i=n.slice(0,n.length-1).reverse().map(s=>XM(s)).filter(s=>s!==null).map(s=>Ps(()=>{let a=s.guards.map(l=>{let c=Vi(s.node)??t,u=xo(l,c),h=i1(u)?u.canActivateChild(r,e):nt(c,()=>u(r,e));return Gn(h).pipe(tn())});return R(a).pipe(Eo())}));return R(i).pipe(Eo())}function m1(e,n,t,r,o){let i=n&&n.routeConfig?n.routeConfig.canDeactivate:null;if(!i||i.length===0)return R(!0);let s=i.map(a=>{let l=Vi(n)??o,c=xo(a,l),u=s1(c)?c.canDeactivate(e,n,t,r):nt(l,()=>c(e,n,t,r));return Gn(u).pipe(tn())});return R(s).pipe(Eo())}function v1(e,n,t,r){let o=n.canLoad;if(o===void 0||o.length===0)return R(!0);let i=o.map(s=>{let a=xo(s,e),l=r1(a)?a.canLoad(n,t):nt(e,()=>a(n,t));return Gn(l)});return R(i).pipe(Eo(),iy(r))}function iy(e){return rc(ge(n=>{if(typeof n!="boolean")throw _l(e,n)}),B(n=>n===!0))}function y1(e,n,t,r){let o=n.canMatch;if(!o||o.length===0)return R(!0);let i=o.map(s=>{let a=xo(s,e),l=a1(a)?a.canMatch(n,t):nt(e,()=>a(n,t));return Gn(l)});return R(i).pipe(Eo(),iy(r))}var Ri=class{segmentGroup;constructor(n){this.segmentGroup=n||null}},Oi=class extends Error{urlTree;constructor(n){super(),this.urlTree=n}};function go(e){return Pr(new Ri(e))}function b1(e){return Pr(new x(4e3,!1))}function C1(e){return Pr(ny(!1,pt.GuardRejected))}var xf=class{urlSerializer;urlTree;constructor(n,t){this.urlSerializer=n,this.urlTree=t}lineralizeSegments(n,t){let r=[],o=t.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return R(r);if(o.numberOfChildren>1||!o.children[U])return b1(`${n.redirectTo}`);o=o.children[U]}}applyRedirectCommands(n,t,r,o,i){if(typeof t!="string"){let a=t,{queryParams:l,fragment:c,routeConfig:u,url:h,outlet:m,params:g,data:v,title:y}=o,_=nt(i,()=>a({params:g,data:v,queryParams:l,fragment:c,routeConfig:u,url:h,outlet:m,title:y}));if(_ instanceof Mn)throw new Oi(_);t=_}let s=this.applyRedirectCreateUrlTree(t,this.urlSerializer.parse(t),n,r);if(t[0]==="/")throw new Oi(s);return s}applyRedirectCreateUrlTree(n,t,r,o){let i=this.createSegmentGroup(n,t.root,r,o);return new Mn(i,this.createQueryParams(t.queryParams,this.urlTree.queryParams),t.fragment)}createQueryParams(n,t){let r={};return Object.entries(n).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=t[a]}else r[o]=i}),r}createSegmentGroup(n,t,r,o){let i=this.createSegments(n,t.segments,r,o),s={};return Object.entries(t.children).forEach(([a,l])=>{s[a]=this.createSegmentGroup(n,l,r,o)}),new ne(i,s)}createSegments(n,t,r,o){return t.map(i=>i.path[0]===":"?this.findPosParam(n,i,o):this.findOrReturn(i,r))}findPosParam(n,t,r){let o=r[t.path.substring(1)];if(!o)throw new x(4001,!1);return o}findOrReturn(n,t){let r=0;for(let o of t){if(o.path===n.path)return t.splice(r),o;r++}return n}},Sf={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function w1(e,n,t,r,o){let i=sy(e,n,t);return i.matched?(r=HM(n,r),y1(r,n,t,o).pipe(B(s=>s===!0?i:b({},Sf)))):R(i)}function sy(e,n,t){if(n.path==="**")return D1(t);if(n.path==="")return n.pathMatch==="full"&&(e.hasChildren()||t.length>0)?b({},Sf):{matched:!0,consumedSegments:[],remainingSegments:t,parameters:{},positionalParamSegments:{}};let o=(n.matcher||vM)(t,e,n);if(!o)return b({},Sf);let i={};Object.entries(o.posParams??{}).forEach(([a,l])=>{i[a]=l.path});let s=o.consumed.length>0?b(b({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:t.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function D1(e){return{matched:!0,parameters:e.length>0?j0(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function O0(e,n,t,r){return t.length>0&&x1(e,t,r)?{segmentGroup:new ne(n,E1(r,new ne(t,e.children))),slicedSegments:[]}:t.length===0&&S1(e,t,r)?{segmentGroup:new ne(e.segments,_1(e,t,r,e.children)),slicedSegments:t}:{segmentGroup:new ne(e.segments,e.children),slicedSegments:t}}function _1(e,n,t,r){let o={};for(let i of t)if(Ml(e,n,i)&&!r[Rt(i)]){let s=new ne([],{});o[Rt(i)]=s}return b(b({},r),o)}function E1(e,n){let t={};t[U]=n;for(let r of e)if(r.path===""&&Rt(r)!==U){let o=new ne([],{});t[Rt(r)]=o}return t}function x1(e,n,t){return t.some(r=>Ml(e,n,r)&&Rt(r)!==U)}function S1(e,n,t){return t.some(r=>Ml(e,n,r))}function Ml(e,n,t){return(e.hasChildren()||n.length>0)&&t.pathMatch==="full"?!1:t.path===""}function M1(e,n,t){return n.length===0&&!e.children[t]}var Mf=class{};function I1(e,n,t,r,o,i,s="emptyOnly"){return new If(e,n,t,r,o,s,i).recognize()}var T1=31,If=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(n,t,r,o,i,s,a){this.injector=n,this.configLoader=t,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new xf(this.urlSerializer,this.urlTree)}noMatchError(n){return new x(4002,`'${n.segmentGroup}'`)}recognize(){let n=O0(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(n).pipe(B(({children:t,rootSnapshot:r})=>{let o=new ht(r,t),i=new Dl("",o),s=FM(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(n){let t=new yo([],Object.freeze({}),Object.freeze(b({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),U,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,n,U,t).pipe(B(r=>({children:r,rootSnapshot:t})),Et(r=>{if(r instanceof Oi)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof Ri?this.noMatchError(r):r}))}processSegmentGroup(n,t,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(n,t,r,i):this.processSegment(n,t,r,r.segments,o,!0,i).pipe(B(s=>s instanceof ht?[s]:[]))}processChildren(n,t,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return de(i).pipe(Xt(s=>{let a=r.children[s],l=zM(t,s);return this.processSegmentGroup(n,l,a,s,o)}),dc((s,a)=>(s.push(...a),s)),An(null),uc(),Ee(s=>{if(s===null)return go(r);let a=ay(s);return k1(a),R(a)}))}processSegment(n,t,r,o,i,s,a){return de(t).pipe(Xt(l=>this.processSegmentAgainstRoute(l._injector??n,t,l,r,o,i,s,a).pipe(Et(c=>{if(c instanceof Ri)return R(null);throw c}))),tn(l=>!!l),Et(l=>{if(oy(l))return M1(r,o,i)?R(new Mf):go(r);throw l}))}processSegmentAgainstRoute(n,t,r,o,i,s,a,l){return Rt(r)!==s&&(s===U||!Ml(o,i,r))?go(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(n,o,r,i,s,l):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(n,o,t,r,i,s,l):go(o)}expandSegmentAgainstRouteUsingRedirect(n,t,r,o,i,s,a){let{matched:l,parameters:c,consumedSegments:u,positionalParamSegments:h,remainingSegments:m}=sy(t,o,i);if(!l)return go(t);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>T1&&(this.allowRedirects=!1));let g=new yo(i,c,Object.freeze(b({},this.urlTree.queryParams)),this.urlTree.fragment,P0(o),Rt(o),o.component??o._loadedComponent??null,o,F0(o)),v=wl(g,a,this.paramsInheritanceStrategy);g.params=Object.freeze(v.params),g.data=Object.freeze(v.data);let y=this.applyRedirects.applyRedirectCommands(u,o.redirectTo,h,g,n);return this.applyRedirects.lineralizeSegments(o,y).pipe(Ee(_=>this.processSegment(n,r,t,_.concat(m),s,!1,a)))}matchSegmentAgainstRoute(n,t,r,o,i,s){let a=w1(t,r,o,n,this.urlSerializer);return r.path==="**"&&(t.children={}),a.pipe(Ke(l=>l.matched?(n=r._injector??n,this.getChildConfig(n,r,o).pipe(Ke(({routes:c})=>{let u=r._loadedInjector??n,{parameters:h,consumedSegments:m,remainingSegments:g}=l,v=new yo(m,h,Object.freeze(b({},this.urlTree.queryParams)),this.urlTree.fragment,P0(r),Rt(r),r.component??r._loadedComponent??null,r,F0(r)),y=wl(v,s,this.paramsInheritanceStrategy);v.params=Object.freeze(y.params),v.data=Object.freeze(y.data);let{segmentGroup:_,slicedSegments:A}=O0(t,m,g,c);if(A.length===0&&_.hasChildren())return this.processChildren(u,c,_,v).pipe(B(oe=>new ht(v,oe)));if(c.length===0&&A.length===0)return R(new ht(v,[]));let ae=Rt(r)===i;return this.processSegment(u,c,_,A,ae?U:i,!0,v).pipe(B(oe=>new ht(v,oe instanceof ht?[oe]:[])))}))):go(t)))}getChildConfig(n,t,r){return t.children?R({routes:t.children,injector:n}):t.loadChildren?t._loadedRoutes!==void 0?R({routes:t._loadedRoutes,injector:t._loadedInjector}):v1(n,t,r,this.urlSerializer).pipe(Ee(o=>o?this.configLoader.loadChildren(n,t).pipe(ge(i=>{t._loadedRoutes=i.routes,t._loadedInjector=i.injector})):C1(t))):R({routes:[],injector:n})}};function k1(e){e.sort((n,t)=>n.value.outlet===U?-1:t.value.outlet===U?1:n.value.outlet.localeCompare(t.value.outlet))}function A1(e){let n=e.value.routeConfig;return n&&n.path===""}function ay(e){let n=[],t=new Set;for(let r of e){if(!A1(r)){n.push(r);continue}let o=n.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),t.add(o)):n.push(r)}for(let r of t){let o=ay(r.children);n.push(new ht(r.value,o))}return n.filter(r=>!t.has(r))}function P0(e){return e.data||{}}function F0(e){return e.resolve||{}}function N1(e,n,t,r,o,i){return Ee(s=>I1(e,n,t,r,s.extractedUrl,o,i).pipe(B(({state:a,tree:l})=>I(b({},s),{targetSnapshot:a,urlAfterRedirects:l}))))}function R1(e,n){return Ee(t=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=t;if(!o.length)return R(t);let i=new Set(o.map(l=>l.route)),s=new Set;for(let l of i)if(!s.has(l))for(let c of ly(l))s.add(c);let a=0;return de(s).pipe(Xt(l=>i.has(l)?O1(l,r,e,n):(l.data=wl(l,l.parent,e).resolve,R(void 0))),ge(()=>a++),Lr(1),Ee(l=>a===s.size?R(t):Ye))})}function ly(e){let n=e.children.map(t=>ly(t)).flat();return[e,...n]}function O1(e,n,t,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!ey(o)&&(i[Fi]=o.title),P1(i,e,n,r).pipe(B(s=>(e._resolvedData=s,e.data=wl(e,e.parent,t).resolve,null)))}function P1(e,n,t,r){let o=of(e);if(o.length===0)return R({});let i={};return de(o).pipe(Ee(s=>F1(e[s],n,t,r).pipe(tn(),ge(a=>{if(a instanceof Ni)throw _l(new wo,a);i[s]=a}))),Lr(1),B(()=>i),Et(s=>oy(s)?Ye:Pr(s)))}function F1(e,n,t,r){let o=Vi(n)??r,i=xo(e,o),s=i.resolve?i.resolve(n,t):nt(o,()=>i(n,t));return Gn(s)}function nf(e){return Ke(n=>{let t=e(n);return t?de(t).pipe(B(()=>n)):R(n)})}var cy=(()=>{class e{buildTitle(t){let r,o=t.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===U);return r}getResolvedTitleForRoute(t){return t.data[Fi]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:()=>w(L1),providedIn:"root"})}return e})(),L1=(()=>{class e extends cy{title;constructor(t){super(),this.title=t}updateTitle(t){let r=this.buildTitle(t);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(T(M0))};static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),$i=new M("",{providedIn:"root",factory:()=>({})}),V1=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=q({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&D(0,"router-outlet")},dependencies:[Af],encapsulation:2})}return e})();function Nf(e){let n=e.children&&e.children.map(Nf),t=n?I(b({},e),{children:n}):b({},e);return!t.component&&!t.loadComponent&&(n||t.loadChildren)&&t.outlet&&t.outlet!==U&&(t.component=V1),t}var Pi=new M(""),Rf=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=w(Ba);loadComponent(t){if(this.componentLoaders.get(t))return this.componentLoaders.get(t);if(t._loadedComponent)return R(t._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(t);let r=Gn(t.loadComponent()).pipe(B(uy),ge(i=>{this.onLoadEndListener&&this.onLoadEndListener(t),t._loadedComponent=i}),Jn(()=>{this.componentLoaders.delete(t)})),o=new Or(r,()=>new pe).pipe(Rr());return this.componentLoaders.set(t,o),o}loadChildren(t,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return R({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=j1(r,this.compiler,t,this.onLoadEndListener).pipe(Jn(()=>{this.childrenLoaders.delete(r)})),s=new Or(i,()=>new pe).pipe(Rr());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function j1(e,n,t,r){return Gn(e.loadChildren()).pipe(B(uy),Ee(o=>o instanceof wd||Array.isArray(o)?R(o):de(n.compileModuleAsync(o))),B(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(t).injector,s=i.get(Pi,[],{optional:!0,self:!0}).flat()),{routes:s.map(Nf),injector:i}}))}function B1(e){return e&&typeof e=="object"&&"default"in e}function uy(e){return B1(e)?e.default:e}var Of=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:()=>w($1),providedIn:"root"})}return e})(),$1=(()=>{class e{shouldProcessUrl(t){return!0}extract(t){return t}merge(t,r){return t}static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),dy=new M(""),fy=new M("");function U1(e,n,t){let r=e.get(fy),o=e.get(Oe);return e.get(ve).runOutsideAngular(()=>{if(!o.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(c=>setTimeout(c));let i,s=new Promise(c=>{i=c}),a=o.startViewTransition(()=>(i(),H1(e))),{onViewTransitionCreated:l}=r;return l&&nt(e,()=>l({transition:a,from:n,to:t})),s})}function H1(e){return new Promise(n=>{rd({read:()=>setTimeout(n)},{injector:e})})}var hy=new M(""),Pf=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new pe;transitionAbortSubject=new pe;configLoader=w(Rf);environmentInjector=w(He);destroyRef=w(ao);urlSerializer=w(Li);rootContexts=w(ji);location=w(ho);inputBindingEnabled=w(Sl,{optional:!0})!==null;titleStrategy=w(cy);options=w($i,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=w(Of);createViewTransition=w(dy,{optional:!0});navigationErrorHandler=w(hy,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>R(void 0);rootComponentType=null;destroyed=!1;constructor(){let t=o=>this.events.next(new hf(o)),r=o=>this.events.next(new pf(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=t,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(t){let r=++this.navigationId;this.transitions?.next(I(b(b({},this.transitions.value),t),{id:r}))}setupNavigations(t,r,o){return this.transitions=new De({id:0,currentUrlTree:r,currentRawUrl:r,extractedUrl:this.urlHandlingStrategy.extract(r),urlAfterRedirects:this.urlHandlingStrategy.extract(r),rawUrl:r,extras:{},resolve:()=>{},reject:()=>{},promise:Promise.resolve(!0),source:Si,restoredState:null,currentSnapshot:o.snapshot,targetSnapshot:null,currentRouterState:o,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe(Je(i=>i.id!==0),B(i=>I(b({},i),{extractedUrl:this.urlHandlingStrategy.extract(i.rawUrl)})),Ke(i=>{let s=!1,a=!1;return R(i).pipe(Ke(l=>{if(this.navigationId>i.id)return this.cancelNavigationTransition(i,"",pt.SupersededByNewNavigation),Ye;this.currentTransition=i,this.currentNavigation={id:l.id,initialUrl:l.rawUrl,extractedUrl:l.extractedUrl,targetBrowserUrl:typeof l.extras.browserUrl=="string"?this.urlSerializer.parse(l.extras.browserUrl):l.extras.browserUrl,trigger:l.source,extras:l.extras,previousNavigation:this.lastSuccessfulNavigation?I(b({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let c=!t.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),u=l.extras.onSameUrlNavigation??t.onSameUrlNavigation;if(!c&&u!=="reload"){let h="";return this.events.next(new Hn(l.id,this.urlSerializer.serialize(l.rawUrl),h,ml.IgnoredSameUrlNavigation)),l.resolve(!1),Ye}if(this.urlHandlingStrategy.shouldProcessUrl(l.rawUrl))return R(l).pipe(Ke(h=>{let m=this.transitions?.getValue();return this.events.next(new Do(h.id,this.urlSerializer.serialize(h.extractedUrl),h.source,h.restoredState)),m!==this.transitions?.getValue()?Ye:Promise.resolve(h)}),N1(this.environmentInjector,this.configLoader,this.rootComponentType,t.config,this.urlSerializer,this.paramsInheritanceStrategy),ge(h=>{i.targetSnapshot=h.targetSnapshot,i.urlAfterRedirects=h.urlAfterRedirects,this.currentNavigation=I(b({},this.currentNavigation),{finalUrl:h.urlAfterRedirects});let m=new vl(h.id,this.urlSerializer.serialize(h.extractedUrl),this.urlSerializer.serialize(h.urlAfterRedirects),h.targetSnapshot);this.events.next(m)}));if(c&&this.urlHandlingStrategy.shouldProcessUrl(l.currentRawUrl)){let{id:h,extractedUrl:m,source:g,restoredState:v,extras:y}=l,_=new Do(h,this.urlSerializer.serialize(m),g,v);this.events.next(_);let A=K0(this.rootComponentType).snapshot;return this.currentTransition=i=I(b({},l),{targetSnapshot:A,urlAfterRedirects:m,extras:I(b({},y),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=m,R(i)}else{let h="";return this.events.next(new Hn(l.id,this.urlSerializer.serialize(l.extractedUrl),h,ml.IgnoredByUrlHandlingStrategy)),l.resolve(!1),Ye}}),ge(l=>{let c=new cf(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(c)}),B(l=>(this.currentTransition=i=I(b({},l),{guards:KM(l.targetSnapshot,l.currentSnapshot,this.rootContexts)}),i)),c1(this.environmentInjector,l=>this.events.next(l)),ge(l=>{if(i.guardsResult=l.guardsResult,l.guardsResult&&typeof l.guardsResult!="boolean")throw _l(this.urlSerializer,l.guardsResult);let c=new uf(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot,!!l.guardsResult);this.events.next(c)}),Je(l=>l.guardsResult?!0:(this.cancelNavigationTransition(l,"",pt.GuardRejected),!1)),nf(l=>{if(l.guards.canActivateChecks.length)return R(l).pipe(ge(c=>{let u=new df(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(u)}),Ke(c=>{let u=!1;return R(c).pipe(R1(this.paramsInheritanceStrategy,this.environmentInjector),ge({next:()=>u=!0,complete:()=>{u||this.cancelNavigationTransition(c,"",pt.NoDataFromResolver)}}))}),ge(c=>{let u=new ff(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(u)}))}),nf(l=>{let c=u=>{let h=[];u.routeConfig?.loadComponent&&!u.routeConfig._loadedComponent&&h.push(this.configLoader.loadComponent(u.routeConfig).pipe(ge(m=>{u.component=m}),B(()=>{})));for(let m of u.children)h.push(...c(m));return h};return Go(c(l.targetSnapshot.root)).pipe(An(null),en(1))}),nf(()=>this.afterPreactivation()),Ke(()=>{let{currentSnapshot:l,targetSnapshot:c}=i,u=this.createViewTransition?.(this.environmentInjector,l.root,c.root);return u?de(u).pipe(B(()=>i)):R(i)}),B(l=>{let c=WM(t.routeReuseStrategy,l.targetSnapshot,l.currentRouterState);return this.currentTransition=i=I(b({},l),{targetRouterState:c}),this.currentNavigation.targetRouterState=c,i}),ge(()=>{this.events.next(new ki)}),JM(this.rootContexts,t.routeReuseStrategy,l=>this.events.next(l),this.inputBindingEnabled),en(1),ge({next:l=>{s=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Ot(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects))),this.titleStrategy?.updateTitle(l.targetRouterState.snapshot),l.resolve(!0)},complete:()=>{s=!0}}),hc(this.transitionAbortSubject.pipe(ge(l=>{throw l}))),Jn(()=>{!s&&!a&&this.cancelNavigationTransition(i,"",pt.SupersededByNewNavigation),this.currentTransition?.id===i.id&&(this.currentNavigation=null,this.currentTransition=null)}),Et(l=>{if(this.destroyed)return i.resolve(!1),Ye;if(a=!0,ry(l))this.events.next(new Sn(i.id,this.urlSerializer.serialize(i.extractedUrl),l.message,l.cancellationCode)),QM(l)?this.events.next(new _o(l.url,l.navigationBehaviorOptions)):i.resolve(!1);else{let c=new Ti(i.id,this.urlSerializer.serialize(i.extractedUrl),l,i.targetSnapshot??void 0);try{let u=nt(this.environmentInjector,()=>this.navigationErrorHandler?.(c));if(u instanceof Ni){let{message:h,cancellationCode:m}=_l(this.urlSerializer,u);this.events.next(new Sn(i.id,this.urlSerializer.serialize(i.extractedUrl),h,m)),this.events.next(new _o(u.redirectTo,u.navigationBehaviorOptions))}else throw this.events.next(c),l}catch(u){this.options.resolveNavigationPromiseOnError?i.resolve(!1):i.reject(u)}}return Ye}))}))}cancelNavigationTransition(t,r,o){let i=new Sn(t.id,this.urlSerializer.serialize(t.extractedUrl),r,o);this.events.next(i),t.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let t=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return t.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function z1(e){return e!==Si}var G1=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:()=>w(q1),providedIn:"root"})}return e})(),Tf=class{shouldDetach(n){return!1}store(n,t){}shouldAttach(n){return!1}retrieve(n){return null}shouldReuseRoute(n,t){return n.routeConfig===t.routeConfig}},q1=(()=>{class e extends Tf{static \u0275fac=(()=>{let t;return function(o){return(t||(t=Pn(e)))(o||e)}})();static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),py=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:()=>w(W1),providedIn:"root"})}return e})(),W1=(()=>{class e extends py{location=w(ho);urlSerializer=w(Li);options=w($i,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";urlHandlingStrategy=w(Of);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new Mn;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}routerState=K0(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}registerNonRouterCurrentEntryChangeListener(t){return this.location.subscribe(r=>{r.type==="popstate"&&t(r.url,r.state)})}handleRouterEvent(t,r){if(t instanceof Do)this.stateMemento=this.createStateMemento();else if(t instanceof Hn)this.rawUrlTree=r.initialUrl;else if(t instanceof vl){if(this.urlUpdateStrategy==="eager"&&!r.extras.skipLocationChange){let o=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl);this.setBrowserUrl(r.targetBrowserUrl??o,r)}}else t instanceof ki?(this.currentUrlTree=r.finalUrl,this.rawUrlTree=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl),this.routerState=r.targetRouterState,this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(r.targetBrowserUrl??this.rawUrlTree,r)):t instanceof Sn&&(t.code===pt.GuardRejected||t.code===pt.NoDataFromResolver)?this.restoreHistory(r):t instanceof Ti?this.restoreHistory(r,!0):t instanceof Ot&&(this.lastSuccessfulId=t.id,this.currentPageId=this.browserPageId)}setBrowserUrl(t,r){let o=t instanceof Mn?this.urlSerializer.serialize(t):t;if(this.location.isCurrentPathEqualTo(o)||r.extras.replaceUrl){let i=this.browserPageId,s=b(b({},r.extras.state),this.generateNgRouterState(r.id,i));this.location.replaceState(o,"",s)}else{let i=b(b({},r.extras.state),this.generateNgRouterState(r.id,this.browserPageId+1));this.location.go(o,"",i)}}restoreHistory(t,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.currentUrlTree===t.finalUrl&&i===0&&(this.resetState(t),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetState(t),this.resetUrlToCurrentUrlTree())}resetState(t){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,t.finalUrl??this.rawUrlTree)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(t,r){return this.canceledNavigationResolution==="computed"?{navigationId:t,\u0275routerPageId:r}:{navigationId:t}}static \u0275fac=(()=>{let t;return function(o){return(t||(t=Pn(e)))(o||e)}})();static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function gy(e,n){e.events.pipe(Je(t=>t instanceof Ot||t instanceof Sn||t instanceof Ti||t instanceof Hn),B(t=>t instanceof Ot||t instanceof Hn?0:(t instanceof Sn?t.code===pt.Redirect||t.code===pt.SupersededByNewNavigation:!1)?2:1),Je(t=>t!==2),en(1)).subscribe(()=>{n()})}var Z1={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},Y1={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},Ae=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=w(_d);stateManager=w(py);options=w($i,{optional:!0})||{};pendingTasks=w(Fn);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=w(Pf);urlSerializer=w(Li);location=w(ho);urlHandlingStrategy=w(Of);_events=new pe;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=w(G1);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=w(Pi,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!w(Sl,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe({error:t=>{this.console.warn(t)}}),this.subscribeToNavigationEvents()}eventsSubscription=new ie;subscribeToNavigationEvents(){let t=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof Sn&&r.code!==pt.Redirect&&r.code!==pt.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof Ot)this.navigated=!0;else if(r instanceof _o){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),l=b({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||z1(o.source)},s);this.scheduleNavigation(a,Si,null,l,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}J1(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortSubject.next(o)}});this.eventsSubscription.add(t)}resetRootComponentType(t){this.routerState.root.component=t,this.navigationTransitions.rootComponentType=t}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Si,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((t,r)=>{setTimeout(()=>{this.navigateToSyncWithBrowser(t,"popstate",r)},0)})}navigateToSyncWithBrowser(t,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let l=b({},o);delete l.navigationId,delete l.\u0275routerPageId,Object.keys(l).length!==0&&(i.state=l)}let a=this.parseUrl(t);this.scheduleNavigation(a,r,s,i)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(t){this.config=t.map(Nf),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(t,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:l}=r,c=l?this.currentUrlTree.fragment:s,u=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":u=b(b({},this.currentUrlTree.queryParams),i);break;case"preserve":u=this.currentUrlTree.queryParams;break;default:u=i||null}u!==null&&(u=this.removeEmptyProps(u));let h;try{let m=o?o.snapshot:this.routerState.snapshot.root;h=Z0(m)}catch{(typeof t[0]!="string"||t[0][0]!=="/")&&(t=[]),h=this.currentUrlTree.root}return Y0(h,t,u,c??null)}navigateByUrl(t,r={skipLocationChange:!1}){let o=Cr(t)?t:this.parseUrl(t),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,Si,null,r)}navigate(t,r={skipLocationChange:!1}){return Q1(t),this.navigateByUrl(this.createUrlTree(t,r),r)}serializeUrl(t){return this.urlSerializer.serialize(t)}parseUrl(t){try{return this.urlSerializer.parse(t)}catch{return this.urlSerializer.parse("/")}}isActive(t,r){let o;if(r===!0?o=b({},Z1):r===!1?o=b({},Y1):o=r,Cr(t))return T0(this.currentUrlTree,t,o);let i=this.parseUrl(t);return T0(this.currentUrlTree,i,o)}removeEmptyProps(t){return Object.entries(t).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(t,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,l,c;s?(a=s.resolve,l=s.reject,c=s.promise):c=new Promise((h,m)=>{a=h,l=m});let u=this.pendingTasks.add();return gy(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(u))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:t,extras:i,resolve:a,reject:l,promise:c,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),c.catch(h=>Promise.reject(h))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Q1(e){for(let n=0;n<e.length;n++)if(e[n]==null)throw new x(4008,!1)}function J1(e){return!(e instanceof ki)&&!(e instanceof _o)}var je=(()=>{class e{router;route;tabIndexAttribute;renderer;el;locationStrategy;href=null;target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new pe;constructor(t,r,o,i,s,a){this.router=t,this.route=r,this.tabIndexAttribute=o,this.renderer=i,this.el=s,this.locationStrategy=a;let l=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=l==="a"||l==="area",this.isAnchorElement?this.subscription=t.events.subscribe(c=>{c instanceof Ot&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(t){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",t)}ngOnChanges(t){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}routerLinkInput=null;set routerLink(t){t==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(Cr(t)?this.routerLinkInput=t:this.routerLinkInput=Array.isArray(t)?t:[t],this.setTabIndexIfNotOnNativeEl("0"))}onClick(t,r,o,i,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(t!==0||r||o||i||s||typeof this.target=="string"&&this.target!="_self"))return!0;let l={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,l),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let t=this.urlTree;this.href=t!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(t)):null;let r=this.href===null?null:Cm(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",r)}applyAttributeValue(t,r){let o=this.renderer,i=this.el.nativeElement;r!==null?o.setAttribute(i,t,r):o.removeAttribute(i,t)}get urlTree(){return this.routerLinkInput===null?null:Cr(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||e)(E(Ae),E(zn),Yu("tabindex"),E(mn),E(ct),E(xn))};static \u0275dir=Se({type:e,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(r,o){r&1&&V("click",function(s){return o.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&Gt("target",o.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",Vn],skipLocationChange:[2,"skipLocationChange","skipLocationChange",Vn],replaceUrl:[2,"replaceUrl","replaceUrl",Vn],routerLink:"routerLink"},features:[Ht]})}return e})(),my=(()=>{class e{router;element;renderer;cdr;link;links;classes=[];routerEventsSubscription;linkInputChangesSubscription;_isActive=!1;get isActive(){return this._isActive}routerLinkActiveOptions={exact:!1};ariaCurrentWhenActive;isActiveChange=new me;constructor(t,r,o,i,s){this.router=t,this.element=r,this.renderer=o,this.cdr=i,this.link=s,this.routerEventsSubscription=t.events.subscribe(a=>{a instanceof Ot&&this.update()})}ngAfterContentInit(){R(this.links.changes,R(null)).pipe(kn()).subscribe(t=>{this.update(),this.subscribeToEachLinkOnChanges()})}subscribeToEachLinkOnChanges(){this.linkInputChangesSubscription?.unsubscribe();let t=[...this.links.toArray(),this.link].filter(r=>!!r).map(r=>r.onChanges);this.linkInputChangesSubscription=de(t).pipe(kn()).subscribe(r=>{this._isActive!==this.isLinkActive(this.router)(r)&&this.update()})}set routerLinkActive(t){let r=Array.isArray(t)?t:t.split(" ");this.classes=r.filter(o=>!!o)}ngOnChanges(t){this.update()}ngOnDestroy(){this.routerEventsSubscription.unsubscribe(),this.linkInputChangesSubscription?.unsubscribe()}update(){!this.links||!this.router.navigated||queueMicrotask(()=>{let t=this.hasActiveLinks();this.classes.forEach(r=>{t?this.renderer.addClass(this.element.nativeElement,r):this.renderer.removeClass(this.element.nativeElement,r)}),t&&this.ariaCurrentWhenActive!==void 0?this.renderer.setAttribute(this.element.nativeElement,"aria-current",this.ariaCurrentWhenActive.toString()):this.renderer.removeAttribute(this.element.nativeElement,"aria-current"),this._isActive!==t&&(this._isActive=t,this.cdr.markForCheck(),this.isActiveChange.emit(t))})}isLinkActive(t){let r=K1(this.routerLinkActiveOptions)?this.routerLinkActiveOptions:this.routerLinkActiveOptions.exact||!1;return o=>{let i=o.urlTree;return i?t.isActive(i,r):!1}}hasActiveLinks(){let t=this.isLinkActive(this.router);return this.link&&t(this.link)||this.links.some(t)}static \u0275fac=function(r){return new(r||e)(E(Ae),E(ct),E(mn),E(ye),E(je,8))};static \u0275dir=Se({type:e,selectors:[["","routerLinkActive",""]],contentQueries:function(r,o,i){if(r&1&&Mv(i,je,5),r&2){let s;Fa(s=La())&&(o.links=s)}},inputs:{routerLinkActiveOptions:"routerLinkActiveOptions",ariaCurrentWhenActive:"ariaCurrentWhenActive",routerLinkActive:"routerLinkActive"},outputs:{isActiveChange:"isActiveChange"},exportAs:["routerLinkActive"],features:[Ht]})}return e})();function K1(e){return!!e.paths}var xl=class{};var X1=(()=>{class e{router;injector;preloadingStrategy;loader;subscription;constructor(t,r,o,i,s){this.router=t,this.injector=o,this.preloadingStrategy=i,this.loader=s}setUpPreloading(){this.subscription=this.router.events.pipe(Je(t=>t instanceof Ot),Xt(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(t,r){let o=[];for(let i of r){i.providers&&!i._injector&&(i._injector=Ra(i.providers,t,`Route: ${i.path}`));let s=i._injector??t,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&i.canLoad===void 0||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return de(o).pipe(kn())}preloadConfig(t,r){return this.preloadingStrategy.preload(r,()=>{let o;r.loadChildren&&r.canLoad===void 0?o=this.loader.loadChildren(t,r):o=R(null);let i=o.pipe(Ee(s=>s===null?R(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??t,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return de([i,s]).pipe(kn())}else return i})}static \u0275fac=function(r){return new(r||e)(T(Ae),T(Ba),T(He),T(xl),T(Rf))};static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),vy=new M(""),eI=(()=>{class e{urlSerializer;transitions;viewportScroller;zone;options;routerEventsSubscription;scrollEventsSubscription;lastId=0;lastSource="imperative";restoredId=0;store={};constructor(t,r,o,i,s={}){this.urlSerializer=t,this.transitions=r,this.viewportScroller=o,this.zone=i,this.options=s,s.scrollPositionRestoration||="disabled",s.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(t=>{t instanceof Do?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=t.navigationTrigger,this.restoredId=t.restoredState?t.restoredState.navigationId:0):t instanceof Ot?(this.lastId=t.id,this.scheduleScrollEvent(t,this.urlSerializer.parse(t.urlAfterRedirects).fragment)):t instanceof Hn&&t.code===ml.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(t,this.urlSerializer.parse(t.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(t=>{t instanceof yl&&(t.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(t.position):t.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(t.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(t,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new yl(t,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static \u0275fac=function(r){Xm()};static \u0275prov=S({token:e,factory:e.\u0275fac})}return e})();function yy(e,...n){return no([{provide:Pi,multi:!0,useValue:e},[],{provide:zn,useFactory:by,deps:[Ae]},{provide:hi,multi:!0,useFactory:Cy},n.map(t=>t.\u0275providers)])}function by(e){return e.routerState.root}function Ui(e,n){return{\u0275kind:e,\u0275providers:n}}function Cy(){let e=w(ze);return n=>{let t=e.get(cn);if(n!==t.components[0])return;let r=e.get(Ae),o=e.get(wy);e.get(Ff)===1&&r.initialNavigation(),e.get(Dy,null,z.Optional)?.setUpPreloading(),e.get(vy,null,z.Optional)?.init(),r.resetRootComponentType(t.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var wy=new M("",{factory:()=>new pe}),Ff=new M("",{providedIn:"root",factory:()=>1});function tI(){return Ui(2,[{provide:Ff,useValue:0},{provide:Oa,multi:!0,deps:[ze],useFactory:n=>{let t=n.get(Gv,Promise.resolve());return()=>t.then(()=>new Promise(r=>{let o=n.get(Ae),i=n.get(wy);gy(o,()=>{r(!0)}),n.get(Pf).afterPreactivation=()=>(r(!0),i.closed?R(void 0):i),o.initialNavigation()}))}}])}function nI(){return Ui(3,[{provide:Oa,multi:!0,useFactory:()=>{let n=w(Ae);return()=>{n.setUpLocationChangeListener()}}},{provide:Ff,useValue:2}])}var Dy=new M("");function rI(e){return Ui(0,[{provide:Dy,useExisting:X1},{provide:xl,useExisting:e}])}function oI(){return Ui(8,[R0,{provide:Sl,useExisting:R0}])}function iI(e){let n=[{provide:dy,useValue:U1},{provide:fy,useValue:b({skipNextTransition:!!e?.skipInitialTransition},e)}];return Ui(9,n)}var sI=[ho,{provide:Li,useClass:wo},Ae,ji,{provide:zn,useFactory:by,deps:[Ae]},Rf,[]],Be=(()=>{class e{constructor(){}static forRoot(t,r){return{ngModule:e,providers:[sI,[],{provide:Pi,multi:!0,useValue:t},[],r?.errorHandler?{provide:hy,useValue:r.errorHandler}:[],{provide:$i,useValue:r||{}},r?.useHash?lI():cI(),aI(),r?.preloadingStrategy?rI(r.preloadingStrategy).\u0275providers:[],r?.initialNavigation?uI(r):[],r?.bindToComponentInputs?oI().\u0275providers:[],r?.enableViewTransitions?iI().\u0275providers:[],dI()]}}static forChild(t){return{ngModule:e,providers:[{provide:Pi,multi:!0,useValue:t}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=vn({type:e});static \u0275inj=dn({})}return e})();function aI(){return{provide:vy,useFactory:()=>{let e=w(Xv),n=w(ve),t=w($i),r=w(Pf),o=w(Li);return t.scrollOffset&&e.setOffset(t.scrollOffset),new eI(o,r,e,n,t)}}}function lI(){return{provide:xn,useClass:Wv}}function cI(){return{provide:xn,useClass:Pd}}function uI(e){return[e.initialNavigation==="disabled"?nI().\u0275providers:[],e.initialNavigation==="enabledBlocking"?tI().\u0275providers:[]]}var L0=new M("");function dI(){return[{provide:L0,useFactory:Cy},{provide:hi,multi:!0,useExisting:L0}]}var ky=(()=>{class e{_renderer;_elementRef;onChange=t=>{};onTouched=()=>{};constructor(t,r){this._renderer=t,this._elementRef=r}setProperty(t,r){this._renderer.setProperty(this._elementRef.nativeElement,t,r)}registerOnTouched(t){this.onTouched=t}registerOnChange(t){this.onChange=t}setDisabledState(t){this.setProperty("disabled",t)}static \u0275fac=function(r){return new(r||e)(E(mn),E(ct))};static \u0275dir=Se({type:e})}return e})(),fI=(()=>{class e extends ky{static \u0275fac=(()=>{let t;return function(o){return(t||(t=Pn(e)))(o||e)}})();static \u0275dir=Se({type:e,features:[At]})}return e})(),Ay=new M("");var hI={provide:Ay,useExisting:un(()=>qn),multi:!0};function pI(){let e=En()?En().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}var gI=new M(""),qn=(()=>{class e extends ky{_compositionMode;_composing=!1;constructor(t,r,o){super(t,r),this._compositionMode=o,this._compositionMode==null&&(this._compositionMode=!pI())}writeValue(t){let r=t??"";this.setProperty("value",r)}_handleInput(t){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(t)}_compositionStart(){this._composing=!0}_compositionEnd(t){this._composing=!1,this._compositionMode&&this.onChange(t)}static \u0275fac=function(r){return new(r||e)(E(mn),E(ct),E(gI,8))};static \u0275dir=Se({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(r,o){r&1&&V("input",function(s){return o._handleInput(s.target.value)})("blur",function(){return o.onTouched()})("compositionstart",function(){return o._compositionStart()})("compositionend",function(s){return o._compositionEnd(s.target.value)})},standalone:!1,features:[mr([hI]),At]})}return e})();function Ny(e){return e==null||Ry(e)===0}function Ry(e){return e==null?null:Array.isArray(e)||typeof e=="string"?e.length:e instanceof Set?e.size:null}var Yi=new M(""),Oy=new M("");function mI(e){return Ny(e.value)?{required:!0}:null}function vI(e){return n=>{let t=n.value?.length??Ry(n.value);return t===null||t===0?null:t<e?{minlength:{requiredLength:e,actualLength:t}}:null}}function yI(e){if(!e)return Lf;let n,t;return typeof e=="string"?(t="",e.charAt(0)!=="^"&&(t+="^"),t+=e,e.charAt(e.length-1)!=="$"&&(t+="$"),n=new RegExp(t)):(t=e.toString(),n=e),r=>{if(Ny(r.value))return null;let o=r.value;return n.test(o)?null:{pattern:{requiredPattern:t,actualValue:o}}}}function Lf(e){return null}function Py(e){return e!=null}function Fy(e){return gr(e)?de(e):e}function Ly(e){let n={};return e.forEach(t=>{n=t!=null?b(b({},n),t):n}),Object.keys(n).length===0?null:n}function Vy(e,n){return n.map(t=>t(e))}function bI(e){return!e.validate}function jy(e){return e.map(n=>bI(n)?n:t=>n.validate(t))}function CI(e){if(!e)return null;let n=e.filter(Py);return n.length==0?null:function(t){return Ly(Vy(t,n))}}function jf(e){return e!=null?CI(jy(e)):null}function wI(e){if(!e)return null;let n=e.filter(Py);return n.length==0?null:function(t){let r=Vy(t,n).map(Fy);return cc(r).pipe(B(Ly))}}function Bf(e){return e!=null?wI(jy(e)):null}function _y(e,n){return e===null?[n]:Array.isArray(e)?[...e,n]:[e,n]}function DI(e){return e._rawValidators}function _I(e){return e._rawAsyncValidators}function Vf(e){return e?Array.isArray(e)?e:[e]:[]}function Tl(e,n){return Array.isArray(e)?e.includes(n):e===n}function Ey(e,n){let t=Vf(n);return Vf(e).forEach(o=>{Tl(t,o)||t.push(o)}),t}function xy(e,n){return Vf(n).filter(t=>!Tl(e,t))}var kl=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(n){this._rawValidators=n||[],this._composedValidatorFn=jf(this._rawValidators)}_setAsyncValidators(n){this._rawAsyncValidators=n||[],this._composedAsyncValidatorFn=Bf(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(n){this._onDestroyCallbacks.push(n)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(n=>n()),this._onDestroyCallbacks=[]}reset(n=void 0){this.control&&this.control.reset(n)}hasError(n,t){return this.control?this.control.hasError(n,t):!1}getError(n,t){return this.control?this.control.getError(n,t):null}},Io=class extends kl{name;get formDirective(){return null}get path(){return null}},Zi=class extends kl{_parent=null;name=null;valueAccessor=null},Al=class{_cd;constructor(n){this._cd=n}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},EI={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},PL=I(b({},EI),{"[class.ng-submitted]":"isSubmitted"}),ko=(()=>{class e extends Al{constructor(t){super(t)}static \u0275fac=function(r){return new(r||e)(E(Zi,2))};static \u0275dir=Se({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(r,o){r&2&&dt("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)},standalone:!1,features:[At]})}return e})(),Pl=(()=>{class e extends Al{constructor(t){super(t)}static \u0275fac=function(r){return new(r||e)(E(Io,10))};static \u0275dir=Se({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(r,o){r&2&&dt("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)("ng-submitted",o.isSubmitted)},standalone:!1,features:[At]})}return e})();var Hi="VALID",Il="INVALID",So="PENDING",zi="DISABLED",To=class{},Nl=class extends To{value;source;constructor(n,t){super(),this.value=n,this.source=t}},qi=class extends To{pristine;source;constructor(n,t){super(),this.pristine=n,this.source=t}},Wi=class extends To{touched;source;constructor(n,t){super(),this.touched=n,this.source=t}},Mo=class extends To{status;source;constructor(n,t){super(),this.status=n,this.source=t}};function By(e){return(Fl(e)?e.validators:e)||null}function xI(e){return Array.isArray(e)?jf(e):e||null}function $y(e,n){return(Fl(n)?n.asyncValidators:e)||null}function SI(e){return Array.isArray(e)?Bf(e):e||null}function Fl(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}function MI(e,n,t){let r=e.controls;if(!(n?Object.keys(r):r).length)throw new x(1e3,"");if(!r[t])throw new x(1001,"")}function II(e,n,t){e._forEachChild((r,o)=>{if(t[o]===void 0)throw new x(1002,"")})}var Rl=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(n,t){this._assignValidators(n),this._assignAsyncValidators(t)}get validator(){return this._composedValidatorFn}set validator(n){this._rawValidators=this._composedValidatorFn=n}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(n){this._rawAsyncValidators=this._composedAsyncValidatorFn=n}get parent(){return this._parent}get status(){return Cn(this.statusReactive)}set status(n){Cn(()=>this.statusReactive.set(n))}_status=qe(()=>this.statusReactive());statusReactive=ut(void 0);get valid(){return this.status===Hi}get invalid(){return this.status===Il}get pending(){return this.status==So}get disabled(){return this.status===zi}get enabled(){return this.status!==zi}errors;get pristine(){return Cn(this.pristineReactive)}set pristine(n){Cn(()=>this.pristineReactive.set(n))}_pristine=qe(()=>this.pristineReactive());pristineReactive=ut(!0);get dirty(){return!this.pristine}get touched(){return Cn(this.touchedReactive)}set touched(n){Cn(()=>this.touchedReactive.set(n))}_touched=qe(()=>this.touchedReactive());touchedReactive=ut(!1);get untouched(){return!this.touched}_events=new pe;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(n){this._assignValidators(n)}setAsyncValidators(n){this._assignAsyncValidators(n)}addValidators(n){this.setValidators(Ey(n,this._rawValidators))}addAsyncValidators(n){this.setAsyncValidators(Ey(n,this._rawAsyncValidators))}removeValidators(n){this.setValidators(xy(n,this._rawValidators))}removeAsyncValidators(n){this.setAsyncValidators(xy(n,this._rawAsyncValidators))}hasValidator(n){return Tl(this._rawValidators,n)}hasAsyncValidator(n){return Tl(this._rawAsyncValidators,n)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(n={}){let t=this.touched===!1;this.touched=!0;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsTouched(I(b({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new Wi(!0,r))}markAllAsTouched(n={}){this.markAsTouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:this}),this._forEachChild(t=>t.markAllAsTouched(n))}markAsUntouched(n={}){let t=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let r=n.sourceControl??this;this._forEachChild(o=>{o.markAsUntouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:r})}),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,r),t&&n.emitEvent!==!1&&this._events.next(new Wi(!1,r))}markAsDirty(n={}){let t=this.pristine===!0;this.pristine=!1;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsDirty(I(b({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new qi(!1,r))}markAsPristine(n={}){let t=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let r=n.sourceControl??this;this._forEachChild(o=>{o.markAsPristine({onlySelf:!0,emitEvent:n.emitEvent})}),this._parent&&!n.onlySelf&&this._parent._updatePristine(n,r),t&&n.emitEvent!==!1&&this._events.next(new qi(!0,r))}markAsPending(n={}){this.status=So;let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new Mo(this.status,t)),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.markAsPending(I(b({},n),{sourceControl:t}))}disable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=zi,this.errors=null,this._forEachChild(o=>{o.disable(I(b({},n),{onlySelf:!0}))}),this._updateValue();let r=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new Nl(this.value,r)),this._events.next(new Mo(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(I(b({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(o=>o(!0))}enable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=Hi,this._forEachChild(r=>{r.enable(I(b({},n),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent}),this._updateAncestors(I(b({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(n,t){this._parent&&!n.onlySelf&&(this._parent.updateValueAndValidity(n),n.skipPristineCheck||this._parent._updatePristine({},t),this._parent._updateTouched({},t))}setParent(n){this._parent=n}getRawValue(){return this.value}updateValueAndValidity(n={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===Hi||this.status===So)&&this._runAsyncValidator(r,n.emitEvent)}let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new Nl(this.value,t)),this._events.next(new Mo(this.status,t)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.updateValueAndValidity(I(b({},n),{sourceControl:t}))}_updateTreeValidity(n={emitEvent:!0}){this._forEachChild(t=>t._updateTreeValidity(n)),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?zi:Hi}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(n,t){if(this.asyncValidator){this.status=So,this._hasOwnPendingAsyncValidator={emitEvent:t!==!1};let r=Fy(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(o=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(o,{emitEvent:t,shouldHaveEmitted:n})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let n=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,n}return!1}setErrors(n,t={}){this.errors=n,this._updateControlsErrors(t.emitEvent!==!1,this,t.shouldHaveEmitted)}get(n){let t=n;return t==null||(Array.isArray(t)||(t=t.split(".")),t.length===0)?null:t.reduce((r,o)=>r&&r._find(o),this)}getError(n,t){let r=t?this.get(t):this;return r&&r.errors?r.errors[n]:null}hasError(n,t){return!!this.getError(n,t)}get root(){let n=this;for(;n._parent;)n=n._parent;return n}_updateControlsErrors(n,t,r){this.status=this._calculateStatus(),n&&this.statusChanges.emit(this.status),(n||r)&&this._events.next(new Mo(this.status,t)),this._parent&&this._parent._updateControlsErrors(n,t,r)}_initObservables(){this.valueChanges=new me,this.statusChanges=new me}_calculateStatus(){return this._allControlsDisabled()?zi:this.errors?Il:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(So)?So:this._anyControlsHaveStatus(Il)?Il:Hi}_anyControlsHaveStatus(n){return this._anyControls(t=>t.status===n)}_anyControlsDirty(){return this._anyControls(n=>n.dirty)}_anyControlsTouched(){return this._anyControls(n=>n.touched)}_updatePristine(n,t){let r=!this._anyControlsDirty(),o=this.pristine!==r;this.pristine=r,this._parent&&!n.onlySelf&&this._parent._updatePristine(n,t),o&&this._events.next(new qi(this.pristine,t))}_updateTouched(n={},t){this.touched=this._anyControlsTouched(),this._events.next(new Wi(this.touched,t)),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,t)}_onDisabledChange=[];_registerOnCollectionChange(n){this._onCollectionChange=n}_setUpdateStrategy(n){Fl(n)&&n.updateOn!=null&&(this._updateOn=n.updateOn)}_parentMarkedDirty(n){let t=this._parent&&this._parent.dirty;return!n&&!!t&&!this._parent._anyControlsDirty()}_find(n){return null}_assignValidators(n){this._rawValidators=Array.isArray(n)?n.slice():n,this._composedValidatorFn=xI(this._rawValidators)}_assignAsyncValidators(n){this._rawAsyncValidators=Array.isArray(n)?n.slice():n,this._composedAsyncValidatorFn=SI(this._rawAsyncValidators)}},Ol=class extends Rl{constructor(n,t,r){super(By(t),$y(r,t)),this.controls=n,this._initObservables(),this._setUpdateStrategy(t),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;registerControl(n,t){return this.controls[n]?this.controls[n]:(this.controls[n]=t,t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange),t)}addControl(n,t,r={}){this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}removeControl(n,t={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}setControl(n,t,r={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],t&&this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}contains(n){return this.controls.hasOwnProperty(n)&&this.controls[n].enabled}setValue(n,t={}){II(this,!0,n),Object.keys(n).forEach(r=>{MI(this,!0,r),this.controls[r].setValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t)}patchValue(n,t={}){n!=null&&(Object.keys(n).forEach(r=>{let o=this.controls[r];o&&o.patchValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t))}reset(n={},t={}){this._forEachChild((r,o)=>{r.reset(n?n[o]:null,{onlySelf:!0,emitEvent:t.emitEvent})}),this._updatePristine(t,this),this._updateTouched(t,this),this.updateValueAndValidity(t)}getRawValue(){return this._reduceChildren({},(n,t,r)=>(n[r]=t.getRawValue(),n))}_syncPendingControls(){let n=this._reduceChildren(!1,(t,r)=>r._syncPendingControls()?!0:t);return n&&this.updateValueAndValidity({onlySelf:!0}),n}_forEachChild(n){Object.keys(this.controls).forEach(t=>{let r=this.controls[t];r&&n(r,t)})}_setUpControls(){this._forEachChild(n=>{n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(n){for(let[t,r]of Object.entries(this.controls))if(this.contains(t)&&n(r))return!0;return!1}_reduceValue(){let n={};return this._reduceChildren(n,(t,r,o)=>((r.enabled||this.disabled)&&(t[o]=r.value),t))}_reduceChildren(n,t){let r=n;return this._forEachChild((o,i)=>{r=t(r,o,i)}),r}_allControlsDisabled(){for(let n of Object.keys(this.controls))if(this.controls[n].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(n){return this.controls.hasOwnProperty(n)?this.controls[n]:null}};var $f=new M("",{providedIn:"root",factory:()=>Uf}),Uf="always";function TI(e,n){return[...n.path,e]}function Uy(e,n,t=Uf){Hy(e,n),n.valueAccessor.writeValue(e.value),(e.disabled||t==="always")&&n.valueAccessor.setDisabledState?.(e.disabled),AI(e,n),RI(e,n),NI(e,n),kI(e,n)}function Sy(e,n){e.forEach(t=>{t.registerOnValidatorChange&&t.registerOnValidatorChange(n)})}function kI(e,n){if(n.valueAccessor.setDisabledState){let t=r=>{n.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(t),n._registerOnDestroy(()=>{e._unregisterOnDisabledChange(t)})}}function Hy(e,n){let t=DI(e);n.validator!==null?e.setValidators(_y(t,n.validator)):typeof t=="function"&&e.setValidators([t]);let r=_I(e);n.asyncValidator!==null?e.setAsyncValidators(_y(r,n.asyncValidator)):typeof r=="function"&&e.setAsyncValidators([r]);let o=()=>e.updateValueAndValidity();Sy(n._rawValidators,o),Sy(n._rawAsyncValidators,o)}function AI(e,n){n.valueAccessor.registerOnChange(t=>{e._pendingValue=t,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&zy(e,n)})}function NI(e,n){n.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&zy(e,n),e.updateOn!=="submit"&&e.markAsTouched()})}function zy(e,n){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),n.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function RI(e,n){let t=(r,o)=>{n.valueAccessor.writeValue(r),o&&n.viewToModelUpdate(r)};e.registerOnChange(t),n._registerOnDestroy(()=>{e._unregisterOnChange(t)})}function OI(e,n){e==null,Hy(e,n)}function PI(e,n){if(!e.hasOwnProperty("model"))return!1;let t=e.model;return t.isFirstChange()?!0:!Object.is(n,t.currentValue)}function FI(e){return Object.getPrototypeOf(e.constructor)===fI}function LI(e,n){e._syncPendingControls(),n.forEach(t=>{let r=t.control;r.updateOn==="submit"&&r._pendingChange&&(t.viewToModelUpdate(r._pendingValue),r._pendingChange=!1)})}function VI(e,n){if(!n)return null;Array.isArray(n);let t,r,o;return n.forEach(i=>{i.constructor===qn?t=i:FI(i)?r=i:o=i}),o||r||t||null}var jI={provide:Io,useExisting:un(()=>Qi)},Gi=Promise.resolve(),Qi=(()=>{class e extends Io{callSetDisabledState;get submitted(){return Cn(this.submittedReactive)}_submitted=qe(()=>this.submittedReactive());submittedReactive=ut(!1);_directives=new Set;form;ngSubmit=new me;options;constructor(t,r,o){super(),this.callSetDisabledState=o,this.form=new Ol({},jf(t),Bf(r))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(t){Gi.then(()=>{let r=this._findContainer(t.path);t.control=r.registerControl(t.name,t.control),Uy(t.control,t,this.callSetDisabledState),t.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(t)})}getControl(t){return this.form.get(t.path)}removeControl(t){Gi.then(()=>{let r=this._findContainer(t.path);r&&r.removeControl(t.name),this._directives.delete(t)})}addFormGroup(t){Gi.then(()=>{let r=this._findContainer(t.path),o=new Ol({});OI(o,t),r.registerControl(t.name,o),o.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(t){Gi.then(()=>{let r=this._findContainer(t.path);r&&r.removeControl(t.name)})}getFormGroup(t){return this.form.get(t.path)}updateModel(t,r){Gi.then(()=>{this.form.get(t.path).setValue(r)})}setValue(t){this.control.setValue(t)}onSubmit(t){return this.submittedReactive.set(!0),LI(this.form,this._directives),this.ngSubmit.emit(t),t?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(t=void 0){this.form.reset(t),this.submittedReactive.set(!1)}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(t){return t.pop(),t.length?this.form.get(t):this.form}static \u0275fac=function(r){return new(r||e)(E(Yi,10),E(Oy,10),E($f,8))};static \u0275dir=Se({type:e,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(r,o){r&1&&V("submit",function(s){return o.onSubmit(s)})("reset",function(){return o.onReset()})},inputs:{options:[0,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],standalone:!1,features:[mr([jI]),At]})}return e})();function My(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function Iy(e){return typeof e=="object"&&e!==null&&Object.keys(e).length===2&&"value"in e&&"disabled"in e}var BI=class extends Rl{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(n=null,t,r){super(By(t),$y(r,t)),this._applyFormState(n),this._setUpdateStrategy(t),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),Fl(t)&&(t.nonNullable||t.initialValueIsDefault)&&(Iy(n)?this.defaultValue=n.value:this.defaultValue=n)}setValue(n,t={}){this.value=this._pendingValue=n,this._onChange.length&&t.emitModelToViewChange!==!1&&this._onChange.forEach(r=>r(this.value,t.emitViewToModelChange!==!1)),this.updateValueAndValidity(t)}patchValue(n,t={}){this.setValue(n,t)}reset(n=this.defaultValue,t={}){this._applyFormState(n),this.markAsPristine(t),this.markAsUntouched(t),this.setValue(this.value,t),this._pendingChange=!1}_updateValue(){}_anyControls(n){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(n){this._onChange.push(n)}_unregisterOnChange(n){My(this._onChange,n)}registerOnDisabledChange(n){this._onDisabledChange.push(n)}_unregisterOnDisabledChange(n){My(this._onDisabledChange,n)}_forEachChild(n){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(n){Iy(n)?(this.value=this._pendingValue=n.value,n.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=n}};var $I={provide:Zi,useExisting:un(()=>Dr)},Ty=Promise.resolve(),Dr=(()=>{class e extends Zi{_changeDetectorRef;callSetDisabledState;control=new BI;static ngAcceptInputType_isDisabled;_registered=!1;viewModel;name="";isDisabled;model;options;update=new me;constructor(t,r,o,i,s,a){super(),this._changeDetectorRef=s,this.callSetDisabledState=a,this._parent=t,this._setValidators(r),this._setAsyncValidators(o),this.valueAccessor=VI(this,i)}ngOnChanges(t){if(this._checkForErrors(),!this._registered||"name"in t){if(this._registered&&(this._checkName(),this.formDirective)){let r=t.name.previousValue;this.formDirective.removeControl({name:r,path:this._getPath(r)})}this._setUpControl()}"isDisabled"in t&&this._updateDisabled(t),PI(t,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(t){this.viewModel=t,this.update.emit(t)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){Uy(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._checkName()}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(t){Ty.then(()=>{this.control.setValue(t,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(t){let r=t.isDisabled.currentValue,o=r!==0&&Vn(r);Ty.then(()=>{o&&!this.control.disabled?this.control.disable():!o&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(t){return this._parent?TI(t,this._parent):[t]}static \u0275fac=function(r){return new(r||e)(E(Io,9),E(Yi,10),E(Oy,10),E(Ay,10),E(ye,8),E($f,8))};static \u0275dir=Se({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],standalone:!1,features:[mr([$I]),At,Ht]})}return e})();var Ll=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275dir=Se({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""],standalone:!1})}return e})();function UI(e){return typeof e=="number"?e:parseInt(e,10)}var Hf=(()=>{class e{_validator=Lf;_onChange;_enabled;ngOnChanges(t){if(this.inputName in t){let r=this.normalizeInput(t[this.inputName].currentValue);this._enabled=this.enabled(r),this._validator=this._enabled?this.createValidator(r):Lf,this._onChange&&this._onChange()}}validate(t){return this._validator(t)}registerOnValidatorChange(t){this._onChange=t}enabled(t){return t!=null}static \u0275fac=function(r){return new(r||e)};static \u0275dir=Se({type:e,features:[Ht]})}return e})();var HI={provide:Yi,useExisting:un(()=>Ji),multi:!0};var Ji=(()=>{class e extends Hf{required;inputName="required";normalizeInput=Vn;createValidator=t=>mI;enabled(t){return t}static \u0275fac=(()=>{let t;return function(o){return(t||(t=Pn(e)))(o||e)}})();static \u0275dir=Se({type:e,selectors:[["","required","","formControlName","",3,"type","checkbox"],["","required","","formControl","",3,"type","checkbox"],["","required","","ngModel","",3,"type","checkbox"]],hostVars:1,hostBindings:function(r,o){r&2&&Gt("required",o._enabled?"":null)},inputs:{required:"required"},standalone:!1,features:[mr([HI]),At]})}return e})();var zI={provide:Yi,useExisting:un(()=>Ki),multi:!0},Ki=(()=>{class e extends Hf{minlength;inputName="minlength";normalizeInput=t=>UI(t);createValidator=t=>vI(t);static \u0275fac=(()=>{let t;return function(o){return(t||(t=Pn(e)))(o||e)}})();static \u0275dir=Se({type:e,selectors:[["","minlength","","formControlName",""],["","minlength","","formControl",""],["","minlength","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&Gt("minlength",o._enabled?o.minlength:null)},inputs:{minlength:"minlength"},standalone:!1,features:[mr([zI]),At]})}return e})();var GI={provide:Yi,useExisting:un(()=>Xi),multi:!0},Xi=(()=>{class e extends Hf{pattern;inputName="pattern";normalizeInput=t=>t;createValidator=t=>yI(t);static \u0275fac=(()=>{let t;return function(o){return(t||(t=Pn(e)))(o||e)}})();static \u0275dir=Se({type:e,selectors:[["","pattern","","formControlName",""],["","pattern","","formControl",""],["","pattern","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&Gt("pattern",o._enabled?o.pattern:null)},inputs:{pattern:"pattern"},standalone:!1,features:[mr([GI]),At]})}return e})();var qI=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=vn({type:e});static \u0275inj=dn({})}return e})();var Ao=(()=>{class e{static withConfig(t){return{ngModule:e,providers:[{provide:$f,useValue:t.callSetDisabledState??Uf}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=vn({type:e});static \u0275inj=dn({imports:[qI]})}return e})();var Vl={production:!1,FLOWWISE_API_URL:"/api/v1/prediction/a180e12e-d360-47fd-9fbd-443dcc3a5d1d",AUTH_API_URL:"http://localhost:3003/api/auth"};var In=(()=>{class e{http;router;apiUrl=Vl.AUTH_API_URL;constructor(t,r){this.http=t,this.router=r}login(t,r){return this.http.post(`${this.apiUrl}/login`,{email:t,password:r}).pipe(ge(o=>{o.token&&(localStorage.setItem("token",o.token),localStorage.setItem("loginUser",t))}))}register(t,r){return this.http.post(`${this.apiUrl}/register`,{email:t,password:r}).pipe(ge(o=>{o.id&&localStorage.setItem("registeredEmail",t)}))}logout(){localStorage.removeItem("token"),localStorage.removeItem("loginUser"),this.router.navigate(["/sign-in"])}isLoggedIn(){return!!localStorage.getItem("token")}getToken(){return localStorage.getItem("token")}getUserEmail(){return localStorage.getItem("loginUser")}getRegisteredEmail(){return localStorage.getItem("registeredEmail")}static \u0275fac=function(r){return new(r||e)(T(vr),T(Ae))};static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function WI(e,n){if(e&1&&(d(0,"div",7)(1,"p"),p(2),f()()),e&2){let t=N();C(2),fe(t.errorMessage)}}function ZI(e,n){if(e&1&&(d(0,"div",8)(1,"p"),p(2),f()()),e&2){let t=N();C(2),fe(t.successMessage)}}function YI(e,n){e&1&&(d(0,"p"),p(1,"Email is required"),f())}function QI(e,n){e&1&&(d(0,"p"),p(1,"Please enter a valid email address"),f())}function JI(e,n){if(e&1&&(d(0,"div",12),W(1,YI,2,0,"p")(2,QI,2,0,"p"),f()),e&2){N();let t=ot(13);C(),O(t.errors!=null&&t.errors.required?1:-1),C(),O(t.errors!=null&&t.errors.pattern?2:-1)}}function KI(e,n){e&1&&(d(0,"p"),p(1,"Password is required"),f())}function XI(e,n){e&1&&(d(0,"p"),p(1,"Password must be at least 6 characters"),f())}function eT(e,n){if(e&1&&(d(0,"div",12),W(1,KI,2,0,"p")(2,XI,2,0,"p"),f()),e&2){N();let t=ot(19);C(),O(t.errors!=null&&t.errors.required?1:-1),C(),O(t.errors!=null&&t.errors.minlength?2:-1)}}function tT(e,n){e&1&&(d(0,"span"),p(1,"Sign In"),f())}function nT(e,n){e&1&&D(0,"div",16)}var Gy=(()=>{class e{authService;cdr;loginData={email:"",password:""};isLoading=!1;errorMessage="";successMessage="";router=w(Ae);constructor(t,r){this.authService=t,this.cdr=r}onSubmit(){if(this.errorMessage="",this.successMessage="",!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(this.loginData.email)){this.errorMessage="Please enter a valid email address",this.cdr.markForCheck();return}if(this.loginData.password.length<6){this.errorMessage="Password must be at least 6 characters long",this.cdr.markForCheck();return}this.isLoading=!0,this.cdr.markForCheck(),this.authService.login(this.loginData.email,this.loginData.password).subscribe({next:r=>{console.log("API Response:",r),this.isLoading=!1,r.token?(this.successMessage="Login successful! Redirecting...",setTimeout(()=>{this.router.navigate(["/chat-layout"])},1e3)):this.errorMessage=r.message||"Login failed",this.cdr.markForCheck()},error:r=>{this.isLoading=!1,r.status===0?this.errorMessage="Cannot connect to the server. Please check your internet connection.":r.error&&r.error.message?this.errorMessage=r.error.message:this.errorMessage="Login failed. Please check your credentials and try again.",console.error("Login error:",r),this.cdr.markForCheck()}})}static \u0275fac=function(r){return new(r||e)(E(In),E(ye))};static \u0275cmp=q({type:e,selectors:[["app-sign-in"]],decls:29,vars:8,consts:[["loginForm","ngForm"],["email","ngModel"],["password","ngModel"],[1,"parent"],[1,"signin-container"],[1,"signin-box"],["id","signin-form",3,"ngSubmit"],[1,"error-message"],[1,"success-message"],[1,"input-group"],["for","email"],["type","email","id","email","name","email","required","","pattern","[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}",3,"ngModelChange","ngModel"],[1,"validation-error"],["for","password"],["type","password","id","password","name","password","required","","minlength","6",3,"ngModelChange","ngModel"],["type","submit",1,"signin-button",3,"disabled"],[1,"spinner"],[1,"signup-link"],["routerLink","/sign-up"]],template:function(r,o){if(r&1){let i=Ve();d(0,"div",3)(1,"div",4)(2,"div",5)(3,"h2"),p(4,"Sign In"),f(),d(5,"form",6,0),V("ngSubmit",function(){return Q(i),J(o.onSubmit())}),W(7,WI,3,1,"div",7)(8,ZI,3,1,"div",8),d(9,"div",9)(10,"label",10),p(11,"Email"),f(),d(12,"input",11,1),bn("ngModelChange",function(a){return Q(i),Ln(o.loginData.email,a)||(o.loginData.email=a),J(a)}),f(),W(14,JI,3,2,"div",12),f(),d(15,"div",9)(16,"label",13),p(17,"Password"),f(),d(18,"input",14,2),bn("ngModelChange",function(a){return Q(i),Ln(o.loginData.password,a)||(o.loginData.password=a),J(a)}),f(),W(20,eT,3,2,"div",12),f(),d(21,"button",15),W(22,tT,2,0,"span")(23,nT,1,0,"div",16),f()(),d(24,"div",17)(25,"p"),p(26,"Don't have an account? "),d(27,"a",18),p(28,"Sign Up"),f()()()()()()}if(r&2){let i=ot(6),s=ot(13),a=ot(19);C(7),O(o.errorMessage?7:-1),C(),O(o.successMessage?8:-1),C(4),yn("ngModel",o.loginData.email),C(2),O(s.invalid&&(s.dirty||s.touched)?14:-1),C(4),yn("ngModel",o.loginData.password),C(2),O(a.invalid&&(a.dirty||a.touched)?20:-1),C(),te("disabled",i.invalid||o.isLoading),C(),O(o.isLoading?23:22)}},dependencies:[Z,Ao,Ll,qn,ko,Pl,Ji,Ki,Xi,Dr,Qi,je],styles:[".parent[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;background-color:#f5f5f5;padding:20px}.dark[_nghost-%COMP%]   .parent[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .parent[_ngcontent-%COMP%]{background-color:#1a1a2e}.signin-container[_ngcontent-%COMP%]{width:100%;max-width:400px;padding:20px}.signin-box[_ngcontent-%COMP%]{background-color:#fff;border-radius:8px;box-shadow:0 4px 12px #0000001a;padding:30px}.dark[_nghost-%COMP%]   .signin-box[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .signin-box[_ngcontent-%COMP%]{background-color:#222236;box-shadow:0 4px 12px #0000004d}.signin-box[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{text-align:center;color:#333;margin-bottom:30px;font-weight:600}.dark[_nghost-%COMP%]   .signin-box[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .signin-box[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#fff}.input-group[_ngcontent-%COMP%]{margin-bottom:20px}.input-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;margin-bottom:8px;font-weight:500;color:#555}.dark[_nghost-%COMP%]   .input-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .input-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:#d0d0d0}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{width:100%;padding:12px;border:1px solid #ddd;border-radius:4px;font-size:16px;transition:border-color .3s,background-color .3s,color .3s}.dark[_nghost-%COMP%]   .input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{background-color:#2c2c40;border-color:#3f3f5a;color:#fff}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{outline:none;border-color:#4a90e2;box-shadow:0 0 0 2px #4a90e233}.dark[_nghost-%COMP%]   .input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .dark   [_nghost-%COMP%]   .input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{border-color:#5a9ae8;box-shadow:0 0 0 2px #5a9ae840}.signin-button[_ngcontent-%COMP%]{width:100%;padding:12px;background-color:#4a90e2;color:#fff;border:none;border-radius:4px;font-size:16px;font-weight:500;cursor:pointer;transition:background-color .3s;display:flex;justify-content:center;align-items:center;height:45px}.dark[_nghost-%COMP%]   .signin-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .signin-button[_ngcontent-%COMP%]{background-color:#4a7bdb}.signin-button[_ngcontent-%COMP%]:hover{background-color:#3a7bc8}.dark[_nghost-%COMP%]   .signin-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .signin-button[_ngcontent-%COMP%]:hover{background-color:#3a6bc8}.signin-button[_ngcontent-%COMP%]:disabled{background-color:#a0c1e8;cursor:not-allowed}.dark[_nghost-%COMP%]   .signin-button[_ngcontent-%COMP%]:disabled, .dark   [_nghost-%COMP%]   .signin-button[_ngcontent-%COMP%]:disabled{background-color:#3a4a6a;color:#a0a0a0}.signup-link[_ngcontent-%COMP%]{text-align:center;margin-top:20px}.signup-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#4a90e2;text-decoration:none;font-weight:500}.dark[_nghost-%COMP%]   .signup-link[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .signup-link[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#d0d0d0}.dark[_nghost-%COMP%]   .signup-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .signup-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#5a9ae8}.signup-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}.error-message[_ngcontent-%COMP%], .success-message[_ngcontent-%COMP%], .validation-error[_ngcontent-%COMP%]{padding:10px;border-radius:4px;margin-bottom:20px}.error-message[_ngcontent-%COMP%]{background-color:#ffebee;color:#d32f2f;border-left:4px solid #d32f2f}.dark[_nghost-%COMP%]   .error-message[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .error-message[_ngcontent-%COMP%]{background-color:#d32f2f33;color:#ff6b6b;border-left:4px solid #d32f2f}.success-message[_ngcontent-%COMP%]{background-color:#e8f5e9;color:#2e7d32;border-left:4px solid #2e7d32}.dark[_nghost-%COMP%]   .success-message[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .success-message[_ngcontent-%COMP%]{background-color:#2e7d3233;color:#66bb6a;border-left:4px solid #2e7d32}.validation-error[_ngcontent-%COMP%]{background-color:#ffebee;color:#d32f2f;padding:8px;margin-top:5px;margin-bottom:0;font-size:14px;border-radius:4px}.dark[_nghost-%COMP%]   .validation-error[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .validation-error[_ngcontent-%COMP%]{background-color:#d32f2f33;color:#ff6b6b}.validation-error[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;padding:0}.spinner[_ngcontent-%COMP%]{width:20px;height:20px;border:3px solid rgba(255,255,255,.3);border-radius:50%;border-top-color:#fff;animation:_ngcontent-%COMP%_spin 1s ease-in-out infinite}@keyframes _ngcontent-%COMP%_spin{to{transform:rotate(360deg)}}@media (max-width: 480px){.signin-container[_ngcontent-%COMP%]{padding:10px}.signin-box[_ngcontent-%COMP%]{padding:20px}}"],changeDetection:0})}return e})();function rT(e,n){if(e&1&&(d(0,"div",7)(1,"p"),p(2),f()()),e&2){let t=N();C(2),fe(t.errorMessage)}}function oT(e,n){if(e&1&&(d(0,"div",8)(1,"p"),p(2),f()()),e&2){let t=N();C(2),fe(t.successMessage)}}function iT(e,n){e&1&&(d(0,"p"),p(1,"Email is required"),f())}function sT(e,n){e&1&&(d(0,"p"),p(1,"Please enter a valid email address"),f())}function aT(e,n){if(e&1&&(d(0,"div",12),W(1,iT,2,0,"p")(2,sT,2,0,"p"),f()),e&2){N();let t=ot(13);C(),O(t.errors!=null&&t.errors.required?1:-1),C(),O(t.errors!=null&&t.errors.pattern?2:-1)}}function lT(e,n){e&1&&(d(0,"p"),p(1,"Password is required"),f())}function cT(e,n){e&1&&(d(0,"p"),p(1,"Password must be at least 6 characters"),f())}function uT(e,n){if(e&1&&(d(0,"div",12),W(1,lT,2,0,"p")(2,cT,2,0,"p"),f()),e&2){N();let t=ot(19);C(),O(t.errors!=null&&t.errors.required?1:-1),C(),O(t.errors!=null&&t.errors.minlength?2:-1)}}function dT(e,n){e&1&&(d(0,"span"),p(1,"Sign Up"),f())}function fT(e,n){e&1&&D(0,"div",16)}var qy=(()=>{class e{authService;cdr;registerData={email:"",password:""};isLoading=!1;errorMessage="";successMessage="";router=w(Ae);constructor(t,r){this.authService=t,this.cdr=r}onSubmit(){if(this.errorMessage="",this.successMessage="",!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(this.registerData.email)){this.errorMessage="Please enter a valid email address",this.cdr.markForCheck();return}if(this.registerData.password.length<6){this.errorMessage="Password must be at least 6 characters long",this.cdr.markForCheck();return}this.isLoading=!0,this.cdr.markForCheck(),this.authService.register(this.registerData.email,this.registerData.password).subscribe({next:r=>{console.log("API Response:",r),this.isLoading=!1,r.id?(this.successMessage="Registration successful! Redirecting to login...",setTimeout(()=>{this.router.navigate(["/sign-in"])},2e3)):this.errorMessage=r.message||"Registration failed",this.cdr.markForCheck()},error:r=>{this.isLoading=!1,console.error("Registration error:",r),r.status===0?this.errorMessage="Cannot connect to the server. Please check your internet connection.":r.status===500?this.errorMessage="Internal server error. Please try again later.":r.error&&r.error.message?this.errorMessage=r.error.message:r.message?this.errorMessage=r.message:this.errorMessage="Registration failed. Please try again.",this.cdr.markForCheck()}})}static \u0275fac=function(r){return new(r||e)(E(In),E(ye))};static \u0275cmp=q({type:e,selectors:[["app-sign-up"]],decls:29,vars:8,consts:[["registerForm","ngForm"],["email","ngModel"],["password","ngModel"],[1,"parent"],[1,"signup-container"],[1,"signup-box"],["id","signup-form",3,"ngSubmit"],[1,"error-message"],[1,"success-message"],[1,"input-group"],["for","email"],["type","email","id","email","name","email","required","","pattern","[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}",3,"ngModelChange","ngModel"],[1,"validation-error"],["for","password"],["type","password","id","password","name","password","required","","minlength","6",3,"ngModelChange","ngModel"],["type","submit",1,"signup-button",3,"disabled"],[1,"spinner"],[1,"login-link"],["routerLink","/sign-in"]],template:function(r,o){if(r&1){let i=Ve();d(0,"div",3)(1,"div",4)(2,"div",5)(3,"h2"),p(4,"Sign Up"),f(),d(5,"form",6,0),V("ngSubmit",function(){return Q(i),J(o.onSubmit())}),W(7,rT,3,1,"div",7)(8,oT,3,1,"div",8),d(9,"div",9)(10,"label",10),p(11,"Email"),f(),d(12,"input",11,1),bn("ngModelChange",function(a){return Q(i),Ln(o.registerData.email,a)||(o.registerData.email=a),J(a)}),f(),W(14,aT,3,2,"div",12),f(),d(15,"div",9)(16,"label",13),p(17,"Password"),f(),d(18,"input",14,2),bn("ngModelChange",function(a){return Q(i),Ln(o.registerData.password,a)||(o.registerData.password=a),J(a)}),f(),W(20,uT,3,2,"div",12),f(),d(21,"button",15),W(22,dT,2,0,"span")(23,fT,1,0,"div",16),f()(),d(24,"div",17)(25,"p"),p(26,"Already have an account? "),d(27,"a",18),p(28,"Log In"),f()()()()()()}if(r&2){let i=ot(6),s=ot(13),a=ot(19);C(7),O(o.errorMessage?7:-1),C(),O(o.successMessage?8:-1),C(4),yn("ngModel",o.registerData.email),C(2),O(s.invalid&&(s.dirty||s.touched)?14:-1),C(4),yn("ngModel",o.registerData.password),C(2),O(a.invalid&&(a.dirty||a.touched)?20:-1),C(),te("disabled",i.invalid||o.isLoading),C(),O(o.isLoading?23:22)}},dependencies:[Z,Ao,Ll,qn,ko,Pl,Ji,Ki,Xi,Dr,Qi,je],styles:[".parent[_ngcontent-%COMP%]{margin:0;font-family:Arial,sans-serif;background-color:#f4f4f4;display:flex;justify-content:center;align-items:center;height:100vh}.dark[_nghost-%COMP%]   .parent[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .parent[_ngcontent-%COMP%]{background-color:#1a1a2e}.signup-container[_ngcontent-%COMP%]{width:100%;max-width:400px;padding:20px;box-sizing:border-box}.signup-box[_ngcontent-%COMP%]{background-color:#fff;padding:40px;border-radius:8px;box-shadow:0 4px 10px #0000001a;text-align:center}.dark[_nghost-%COMP%]   .signup-box[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .signup-box[_ngcontent-%COMP%]{background-color:#222236;box-shadow:0 4px 12px #0000004d}.signup-box[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin-bottom:20px;font-size:24px;color:#333}.dark[_nghost-%COMP%]   .signup-box[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .signup-box[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#fff}.input-group[_ngcontent-%COMP%]{margin-bottom:20px;text-align:left}.input-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;margin-bottom:8px;font-size:14px;color:#555}.dark[_nghost-%COMP%]   .input-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .input-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:#d0d0d0}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{width:100%;padding:10px;border:1px solid #ddd;border-radius:4px;font-size:16px;box-sizing:border-box;transition:border-color .3s,background-color .3s,color .3s}.dark[_nghost-%COMP%]   .input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{background-color:#2c2c40;border-color:#3f3f5a;color:#fff}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{outline:none;border-color:#4a90e2;box-shadow:0 0 0 2px #4a90e233}.dark[_nghost-%COMP%]   .input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .dark   [_nghost-%COMP%]   .input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{border-color:#5a9ae8;box-shadow:0 0 0 2px #5a9ae840}.signup-button[_ngcontent-%COMP%]{width:100%;padding:12px;background-color:#007bff;color:#fff;border:none;border-radius:4px;font-size:16px;cursor:pointer;transition:background-color .3s ease;display:flex;justify-content:center;align-items:center;height:45px}.dark[_nghost-%COMP%]   .signup-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .signup-button[_ngcontent-%COMP%]{background-color:#4a7bdb}.signup-button[_ngcontent-%COMP%]:hover{background-color:#0056b3}.dark[_nghost-%COMP%]   .signup-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .signup-button[_ngcontent-%COMP%]:hover{background-color:#3a6bc8}.signup-button[_ngcontent-%COMP%]:disabled{background-color:#a0c1e8;cursor:not-allowed}.dark[_nghost-%COMP%]   .signup-button[_ngcontent-%COMP%]:disabled, .dark   [_nghost-%COMP%]   .signup-button[_ngcontent-%COMP%]:disabled{background-color:#3a4a6a;color:#a0a0a0}.login-link[_ngcontent-%COMP%]{margin-top:20px;font-size:14px;color:#555}.dark[_nghost-%COMP%]   .login-link[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .login-link[_ngcontent-%COMP%]{color:#d0d0d0}.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#007bff;text-decoration:none;font-weight:500;cursor:pointer}.dark[_nghost-%COMP%]   .login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#5a9ae8}.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}.error-message[_ngcontent-%COMP%], .success-message[_ngcontent-%COMP%], .validation-error[_ngcontent-%COMP%]{padding:10px;border-radius:4px;margin-bottom:20px}.error-message[_ngcontent-%COMP%]{background-color:#ffebee;color:#d32f2f;border-left:4px solid #d32f2f}.dark[_nghost-%COMP%]   .error-message[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .error-message[_ngcontent-%COMP%]{background-color:#d32f2f33;color:#ff6b6b;border-left:4px solid #d32f2f}.success-message[_ngcontent-%COMP%]{background-color:#e8f5e9;color:#2e7d32;border-left:4px solid #2e7d32}.dark[_nghost-%COMP%]   .success-message[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .success-message[_ngcontent-%COMP%]{background-color:#2e7d3233;color:#66bb6a;border-left:4px solid #2e7d32}.validation-error[_ngcontent-%COMP%]{background-color:#ffebee;color:#d32f2f;padding:8px;margin-top:5px;margin-bottom:0;font-size:14px;border-radius:4px}.dark[_nghost-%COMP%]   .validation-error[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .validation-error[_ngcontent-%COMP%]{background-color:#d32f2f33;color:#ff6b6b}.validation-error[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;padding:0}.spinner[_ngcontent-%COMP%]{width:20px;height:20px;border:3px solid rgba(255,255,255,.3);border-radius:50%;border-top-color:#fff;animation:_ngcontent-%COMP%_spin 1s ease-in-out infinite}@keyframes _ngcontent-%COMP%_spin{to{transform:rotate(360deg)}}@media (max-width: 480px){.signup-box[_ngcontent-%COMP%]{padding:20px}.signup-box[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:20px}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .signup-button[_ngcontent-%COMP%]{font-size:14px}.login-link[_ngcontent-%COMP%]{font-size:12px}}"],changeDetection:0})}return e})();var we=function(e){return e.User="user",e.Assistant="assistant",e.System="system",e.Thinking="thinking",e}(we||{}),gt=function(e){return e.Light="light",e.Dark="dark",e.System="system",e}(gt||{}),$e=function(e){return e.Start="start",e.Content="content",e.End="end",e.Error="error",e.Thinking="thinking",e.SourceDocuments="sourceDocuments",e.AgentReasoning="agentReasoning",e.NextAgent="nextAgent",e}($e||{});var Pe=[];for(jl=0;jl<256;++jl)Pe.push((jl+256).toString(16).slice(1));var jl;function Wy(e,n=0){return(Pe[e[n+0]]+Pe[e[n+1]]+Pe[e[n+2]]+Pe[e[n+3]]+"-"+Pe[e[n+4]]+Pe[e[n+5]]+"-"+Pe[e[n+6]]+Pe[e[n+7]]+"-"+Pe[e[n+8]]+Pe[e[n+9]]+"-"+Pe[e[n+10]]+Pe[e[n+11]]+Pe[e[n+12]]+Pe[e[n+13]]+Pe[e[n+14]]+Pe[e[n+15]]).toLowerCase()}var Bl,hT=new Uint8Array(16);function qf(){if(!Bl&&(Bl=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!Bl))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Bl(hT)}var pT=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),Wf={randomUUID:pT};function gT(e,n,t){if(Wf.randomUUID&&!n&&!e)return Wf.randomUUID();e=e||{};var r=e.random||(e.rng||qf)();if(r[6]=r[6]&15|64,r[8]=r[8]&63|128,n){t=t||0;for(var o=0;o<16;++o)n[t+o]=r[o];return n}return Wy(r)}var _r=gT;var Yt=(()=>{class e{http;chats=ut([]);activeChat=ut(null);messageChunksSubject=new pe;thinkingSubject=new De(null);sourceDocumentsSubject=new De([]);isProcessing=ut(!1);currentEvent=ut(null);sourceDocumentBuffer="";isCollectingSourceDocuments=!1;FLOWWISE_API_URL=Vl.FLOWWISE_API_URL;messageChunks$=this.messageChunksSubject.asObservable();thinking$=this.thinkingSubject.asObservable();sourceDocuments$=this.sourceDocumentsSubject.asObservable();currentChat=qe(()=>this.activeChat());chatHistory=qe(()=>this.chats());isAiThinking=qe(()=>this.isProcessing());shouldDisableInput=qe(()=>this.isProcessing());currentEventType=qe(()=>this.currentEvent());constructor(t){this.http=t,this.loadChatsFromStorage(),this.chats().length===0?this.createNewChat():this.setActiveChat(this.chats()[0].id),wn(()=>{this.chats().length>0&&this.saveChatsToStorage()})}createNewChat(){let t={id:_r(),title:"New Chat",messages:[],createdAt:new Date,updatedAt:new Date,reasoningSteps:[],sourceDocuments:[]};this.chats.update(r=>[t,...r]),this.activeChat.set(t)}setActiveChat(t){let r=this.chats().find(o=>o.id===t);r&&this.activeChat.set(b({},r))}sendMessage(t){if(console.log("Sending message:",t),!t.trim())return;let r=this.activeChat();if(!r){console.error("No active chat found");return}let o=_r();this.addMessageToChat({id:_r(),role:we.User,content:t,timestamp:new Date}),this.addMessageToChat({id:o,role:we.Assistant,content:"",timestamp:new Date,isComplete:!1});let i={question:t,streaming:!0};r.flowChatId?(i.chatId=r.flowChatId,console.log("Using existing flowChatId:",r.flowChatId),console.log(`Current chat has ${r.messages.length} messages (including this new one)`)):console.log("Starting new conversation (no flowChatId)"),r.sessionId&&(i.sessionId=r.sessionId,console.log("Using existing sessionId:",r.sessionId)),console.log("Starting streaming request with payload:",JSON.stringify(i)),this.streamFlowWiseResponse(i,o)}streamFlowWiseResponse(t,r){return new Promise((o,i)=>{try{console.log("Starting FlowWise API stream with payload:",t),this.isProcessing.set(!0),fetch(this.FLOWWISE_API_URL,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}).then(s=>Jt(this,null,function*(){if(!s.ok){let u=yield s.text();console.error("FlowWise API error:",s.status,u),this.updateMessage(r,`Error: ${s.status} - ${u||"Unknown error"}`,!0),this.isProcessing.set(!1),i(new Error(`API error: ${s.status}`));return}if(!s.body){console.error("No response body received"),this.updateMessage(r,"Error: No response received",!0),this.isProcessing.set(!1),i(new Error("No response body"));return}let a=s.body.getReader(),l="";Jt(this,null,function*(){try{for(;;){let{done:u,value:h}=yield a.read();if(u){if(console.log("Stream complete"),l.trim())try{let y=JSON.parse(l);this.processStreamEvent(y,r)}catch{let _=this.attemptToFixMalformedJson(l);if(_)try{let A=JSON.parse(_);this.processStreamEvent(A,r)}catch{console.error("Failed to parse fixed JSON at stream end")}}this.isProcessing.set(!1),this.messageChunksSubject.next({type:$e.End,messageId:r});let v=this.getMessageById(r);v&&!v.isComplete&&this.updateMessage(r,v.content,!0);break}let m=new TextDecoder().decode(h);l+=m;let g=0;for(;g<l.length;)try{let v=l.indexOf("{",g);if(v===-1)break;let y=1,_=v+1;for(;y>0&&_<l.length;)l[_]==="{"?y++:l[_]==="}"&&y--,_++;if(y===0){let A=l.substring(v,_);try{let ae=JSON.parse(A);this.processStreamEvent(ae,r),l=l.substring(_),g=0}catch{let oe=this.attemptToFixMalformedJson(A);if(oe)try{let wt=JSON.parse(oe);this.processStreamEvent(wt,r),l=l.substring(_),g=0}catch{g=v+1}else g=v+1}}else break}catch{g++}}}catch(u){console.error("Stream processing error:",u),this.isProcessing.set(!1),this.messageChunksSubject.next({type:$e.Error,content:`Stream error: ${u instanceof Error?u.message:"Unknown error"}`,messageId:r}),this.updateMessage(r,`Error: ${u instanceof Error?u.message:"Unknown error"}`,!0),i(u)}}).then(()=>{o()}).catch(u=>{i(u)})})).catch(s=>{console.error("Stream setup error:",s),this.isProcessing.set(!1),this.messageChunksSubject.next({type:$e.Error,content:`Error: ${s instanceof Error?s.message:"Unknown error"}`,messageId:r}),this.updateMessage(r,`Error: ${s instanceof Error?s.message:"Unknown error"}`,!0),i(s)})}catch(s){console.error("Stream setup error:",s),this.isProcessing.set(!1),this.messageChunksSubject.next({type:$e.Error,content:`Error: ${s instanceof Error?s.message:"Unknown error"}`,messageId:r}),this.updateMessage(r,`Error: ${s instanceof Error?s.message:"Unknown error"}`,!0),i(s)}})}processStreamEvent(t,r){this.currentEvent.set(t.event);let o=null,i,s=[],a;switch(t.event){case"start":console.log("Stream started"),this.isProcessing.set(!0),this.messageChunksSubject.next({type:$e.Start,messageId:r});break;case"token":{let l=typeof t.data=="string"?t.data:"";l&&(this.messageChunksSubject.next({type:$e.Content,content:l,messageId:r}),this.updateMessage(r,l,!1,!0))}break;case"end":console.log("Stream ended"),this.isProcessing.set(!1),this.messageChunksSubject.next({type:$e.End,messageId:r});{let l=this.getMessageById(r);l&&this.updateMessage(r,l.content,!0)}break;case"error":console.log("Stream error event:",t.data),this.isProcessing.set(!1),o=typeof t.data=="string"?`Error: ${t.data}`:"An error occurred during processing",this.messageChunksSubject.next({type:$e.Error,content:o,messageId:r}),this.updateMessage(r,o,!0);break;case"metadata":console.log("Received metadata:",t.data),t.data&&typeof t.data=="object"&&(i=t.data,i.chatId&&typeof i.chatId=="string"&&this.storeChatId(i.chatId),i.sessionId&&typeof i.sessionId=="string"&&this.storeSessionId(i.sessionId));break;case"sourceDocuments":console.log("Received source documents:",t.data),s=Array.isArray(t.data)?t.data:[],s=s.map(l=>I(b({},l),{id:l.id||_r()})),this.activeChat.update(l=>l?I(b({},l),{sourceDocuments:s}):null),this.updateChatInList(),this.saveChatsToStorage(),this.sourceDocumentsSubject.next(s);{let l=this.formatSourceDocuments(s);l&&this.messageChunksSubject.next({type:$e.SourceDocuments,content:l,messageId:r})}break;case"agentReasoning":console.log("Received agent reasoning:",t.data),t.data&&(Array.isArray(t.data)?(t.data.length>0&&(a=t.data[t.data.length-1],this.thinkingSubject.next(a)),this.updateReasoningSteps(t.data)):typeof t.data=="object"&&(a=t.data,this.thinkingSubject.next(a),this.updateReasoningSteps([a])));break;case"nextAgent":console.log("Switching to next agent:",t.data);break;default:if(typeof t=="object"&&t!==null){let l=t,c=l.event,u=l.data;console.log(`Unhandled event type: ${c}`,u)}else console.log("Received unknown event format:",t);break}}getMessageById(t){let r=this.activeChat();if(r)return r.messages.find(o=>o.id===t)}storeChatId(t){this.activeChat.update(r=>r?I(b({},r),{flowChatId:t}):null),this.updateChatInList(),this.saveChatsToStorage(),console.log("Stored chat ID:",t)}storeSessionId(t){this.activeChat.update(r=>r?I(b({},r),{sessionId:t}):null),this.updateChatInList(),this.saveChatsToStorage(),console.log("Stored session ID:",t)}callFlowWiseApi(t){return Jt(this,null,function*(){try{return yield lc(this.http.post(this.FLOWWISE_API_URL,t).pipe(Et(r=>{throw console.error("API error:",r),new Error(r.message)})))}catch(r){throw console.error("Error in API call:",r),r}})}processFlowWiseResponse(t,r){this.activeChat.update(o=>o?I(b({},o),{flowChatId:t.chatId,sessionId:t.sessionId}):null),this.updateChatInList(),t.agentReasoning&&t.agentReasoning.length>0&&(this.thinkingSubject.next(null),t.agentReasoning.forEach((o,i)=>{setTimeout(()=>{this.thinkingSubject.next(o);let s=this.formatThinkingStep(o);this.messageChunksSubject.next({type:$e.Thinking,content:s})},i*800)})),this.simulateTypingEffect(r,t.text)}formatThinkingStep(t){if(!t)return"";let r="";return t.agentName&&(r+=`**Agent: ${t.agentName}**

`),Array.isArray(t.messages)&&t.messages.length>0&&(r+=t.messages.join(`

`)),t.thought&&(r+=`

**Thought Process:**
${t.thought}`),t.action&&(r+=`

**Action:**
${t.action}`),t.observation&&(r+=`

**Observation:**
${t.observation}`),r}simulateTypingEffect(t,r){let i=[];for(let a=0;a<r.length;a+=5)i.push(r.slice(a,a+5));let s="";i.forEach((a,l)=>{setTimeout(()=>{s+=a,this.messageChunksSubject.next({type:$e.Content,content:a,messageId:t}),this.updateMessage(t,s)},l*30)}),setTimeout(()=>{this.messageChunksSubject.next({type:$e.End,messageId:t}),this.updateMessage(t,r,!0)},i.length*30+100)}addMessageToChat(t){this.activeChat()&&(this.activeChat.update(r=>{if(!r)return null;let o=I(b({},r),{messages:[...r.messages,t],updatedAt:new Date});return r.messages.length===0&&t.role===we.User&&(o.title=this.generateChatTitle(t.content)),o}),this.updateChatInList())}updateMessage(t,r,o=!1,i=!1){console.log(`Updating message ${t}, isComplete: ${o}`),this.activeChat.update(s=>{if(!s)return null;let a=s.messages.map(l=>l.id===t?i?I(b({},l),{content:l.content+r,isComplete:o}):I(b({},l),{content:r,isComplete:o}):l);return I(b({},s),{messages:a,updatedAt:new Date})}),this.updateChatInList()}updateChatInList(){let t=this.activeChat();t&&this.chats.update(r=>r.map(o=>o.id===t.id?b({},t):o))}generateChatTitle(t){return t.length<=30?t:t.substring(0,30)+"..."}saveChatsToStorage(){localStorage.setItem("ai-chat-data",JSON.stringify(this.chats()))}loadChatsFromStorage(){let t=localStorage.getItem("ai-chat-data");if(t)try{let o=JSON.parse(t).map(i=>I(b({},i),{createdAt:new Date(i.createdAt),updatedAt:new Date(i.updatedAt),messages:i.messages.map(s=>I(b({},s),{timestamp:new Date(s.timestamp)})),reasoningSteps:Array.isArray(i.reasoningSteps)?i.reasoningSteps.map(s=>({agentName:s.agentName||"Unknown Agent",messages:Array.isArray(s.messages)?[...s.messages]:[],next:s.next,instructions:s.instructions,usedTools:s.usedTools,sourceDocuments:s.sourceDocuments,artifacts:s.artifacts,nodeId:s.nodeId,thought:s.thought,action:s.action,observation:s.observation})):[],sourceDocuments:Array.isArray(i.sourceDocuments)?i.sourceDocuments:[]}));this.chats.set(o),console.log("Loaded chats from storage:",o)}catch(r){console.error("Failed to parse stored chats:",r)}}deleteChat(t){if(console.log("Deleting chat:",t),this.chats.update(r=>r.filter(o=>o.id!==t)),this.activeChat()?.id===t){let r=this.chats()[0];this.activeChat.set(r?b({},r):null),r||this.createNewChat()}this.messageChunksSubject.next({type:$e.End,content:"Chat deleted",messageId:"system-notification"}),this.saveChatsToStorage()}clearCurrentChat(){this.activeChat()&&(console.log("Clearing current chat"),this.activeChat.update(t=>t?I(b({},t),{messages:[],title:"New Chat",updatedAt:new Date,flowChatId:void 0,reasoningSteps:[],sourceDocuments:[]}):null),this.updateChatInList(),this.messageChunksSubject.next({type:$e.End,content:"Chat cleared",messageId:"system-notification"}),this.thinkingSubject.next(null),this.sourceDocumentsSubject.next([]),this.saveChatsToStorage())}formatSourceDocuments(t){if(!t||t.length===0)return"";let r=`## Source Documents

`;return t.forEach((o,i)=>{r+=`### Document ${i+1}

`,r+=`${o.pageContent}

`,o.metadata&&(o.metadata.source&&(r+=`*Source:* ${o.metadata.source}
`),o.metadata.title&&(r+=`*Title:* ${o.metadata.title}
`)),r+=`
---

`}),r}attemptToFixMalformedJson(t){try{try{return JSON.parse(t),t}catch{console.log("JSON is not valid, attempting to fix")}if(!t.trim()||!t.includes("{")&&!t.includes("["))return null;if(t.includes('"event":"token"')){let l=/"event":"token".*?"data":"([^"]*)/,c=t.match(l);if(c){let u=`{"event":"token","data":"${c[1]}"}`;try{return JSON.parse(u),console.log("Fixed token event JSON"),u}catch{}}}if(t.includes('"event":"start"')){let l='{"event":"start","data":[]}';try{return JSON.parse(l),console.log("Fixed start event JSON"),l}catch{}}if(t.includes('"event":"end"')){let l='{"event":"end"}';try{return JSON.parse(l),console.log("Fixed end event JSON"),l}catch{}}if(t.includes('"event":"metadata"')){let l=/"event":"metadata".*?"data":(\{[^}]*)/,c=t.match(l);if(c){let u=c[1];u.endsWith("}")||(u+="}");let h=`{"event":"metadata","data":${u}}`;try{return JSON.parse(h),console.log("Fixed metadata event JSON"),h}catch{let m='{"event":"metadata","data":{}}';try{return JSON.parse(m),console.log("Fixed metadata event with empty data"),m}catch{}}}}if(t.includes('"event":"sourceDocuments"')){let l='{"event":"sourceDocuments","data":[]}';try{return JSON.parse(l),console.log("Fixed sourceDocuments event with empty data"),l}catch{}}if(t.includes('"event":"agentReasoning"')){let l=/"event":"agentReasoning".*?"data":(\[|\{)/,c=t.match(l);if(c)if(c[1]==="["){let u='{"event":"agentReasoning","data":[]}';try{return JSON.parse(u),console.log("Fixed agentReasoning event with empty array"),u}catch{}}else{let u='{"event":"agentReasoning","data":{}}';try{return JSON.parse(u),console.log("Fixed agentReasoning event with empty object"),u}catch{}}}console.log("Attempting general JSON fix approach");let r="",o=0,i=0,s=!1,a=!1;for(let l=0;l<t.length;l++){let c=t[l];if(r+=c,a){a=!1;continue}if(c==="\\"){a=!0;continue}if(c==='"'&&!a){s=!s;continue}if(!s&&(c==="{"?o++:c==="}"?o--:c==="["?i++:c==="]"&&i--,o===0&&i===0&&r.trim().length>1))try{return JSON.parse(r),console.log("Successfully fixed JSON using general approach"),r}catch{}}if(t.includes('"event"')&&t.includes('"data"')){let l=t+"}";try{return JSON.parse(l),console.log("Fixed JSON by adding closing brace"),l}catch{}}return console.error("Failed to fix malformed JSON"),null}catch{return console.error("Error while trying to fix JSON:"),null}}updateReasoningSteps(t){if(!t||t.length===0)return;let r=t.map(o=>({agentName:o.agentName||"Unknown Agent",messages:Array.isArray(o.messages)?[...o.messages]:[],next:o.next,instructions:o.instructions,usedTools:o.usedTools?JSON.parse(JSON.stringify(o.usedTools)):void 0,sourceDocuments:o.sourceDocuments?JSON.parse(JSON.stringify(o.sourceDocuments)):void 0,artifacts:o.artifacts?JSON.parse(JSON.stringify(o.artifacts)):void 0,nodeId:o.nodeId,thought:o.thought,action:o.action,observation:o.observation}));this.activeChat.update(o=>o?I(b({},o),{reasoningSteps:r}):null),this.updateChatInList(),this.saveChatsToStorage(),console.log("Updated reasoning steps in active chat:",r.length)}updateSourceDocuments(t){if(!t||t.length===0)return;console.log("Updating source documents in active chat:",t.length);let r=t.map(o=>({id:o.id||_r(),pageContent:o.pageContent,metadata:o.metadata?JSON.parse(JSON.stringify(o.metadata)):{source:"Unknown Source",blobType:"text/plain"}}));this.activeChat.update(o=>o?I(b({},o),{sourceDocuments:r}):null),this.updateChatInList(),this.saveChatsToStorage(),this.sourceDocumentsSubject.next(r)}static \u0275fac=function(r){return new(r||e)(T(vr))};static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function mT(e,n){e&1&&(d(0,"div",5)(1,"p"),p(2,"No reasoning steps yet"),f(),d(3,"p",7),p(4,"Ask a question to see how the AI processes it"),f()())}function vT(e,n){if(e&1&&(d(0,"div",14)(1,"h4",16),p(2,"INSTRUCTIONS:"),f(),d(3,"div",17),p(4),f()()),e&2){let t=N().$implicit;C(4),ke(" ",t.instructions," ")}}function yT(e,n){if(e&1&&(d(0,"div",17),p(1),f()),e&2){let t=n.$implicit;C(),ke(" ",t," ")}}function bT(e,n){if(e&1&&(d(0,"div",14)(1,"h4",16),p(2,"REASONING:"),f(),d(3,"div",18),qt(4,yT,2,1,"div",17,Ed),f()()),e&2){let t=N().$implicit;C(4),Wt(t.messages)}}function CT(e,n){if(e&1&&(d(0,"div",15)(1,"div",11)(2,"span",19),p(3,"NEXT:"),f(),d(4,"span",20),p(5),f()()()),e&2){let t=N().$implicit;C(5),fe(t.next)}}function wT(e,n){if(e&1&&(d(0,"div",9)(1,"div",10)(2,"div",11)(3,"div",12),p(4),f(),d(5,"h3",13),p(6),f()(),W(7,vT,5,1,"div",14)(8,bT,6,0,"div",14)(9,CT,6,1,"div",15),f()()),e&2){let t=n.$implicit,r=n.$index,o=N(2);dt("animate-pulse",r===o.reasoningSteps.length-1&&!o.isComplete),C(4),ke(" ",r+1," "),C(2),fe(t.agentName),C(),O(t.instructions?7:-1),C(),O(t.messages&&t.messages.length>0?8:-1),C(),O(t.next?9:-1)}}function DT(e,n){if(e&1&&(d(0,"div",6),qt(1,wT,10,7,"div",8,Ed),f()),e&2){let t=N();C(),Wt(t.reasoningSteps)}}var Zy=(()=>{class e{chatService;cdr;reasoningSteps=[];isComplete=!1;subscription=new ie;currentChatId=null;constructor(t,r){this.chatService=t,this.cdr=r,wn(()=>{let o=this.chatService.currentChat();o&&o.id!==this.currentChatId?(this.currentChatId=o.id,o.reasoningSteps&&o.reasoningSteps.length>0?(this.reasoningSteps=[...o.reasoningSteps],this.isComplete=!0):(this.reasoningSteps=[],this.isComplete=!1),this.cdr.markForCheck()):o||(this.currentChatId=null,this.reasoningSteps=[],this.isComplete=!1,this.cdr.markForCheck())})}ngOnInit(){let t=this.chatService.currentChat();t&&t.reasoningSteps&&t.reasoningSteps.length>0&&(this.reasoningSteps=[...t.reasoningSteps],this.isComplete=!0,this.cdr.markForCheck()),this.subscription.add(this.chatService.thinking$.subscribe(r=>{r&&(this.reasoningSteps.push(r),this.chatService.currentChat()&&this.chatService.updateReasoningSteps(this.reasoningSteps),this.cdr.markForCheck())})),this.subscription.add(this.chatService.messageChunks$.subscribe(r=>{r.type==="start"?(this.reasoningSteps=[],this.isComplete=!1,this.chatService.updateReasoningSteps([]),this.cdr.markForCheck()):r.type==="end"&&(this.isComplete=!0,this.cdr.markForCheck())}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)(E(Yt),E(ye))};static \u0275cmp=q({type:e,selectors:[["app-thinking-panel"]],decls:9,vars:2,consts:[[1,"h-full","flex","flex-col","bg-[var(--techwave-some-r-bg-color)]"],[1,"p-4","border-b","border-[var(--techwave-border-color)]"],[1,"text-lg","font-medium","text-[var(--techwave-heading-color)]"],[1,"text-sm","text-[var(--techwave-body-color)]"],[1,"flex-1","overflow-y-auto","scrollbar-thin","p-4"],[1,"text-center","py-8","text-[var(--techwave-body-color)]","text-sm"],[1,"space-y-4"],[1,"mt-2"],[1,"bg-[var(--techwave-card-bg-color)]","rounded-lg","p-4","border","border-[var(--techwave-border-color)]",3,"animate-pulse"],[1,"bg-[var(--techwave-card-bg-color)]","rounded-lg","p-4","border","border-[var(--techwave-border-color)]"],[1,"flex","flex-col","gap-3"],[1,"flex","items-center"],[1,"h-8","w-8","rounded-full","bg-gradient-to-r","from-amber-300","to-amber-500","dark:from-amber-700","dark:to-amber-900","text-amber-900","dark:text-amber-100","flex","items-center","justify-center","text-xs","font-semibold","mr-3"],[1,"text-md","font-medium","text-[var(--techwave-heading-color)]"],[1,"ml-2"],[1,"ml-2","mt-1"],[1,"text-xs","uppercase","tracking-wider","text-[var(--techwave-body-color)]","font-semibold","mb-2"],[1,"text-sm","text-[var(--techwave-heading-color)]","bg-[var(--techwave-some-a-bg-color)]","p-3","rounded","border","border-[var(--techwave-border-color)]","whitespace-pre-line"],[1,"space-y-3"],[1,"text-xs","uppercase","tracking-wider","text-[var(--techwave-body-color)]","font-semibold","mr-2"],[1,"text-sm","font-medium","text-[var(--techwave-heading-color)]","bg-[var(--techwave-some-a-bg-color)]","px-2","py-1","rounded"]],template:function(r,o){r&1&&(d(0,"div",0)(1,"div",1)(2,"h2",2),p(3,"Reasoning Process"),f(),d(4,"p",3),p(5," Watch how the AI processes your question "),f()(),d(6,"div",4),W(7,mT,5,0,"div",5)(8,DT,3,0,"div",6),f()()),r&2&&(C(7),O(o.reasoningSteps.length===0?7:-1),C(),O(o.reasoningSteps.length>0?8:-1))},dependencies:[Z],styles:['[_nghost-%COMP%]{display:block;height:100%}.whitespace-pre-line[_ngcontent-%COMP%]{white-space:pre-line}@keyframes _ngcontent-%COMP%_pulse{0%,to{opacity:1}50%{opacity:.7}}.animate-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 1.5s ease-in-out infinite}.scrollbar-thin[_ngcontent-%COMP%]{scrollbar-width:thin}.scrollbar-thin[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.scrollbar-thin[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:var(--techwave-some-r-bg-color)}.scrollbar-thin[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:var(--techwave-border-color);border-radius:3px}.scrollbar-thin[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background-color:var(--techwave-body-color)}[class*="bg-[var(--techwave-card-bg-color)]"][_ngcontent-%COMP%]{transition:background-color .3s ease,border-color .3s ease}[_ngcontent-%COMP%]:focus{outline:2px solid var(--techwave-main-color);outline-offset:2px}'],changeDetection:0})}return e})();var No=(()=>{class e{THEME_KEY="ai-chat-theme";themeSignal=ut(this.getInitialTheme());theme=qe(()=>this.themeSignal());isDarkMode=qe(()=>{let t=this.themeSignal();return t===gt.System?window.matchMedia("(prefers-color-scheme: dark)").matches:t===gt.Dark});constructor(){wn(()=>{this.applyTheme(this.isDarkMode()),localStorage.setItem(this.THEME_KEY,this.themeSignal())}),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",t=>{this.themeSignal()===gt.System&&this.applyTheme(t.matches)})}setTheme(t){this.themeSignal.set(t)}toggleTheme(){let t=this.themeSignal();t===gt.Dark?this.themeSignal.set(gt.Light):t===gt.Light?this.themeSignal.set(gt.Dark):this.themeSignal.set(this.isDarkMode()?gt.Light:gt.Dark)}getInitialTheme(){let t=localStorage.getItem(this.THEME_KEY);return t&&Object.values(gt).includes(t)?t:gt.System}applyTheme(t){t?(document.documentElement.classList.add("dark"),this.setCSSVariables({"--techwave-bg-color":"#121212","--techwave-card-bg-color":"#1e1e1e","--techwave-heading-color":"#ffffff","--techwave-body-color":"#e0e0e0","--techwave-muted-color":"#a0a0a0","--techwave-border-color":"#333333","--techwave-primary-color":"#8b5cf6","--techwave-primary-hover-color":"#7c3aed","--techwave-user-msg-bg-color":"#1a1a2e","--techwave-assistant-msg-bg-color":"#1e1e2e","--techwave-system-msg-bg-color":"#2d2d2d","--techwave-input-bg-color":"#2d2d2d","--techwave-input-text-color":"#e0e0e0","--techwave-placeholder-color":"#a0a0a0","--techwave-code-bg-color":"#2d2d2d","--techwave-table-header-bg-color":"#2d2d2d","--techwave-table-header-text-color":"#ffffff","--techwave-table-border-color":"#444444","--techwave-table-row-alt-bg-color":"#1a1a1a","--techwave-table-row-hover-bg-color":"#333333"})):(document.documentElement.classList.remove("dark"),this.setCSSVariables({"--techwave-bg-color":"#f8f9fa","--techwave-card-bg-color":"#ffffff","--techwave-heading-color":"#111827","--techwave-body-color":"#374151","--techwave-muted-color":"#6b7280","--techwave-border-color":"#e5e7eb","--techwave-primary-color":"#8b5cf6","--techwave-primary-hover-color":"#7c3aed","--techwave-user-msg-bg-color":"#f3f4f6","--techwave-assistant-msg-bg-color":"#f8f9fa","--techwave-system-msg-bg-color":"#f3f4f6","--techwave-input-bg-color":"#ffffff","--techwave-input-text-color":"#374151","--techwave-placeholder-color":"#9ca3af","--techwave-code-bg-color":"#f3f4f6","--techwave-table-header-bg-color":"#f3f4f6","--techwave-table-header-text-color":"#111827","--techwave-table-border-color":"#e5e7eb","--techwave-table-row-alt-bg-color":"#f9fafb","--techwave-table-row-hover-bg-color":"#f3f4f6"}))}setCSSVariables(t){Object.entries(t).forEach(([r,o])=>{document.documentElement.style.setProperty(r,o)})}static \u0275fac=function(r){return new(r||e)};static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Jf(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}var xr=Jf();function eb(e){xr=e}var rs={exec:()=>null};function re(e,n=""){let t=typeof e=="string"?e:e.source,r={replace:(o,i)=>{let s=typeof i=="string"?i:i.source;return s=s.replace(Ze.caret,"$1"),t=t.replace(o,s),r},getRegex:()=>new RegExp(t,n)};return r}var Ze={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:e=>new RegExp(`^( {0,3}${e})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}#`),htmlBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}<(?:[a-z].*>|!--)`,"i")},_T=/^(?:[ \t]*(?:\n|$))+/,ET=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,xT=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,is=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,ST=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,Kf=/(?:[*+-]|\d{1,9}[.)])/,tb=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,nb=re(tb).replace(/bull/g,Kf).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),MT=re(tb).replace(/bull/g,Kf).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),Xf=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,IT=/^[^\n]+/,eh=/(?!\s*\])(?:\\.|[^\[\]\\])+/,TT=re(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",eh).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),kT=re(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Kf).getRegex(),Hl="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",th=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,AT=re("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",th).replace("tag",Hl).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),rb=re(Xf).replace("hr",is).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Hl).getRegex(),NT=re(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",rb).getRegex(),nh={blockquote:NT,code:ET,def:TT,fences:xT,heading:ST,hr:is,html:AT,lheading:nb,list:kT,newline:_T,paragraph:rb,table:rs,text:IT},Yy=re("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",is).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Hl).getRegex(),RT=I(b({},nh),{lheading:MT,table:Yy,paragraph:re(Xf).replace("hr",is).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Yy).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Hl).getRegex()}),OT=I(b({},nh),{html:re(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",th).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:rs,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:re(Xf).replace("hr",is).replace("heading",` *#{1,6} *[^
]`).replace("lheading",nb).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()}),PT=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,FT=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,ob=/^( {2,}|\\)\n(?!\s*$)/,LT=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,zl=/[\p{P}\p{S}]/u,rh=/[\s\p{P}\p{S}]/u,ib=/[^\s\p{P}\p{S}]/u,VT=re(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,rh).getRegex(),sb=/(?!~)[\p{P}\p{S}]/u,jT=/(?!~)[\s\p{P}\p{S}]/u,BT=/(?:[^\s\p{P}\p{S}]|~)/u,$T=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,ab=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,UT=re(ab,"u").replace(/punct/g,zl).getRegex(),HT=re(ab,"u").replace(/punct/g,sb).getRegex(),lb="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",zT=re(lb,"gu").replace(/notPunctSpace/g,ib).replace(/punctSpace/g,rh).replace(/punct/g,zl).getRegex(),GT=re(lb,"gu").replace(/notPunctSpace/g,BT).replace(/punctSpace/g,jT).replace(/punct/g,sb).getRegex(),qT=re("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,ib).replace(/punctSpace/g,rh).replace(/punct/g,zl).getRegex(),WT=re(/\\(punct)/,"gu").replace(/punct/g,zl).getRegex(),ZT=re(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),YT=re(th).replace("(?:-->|$)","-->").getRegex(),QT=re("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",YT).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Ul=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,JT=re(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",Ul).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),cb=re(/^!?\[(label)\]\[(ref)\]/).replace("label",Ul).replace("ref",eh).getRegex(),ub=re(/^!?\[(ref)\](?:\[\])?/).replace("ref",eh).getRegex(),KT=re("reflink|nolink(?!\\()","g").replace("reflink",cb).replace("nolink",ub).getRegex(),oh={_backpedal:rs,anyPunctuation:WT,autolink:ZT,blockSkip:$T,br:ob,code:FT,del:rs,emStrongLDelim:UT,emStrongRDelimAst:zT,emStrongRDelimUnd:qT,escape:PT,link:JT,nolink:ub,punctuation:VT,reflink:cb,reflinkSearch:KT,tag:QT,text:LT,url:rs},XT=I(b({},oh),{link:re(/^!?\[(label)\]\((.*?)\)/).replace("label",Ul).getRegex(),reflink:re(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Ul).getRegex()}),Yf=I(b({},oh),{emStrongRDelimAst:GT,emStrongLDelim:HT,url:re(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/}),ek=I(b({},Yf),{br:re(ob).replace("{2,}","*").getRegex(),text:re(Yf.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()}),$l={normal:nh,gfm:RT,pedantic:OT},ts={normal:oh,gfm:Yf,breaks:ek,pedantic:XT},tk={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Qy=e=>tk[e];function Qt(e,n){if(n){if(Ze.escapeTest.test(e))return e.replace(Ze.escapeReplace,Qy)}else if(Ze.escapeTestNoEncode.test(e))return e.replace(Ze.escapeReplaceNoEncode,Qy);return e}function Jy(e){try{e=encodeURI(e).replace(Ze.percentDecode,"%")}catch{return null}return e}function Ky(e,n){let t=e.replace(Ze.findPipe,(i,s,a)=>{let l=!1,c=s;for(;--c>=0&&a[c]==="\\";)l=!l;return l?"|":" |"}),r=t.split(Ze.splitPipe),o=0;if(r[0].trim()||r.shift(),r.length>0&&!r.at(-1)?.trim()&&r.pop(),n)if(r.length>n)r.splice(n);else for(;r.length<n;)r.push("");for(;o<r.length;o++)r[o]=r[o].trim().replace(Ze.slashPipe,"|");return r}function ns(e,n,t){let r=e.length;if(r===0)return"";let o=0;for(;o<r&&e.charAt(r-o-1)===n;)o++;return e.slice(0,r-o)}function nk(e,n){if(e.indexOf(n[1])===-1)return-1;let t=0;for(let r=0;r<e.length;r++)if(e[r]==="\\")r++;else if(e[r]===n[0])t++;else if(e[r]===n[1]&&(t--,t<0))return r;return-1}function Xy(e,n,t,r,o){let i=n.href,s=n.title||null,a=e[1].replace(o.other.outputLinkReplace,"$1");if(e[0].charAt(0)!=="!"){r.state.inLink=!0;let l={type:"link",raw:t,href:i,title:s,text:a,tokens:r.inlineTokens(a)};return r.state.inLink=!1,l}return{type:"image",raw:t,href:i,title:s,text:a}}function rk(e,n,t){let r=e.match(t.other.indentCodeCompensation);if(r===null)return n;let o=r[1];return n.split(`
`).map(i=>{let s=i.match(t.other.beginningSpace);if(s===null)return i;let[a]=s;return a.length>=o.length?i.slice(o.length):i}).join(`
`)}var Oo=class{options;rules;lexer;constructor(n){this.options=n||xr}space(n){let t=this.rules.block.newline.exec(n);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(n){let t=this.rules.block.code.exec(n);if(t){let r=t[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?r:ns(r,`
`)}}}fences(n){let t=this.rules.block.fences.exec(n);if(t){let r=t[0],o=rk(r,t[3]||"",this.rules);return{type:"code",raw:r,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:o}}}heading(n){let t=this.rules.block.heading.exec(n);if(t){let r=t[2].trim();if(this.rules.other.endingHash.test(r)){let o=ns(r,"#");(this.options.pedantic||!o||this.rules.other.endingSpaceChar.test(o))&&(r=o.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:r,tokens:this.lexer.inline(r)}}}hr(n){let t=this.rules.block.hr.exec(n);if(t)return{type:"hr",raw:ns(t[0],`
`)}}blockquote(n){let t=this.rules.block.blockquote.exec(n);if(t){let r=ns(t[0],`
`).split(`
`),o="",i="",s=[];for(;r.length>0;){let a=!1,l=[],c;for(c=0;c<r.length;c++)if(this.rules.other.blockquoteStart.test(r[c]))l.push(r[c]),a=!0;else if(!a)l.push(r[c]);else break;r=r.slice(c);let u=l.join(`
`),h=u.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");o=o?`${o}
${u}`:u,i=i?`${i}
${h}`:h;let m=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(h,s,!0),this.lexer.state.top=m,r.length===0)break;let g=s.at(-1);if(g?.type==="code")break;if(g?.type==="blockquote"){let v=g,y=v.raw+`
`+r.join(`
`),_=this.blockquote(y);s[s.length-1]=_,o=o.substring(0,o.length-v.raw.length)+_.raw,i=i.substring(0,i.length-v.text.length)+_.text;break}else if(g?.type==="list"){let v=g,y=v.raw+`
`+r.join(`
`),_=this.list(y);s[s.length-1]=_,o=o.substring(0,o.length-g.raw.length)+_.raw,i=i.substring(0,i.length-v.raw.length)+_.raw,r=y.substring(s.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:o,tokens:s,text:i}}}list(n){let t=this.rules.block.list.exec(n);if(t){let r=t[1].trim(),o=r.length>1,i={type:"list",raw:"",ordered:o,start:o?+r.slice(0,-1):"",loose:!1,items:[]};r=o?`\\d{1,9}\\${r.slice(-1)}`:`\\${r}`,this.options.pedantic&&(r=o?r:"[*+-]");let s=this.rules.other.listItemRegex(r),a=!1;for(;n;){let c=!1,u="",h="";if(!(t=s.exec(n))||this.rules.block.hr.test(n))break;u=t[0],n=n.substring(u.length);let m=t[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,ae=>" ".repeat(3*ae.length)),g=n.split(`
`,1)[0],v=!m.trim(),y=0;if(this.options.pedantic?(y=2,h=m.trimStart()):v?y=t[1].length+1:(y=t[2].search(this.rules.other.nonSpaceChar),y=y>4?1:y,h=m.slice(y),y+=t[1].length),v&&this.rules.other.blankLine.test(g)&&(u+=g+`
`,n=n.substring(g.length+1),c=!0),!c){let ae=this.rules.other.nextBulletRegex(y),oe=this.rules.other.hrRegex(y),wt=this.rules.other.fencesBeginRegex(y),Lt=this.rules.other.headingBeginRegex(y),Wn=this.rules.other.htmlBeginRegex(y);for(;n;){let Sr=n.split(`
`,1)[0],Vo;if(g=Sr,this.options.pedantic?(g=g.replace(this.rules.other.listReplaceNesting,"  "),Vo=g):Vo=g.replace(this.rules.other.tabCharGlobal,"    "),wt.test(g)||Lt.test(g)||Wn.test(g)||ae.test(g)||oe.test(g))break;if(Vo.search(this.rules.other.nonSpaceChar)>=y||!g.trim())h+=`
`+Vo.slice(y);else{if(v||m.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||wt.test(m)||Lt.test(m)||oe.test(m))break;h+=`
`+g}!v&&!g.trim()&&(v=!0),u+=Sr+`
`,n=n.substring(Sr.length+1),m=Vo.slice(y)}}i.loose||(a?i.loose=!0:this.rules.other.doubleBlankLine.test(u)&&(a=!0));let _=null,A;this.options.gfm&&(_=this.rules.other.listIsTask.exec(h),_&&(A=_[0]!=="[ ] ",h=h.replace(this.rules.other.listReplaceTask,""))),i.items.push({type:"list_item",raw:u,task:!!_,checked:A,loose:!1,text:h,tokens:[]}),i.raw+=u}let l=i.items.at(-1);if(l)l.raw=l.raw.trimEnd(),l.text=l.text.trimEnd();else return;i.raw=i.raw.trimEnd();for(let c=0;c<i.items.length;c++)if(this.lexer.state.top=!1,i.items[c].tokens=this.lexer.blockTokens(i.items[c].text,[]),!i.loose){let u=i.items[c].tokens.filter(m=>m.type==="space"),h=u.length>0&&u.some(m=>this.rules.other.anyLine.test(m.raw));i.loose=h}if(i.loose)for(let c=0;c<i.items.length;c++)i.items[c].loose=!0;return i}}html(n){let t=this.rules.block.html.exec(n);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(n){let t=this.rules.block.def.exec(n);if(t){let r=t[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),o=t[2]?t[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",i=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:r,raw:t[0],href:o,title:i}}}table(n){let t=this.rules.block.table.exec(n);if(!t||!this.rules.other.tableDelimiter.test(t[2]))return;let r=Ky(t[1]),o=t[2].replace(this.rules.other.tableAlignChars,"").split("|"),i=t[3]?.trim()?t[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],s={type:"table",raw:t[0],header:[],align:[],rows:[]};if(r.length===o.length){for(let a of o)this.rules.other.tableAlignRight.test(a)?s.align.push("right"):this.rules.other.tableAlignCenter.test(a)?s.align.push("center"):this.rules.other.tableAlignLeft.test(a)?s.align.push("left"):s.align.push(null);for(let a=0;a<r.length;a++)s.header.push({text:r[a],tokens:this.lexer.inline(r[a]),header:!0,align:s.align[a]});for(let a of i)s.rows.push(Ky(a,s.header.length).map((l,c)=>({text:l,tokens:this.lexer.inline(l),header:!1,align:s.align[c]})));return s}}lheading(n){let t=this.rules.block.lheading.exec(n);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(n){let t=this.rules.block.paragraph.exec(n);if(t){let r=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:r,tokens:this.lexer.inline(r)}}}text(n){let t=this.rules.block.text.exec(n);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(n){let t=this.rules.inline.escape.exec(n);if(t)return{type:"escape",raw:t[0],text:t[1]}}tag(n){let t=this.rules.inline.tag.exec(n);if(t)return!this.lexer.state.inLink&&this.rules.other.startATag.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(n){let t=this.rules.inline.link.exec(n);if(t){let r=t[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(r)){if(!this.rules.other.endAngleBracket.test(r))return;let s=ns(r.slice(0,-1),"\\");if((r.length-s.length)%2===0)return}else{let s=nk(t[2],"()");if(s>-1){let l=(t[0].indexOf("!")===0?5:4)+t[1].length+s;t[2]=t[2].substring(0,s),t[0]=t[0].substring(0,l).trim(),t[3]=""}}let o=t[2],i="";if(this.options.pedantic){let s=this.rules.other.pedanticHrefTitle.exec(o);s&&(o=s[1],i=s[3])}else i=t[3]?t[3].slice(1,-1):"";return o=o.trim(),this.rules.other.startAngleBracket.test(o)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(r)?o=o.slice(1):o=o.slice(1,-1)),Xy(t,{href:o&&o.replace(this.rules.inline.anyPunctuation,"$1"),title:i&&i.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer,this.rules)}}reflink(n,t){let r;if((r=this.rules.inline.reflink.exec(n))||(r=this.rules.inline.nolink.exec(n))){let o=(r[2]||r[1]).replace(this.rules.other.multipleSpaceGlobal," "),i=t[o.toLowerCase()];if(!i){let s=r[0].charAt(0);return{type:"text",raw:s,text:s}}return Xy(r,i,r[0],this.lexer,this.rules)}}emStrong(n,t,r=""){let o=this.rules.inline.emStrongLDelim.exec(n);if(!o||o[3]&&r.match(this.rules.other.unicodeAlphaNumeric))return;if(!(o[1]||o[2]||"")||!r||this.rules.inline.punctuation.exec(r)){let s=[...o[0]].length-1,a,l,c=s,u=0,h=o[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(h.lastIndex=0,t=t.slice(-1*n.length+s);(o=h.exec(t))!=null;){if(a=o[1]||o[2]||o[3]||o[4]||o[5]||o[6],!a)continue;if(l=[...a].length,o[3]||o[4]){c+=l;continue}else if((o[5]||o[6])&&s%3&&!((s+l)%3)){u+=l;continue}if(c-=l,c>0)continue;l=Math.min(l,l+c+u);let m=[...o[0]][0].length,g=n.slice(0,s+o.index+m+l);if(Math.min(s,l)%2){let y=g.slice(1,-1);return{type:"em",raw:g,text:y,tokens:this.lexer.inlineTokens(y)}}let v=g.slice(2,-2);return{type:"strong",raw:g,text:v,tokens:this.lexer.inlineTokens(v)}}}}codespan(n){let t=this.rules.inline.code.exec(n);if(t){let r=t[2].replace(this.rules.other.newLineCharGlobal," "),o=this.rules.other.nonSpaceChar.test(r),i=this.rules.other.startingSpaceChar.test(r)&&this.rules.other.endingSpaceChar.test(r);return o&&i&&(r=r.substring(1,r.length-1)),{type:"codespan",raw:t[0],text:r}}}br(n){let t=this.rules.inline.br.exec(n);if(t)return{type:"br",raw:t[0]}}del(n){let t=this.rules.inline.del.exec(n);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(n){let t=this.rules.inline.autolink.exec(n);if(t){let r,o;return t[2]==="@"?(r=t[1],o="mailto:"+r):(r=t[1],o=r),{type:"link",raw:t[0],text:r,href:o,tokens:[{type:"text",raw:r,text:r}]}}}url(n){let t;if(t=this.rules.inline.url.exec(n)){let r,o;if(t[2]==="@")r=t[0],o="mailto:"+r;else{let i;do i=t[0],t[0]=this.rules.inline._backpedal.exec(t[0])?.[0]??"";while(i!==t[0]);r=t[0],t[1]==="www."?o="http://"+t[0]:o=t[0]}return{type:"link",raw:t[0],text:r,href:o,tokens:[{type:"text",raw:r,text:r}]}}}inlineText(n){let t=this.rules.inline.text.exec(n);if(t){let r=this.lexer.state.inRawBlock;return{type:"text",raw:t[0],text:t[0],escaped:r}}}},Pt=class e{tokens;options;state;tokenizer;inlineQueue;constructor(n){this.tokens=[],this.tokens.links=Object.create(null),this.options=n||xr,this.options.tokenizer=this.options.tokenizer||new Oo,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};let t={other:Ze,block:$l.normal,inline:ts.normal};this.options.pedantic?(t.block=$l.pedantic,t.inline=ts.pedantic):this.options.gfm&&(t.block=$l.gfm,this.options.breaks?t.inline=ts.breaks:t.inline=ts.gfm),this.tokenizer.rules=t}static get rules(){return{block:$l,inline:ts}}static lex(n,t){return new e(t).lex(n)}static lexInline(n,t){return new e(t).inlineTokens(n)}lex(n){n=n.replace(Ze.carriageReturn,`
`),this.blockTokens(n,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){let r=this.inlineQueue[t];this.inlineTokens(r.src,r.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(n,t=[],r=!1){for(this.options.pedantic&&(n=n.replace(Ze.tabCharGlobal,"    ").replace(Ze.spaceLine,""));n;){let o;if(this.options.extensions?.block?.some(s=>(o=s.call({lexer:this},n,t))?(n=n.substring(o.raw.length),t.push(o),!0):!1))continue;if(o=this.tokenizer.space(n)){n=n.substring(o.raw.length);let s=t.at(-1);o.raw.length===1&&s!==void 0?s.raw+=`
`:t.push(o);continue}if(o=this.tokenizer.code(n)){n=n.substring(o.raw.length);let s=t.at(-1);s?.type==="paragraph"||s?.type==="text"?(s.raw+=`
`+o.raw,s.text+=`
`+o.text,this.inlineQueue.at(-1).src=s.text):t.push(o);continue}if(o=this.tokenizer.fences(n)){n=n.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.heading(n)){n=n.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.hr(n)){n=n.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.blockquote(n)){n=n.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.list(n)){n=n.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.html(n)){n=n.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.def(n)){n=n.substring(o.raw.length);let s=t.at(-1);s?.type==="paragraph"||s?.type==="text"?(s.raw+=`
`+o.raw,s.text+=`
`+o.raw,this.inlineQueue.at(-1).src=s.text):this.tokens.links[o.tag]||(this.tokens.links[o.tag]={href:o.href,title:o.title});continue}if(o=this.tokenizer.table(n)){n=n.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.lheading(n)){n=n.substring(o.raw.length),t.push(o);continue}let i=n;if(this.options.extensions?.startBlock){let s=1/0,a=n.slice(1),l;this.options.extensions.startBlock.forEach(c=>{l=c.call({lexer:this},a),typeof l=="number"&&l>=0&&(s=Math.min(s,l))}),s<1/0&&s>=0&&(i=n.substring(0,s+1))}if(this.state.top&&(o=this.tokenizer.paragraph(i))){let s=t.at(-1);r&&s?.type==="paragraph"?(s.raw+=`
`+o.raw,s.text+=`
`+o.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=s.text):t.push(o),r=i.length!==n.length,n=n.substring(o.raw.length);continue}if(o=this.tokenizer.text(n)){n=n.substring(o.raw.length);let s=t.at(-1);s?.type==="text"?(s.raw+=`
`+o.raw,s.text+=`
`+o.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=s.text):t.push(o);continue}if(n){let s="Infinite loop on byte: "+n.charCodeAt(0);if(this.options.silent){console.error(s);break}else throw new Error(s)}}return this.state.top=!0,t}inline(n,t=[]){return this.inlineQueue.push({src:n,tokens:t}),t}inlineTokens(n,t=[]){let r=n,o=null;if(this.tokens.links){let a=Object.keys(this.tokens.links);if(a.length>0)for(;(o=this.tokenizer.rules.inline.reflinkSearch.exec(r))!=null;)a.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(r=r.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+r.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(o=this.tokenizer.rules.inline.blockSkip.exec(r))!=null;)r=r.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+r.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(o=this.tokenizer.rules.inline.anyPunctuation.exec(r))!=null;)r=r.slice(0,o.index)+"++"+r.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);let i=!1,s="";for(;n;){i||(s=""),i=!1;let a;if(this.options.extensions?.inline?.some(c=>(a=c.call({lexer:this},n,t))?(n=n.substring(a.raw.length),t.push(a),!0):!1))continue;if(a=this.tokenizer.escape(n)){n=n.substring(a.raw.length),t.push(a);continue}if(a=this.tokenizer.tag(n)){n=n.substring(a.raw.length),t.push(a);continue}if(a=this.tokenizer.link(n)){n=n.substring(a.raw.length),t.push(a);continue}if(a=this.tokenizer.reflink(n,this.tokens.links)){n=n.substring(a.raw.length);let c=t.at(-1);a.type==="text"&&c?.type==="text"?(c.raw+=a.raw,c.text+=a.text):t.push(a);continue}if(a=this.tokenizer.emStrong(n,r,s)){n=n.substring(a.raw.length),t.push(a);continue}if(a=this.tokenizer.codespan(n)){n=n.substring(a.raw.length),t.push(a);continue}if(a=this.tokenizer.br(n)){n=n.substring(a.raw.length),t.push(a);continue}if(a=this.tokenizer.del(n)){n=n.substring(a.raw.length),t.push(a);continue}if(a=this.tokenizer.autolink(n)){n=n.substring(a.raw.length),t.push(a);continue}if(!this.state.inLink&&(a=this.tokenizer.url(n))){n=n.substring(a.raw.length),t.push(a);continue}let l=n;if(this.options.extensions?.startInline){let c=1/0,u=n.slice(1),h;this.options.extensions.startInline.forEach(m=>{h=m.call({lexer:this},u),typeof h=="number"&&h>=0&&(c=Math.min(c,h))}),c<1/0&&c>=0&&(l=n.substring(0,c+1))}if(a=this.tokenizer.inlineText(l)){n=n.substring(a.raw.length),a.raw.slice(-1)!=="_"&&(s=a.raw.slice(-1)),i=!0;let c=t.at(-1);c?.type==="text"?(c.raw+=a.raw,c.text+=a.text):t.push(a);continue}if(n){let c="Infinite loop on byte: "+n.charCodeAt(0);if(this.options.silent){console.error(c);break}else throw new Error(c)}}return t}},Po=class{options;parser;constructor(n){this.options=n||xr}space(n){return""}code({text:n,lang:t,escaped:r}){let o=(t||"").match(Ze.notSpaceStart)?.[0],i=n.replace(Ze.endingNewline,"")+`
`;return o?'<pre><code class="language-'+Qt(o)+'">'+(r?i:Qt(i,!0))+`</code></pre>
`:"<pre><code>"+(r?i:Qt(i,!0))+`</code></pre>
`}blockquote({tokens:n}){return`<blockquote>
${this.parser.parse(n)}</blockquote>
`}html({text:n}){return n}heading({tokens:n,depth:t}){return`<h${t}>${this.parser.parseInline(n)}</h${t}>
`}hr(n){return`<hr>
`}list(n){let t=n.ordered,r=n.start,o="";for(let a=0;a<n.items.length;a++){let l=n.items[a];o+=this.listitem(l)}let i=t?"ol":"ul",s=t&&r!==1?' start="'+r+'"':"";return"<"+i+s+`>
`+o+"</"+i+`>
`}listitem(n){let t="";if(n.task){let r=this.checkbox({checked:!!n.checked});n.loose?n.tokens[0]?.type==="paragraph"?(n.tokens[0].text=r+" "+n.tokens[0].text,n.tokens[0].tokens&&n.tokens[0].tokens.length>0&&n.tokens[0].tokens[0].type==="text"&&(n.tokens[0].tokens[0].text=r+" "+Qt(n.tokens[0].tokens[0].text),n.tokens[0].tokens[0].escaped=!0)):n.tokens.unshift({type:"text",raw:r+" ",text:r+" ",escaped:!0}):t+=r+" "}return t+=this.parser.parse(n.tokens,!!n.loose),`<li>${t}</li>
`}checkbox({checked:n}){return"<input "+(n?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:n}){return`<p>${this.parser.parseInline(n)}</p>
`}table(n){let t="",r="";for(let i=0;i<n.header.length;i++)r+=this.tablecell(n.header[i]);t+=this.tablerow({text:r});let o="";for(let i=0;i<n.rows.length;i++){let s=n.rows[i];r="";for(let a=0;a<s.length;a++)r+=this.tablecell(s[a]);o+=this.tablerow({text:r})}return o&&(o=`<tbody>${o}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+o+`</table>
`}tablerow({text:n}){return`<tr>
${n}</tr>
`}tablecell(n){let t=this.parser.parseInline(n.tokens),r=n.header?"th":"td";return(n.align?`<${r} align="${n.align}">`:`<${r}>`)+t+`</${r}>
`}strong({tokens:n}){return`<strong>${this.parser.parseInline(n)}</strong>`}em({tokens:n}){return`<em>${this.parser.parseInline(n)}</em>`}codespan({text:n}){return`<code>${Qt(n,!0)}</code>`}br(n){return"<br>"}del({tokens:n}){return`<del>${this.parser.parseInline(n)}</del>`}link({href:n,title:t,tokens:r}){let o=this.parser.parseInline(r),i=Jy(n);if(i===null)return o;n=i;let s='<a href="'+n+'"';return t&&(s+=' title="'+Qt(t)+'"'),s+=">"+o+"</a>",s}image({href:n,title:t,text:r}){let o=Jy(n);if(o===null)return Qt(r);n=o;let i=`<img src="${n}" alt="${r}"`;return t&&(i+=` title="${Qt(t)}"`),i+=">",i}text(n){return"tokens"in n&&n.tokens?this.parser.parseInline(n.tokens):"escaped"in n&&n.escaped?n.text:Qt(n.text)}},os=class{strong({text:n}){return n}em({text:n}){return n}codespan({text:n}){return n}del({text:n}){return n}html({text:n}){return n}text({text:n}){return n}link({text:n}){return""+n}image({text:n}){return""+n}br(){return""}},Ft=class e{options;renderer;textRenderer;constructor(n){this.options=n||xr,this.options.renderer=this.options.renderer||new Po,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new os}static parse(n,t){return new e(t).parse(n)}static parseInline(n,t){return new e(t).parseInline(n)}parse(n,t=!0){let r="";for(let o=0;o<n.length;o++){let i=n[o];if(this.options.extensions?.renderers?.[i.type]){let a=i,l=this.options.extensions.renderers[a.type].call({parser:this},a);if(l!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(a.type)){r+=l||"";continue}}let s=i;switch(s.type){case"space":{r+=this.renderer.space(s);continue}case"hr":{r+=this.renderer.hr(s);continue}case"heading":{r+=this.renderer.heading(s);continue}case"code":{r+=this.renderer.code(s);continue}case"table":{r+=this.renderer.table(s);continue}case"blockquote":{r+=this.renderer.blockquote(s);continue}case"list":{r+=this.renderer.list(s);continue}case"html":{r+=this.renderer.html(s);continue}case"paragraph":{r+=this.renderer.paragraph(s);continue}case"text":{let a=s,l=this.renderer.text(a);for(;o+1<n.length&&n[o+1].type==="text";)a=n[++o],l+=`
`+this.renderer.text(a);t?r+=this.renderer.paragraph({type:"paragraph",raw:l,text:l,tokens:[{type:"text",raw:l,text:l,escaped:!0}]}):r+=l;continue}default:{let a='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(a),"";throw new Error(a)}}}return r}parseInline(n,t=this.renderer){let r="";for(let o=0;o<n.length;o++){let i=n[o];if(this.options.extensions?.renderers?.[i.type]){let a=this.options.extensions.renderers[i.type].call({parser:this},i);if(a!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(i.type)){r+=a||"";continue}}let s=i;switch(s.type){case"escape":{r+=t.text(s);break}case"html":{r+=t.html(s);break}case"link":{r+=t.link(s);break}case"image":{r+=t.image(s);break}case"strong":{r+=t.strong(s);break}case"em":{r+=t.em(s);break}case"codespan":{r+=t.codespan(s);break}case"br":{r+=t.br(s);break}case"del":{r+=t.del(s);break}case"text":{r+=t.text(s);break}default:{let a='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(a),"";throw new Error(a)}}}return r}},Ro=class{options;block;constructor(n){this.options=n||xr}static passThroughHooks=new Set(["preprocess","postprocess","processAllTokens"]);preprocess(n){return n}postprocess(n){return n}processAllTokens(n){return n}provideLexer(){return this.block?Pt.lex:Pt.lexInline}provideParser(){return this.block?Ft.parse:Ft.parseInline}},Qf=class{defaults=Jf();options=this.setOptions;parse=this.parseMarkdown(!0);parseInline=this.parseMarkdown(!1);Parser=Ft;Renderer=Po;TextRenderer=os;Lexer=Pt;Tokenizer=Oo;Hooks=Ro;constructor(...n){this.use(...n)}walkTokens(n,t){let r=[];for(let o of n)switch(r=r.concat(t.call(this,o)),o.type){case"table":{let i=o;for(let s of i.header)r=r.concat(this.walkTokens(s.tokens,t));for(let s of i.rows)for(let a of s)r=r.concat(this.walkTokens(a.tokens,t));break}case"list":{let i=o;r=r.concat(this.walkTokens(i.items,t));break}default:{let i=o;this.defaults.extensions?.childTokens?.[i.type]?this.defaults.extensions.childTokens[i.type].forEach(s=>{let a=i[s].flat(1/0);r=r.concat(this.walkTokens(a,t))}):i.tokens&&(r=r.concat(this.walkTokens(i.tokens,t)))}}return r}use(...n){let t=this.defaults.extensions||{renderers:{},childTokens:{}};return n.forEach(r=>{let o=b({},r);if(o.async=this.defaults.async||o.async||!1,r.extensions&&(r.extensions.forEach(i=>{if(!i.name)throw new Error("extension name required");if("renderer"in i){let s=t.renderers[i.name];s?t.renderers[i.name]=function(...a){let l=i.renderer.apply(this,a);return l===!1&&(l=s.apply(this,a)),l}:t.renderers[i.name]=i.renderer}if("tokenizer"in i){if(!i.level||i.level!=="block"&&i.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");let s=t[i.level];s?s.unshift(i.tokenizer):t[i.level]=[i.tokenizer],i.start&&(i.level==="block"?t.startBlock?t.startBlock.push(i.start):t.startBlock=[i.start]:i.level==="inline"&&(t.startInline?t.startInline.push(i.start):t.startInline=[i.start]))}"childTokens"in i&&i.childTokens&&(t.childTokens[i.name]=i.childTokens)}),o.extensions=t),r.renderer){let i=this.defaults.renderer||new Po(this.defaults);for(let s in r.renderer){if(!(s in i))throw new Error(`renderer '${s}' does not exist`);if(["options","parser"].includes(s))continue;let a=s,l=r.renderer[a],c=i[a];i[a]=(...u)=>{let h=l.apply(i,u);return h===!1&&(h=c.apply(i,u)),h||""}}o.renderer=i}if(r.tokenizer){let i=this.defaults.tokenizer||new Oo(this.defaults);for(let s in r.tokenizer){if(!(s in i))throw new Error(`tokenizer '${s}' does not exist`);if(["options","rules","lexer"].includes(s))continue;let a=s,l=r.tokenizer[a],c=i[a];i[a]=(...u)=>{let h=l.apply(i,u);return h===!1&&(h=c.apply(i,u)),h}}o.tokenizer=i}if(r.hooks){let i=this.defaults.hooks||new Ro;for(let s in r.hooks){if(!(s in i))throw new Error(`hook '${s}' does not exist`);if(["options","block"].includes(s))continue;let a=s,l=r.hooks[a],c=i[a];Ro.passThroughHooks.has(s)?i[a]=u=>{if(this.defaults.async)return Promise.resolve(l.call(i,u)).then(m=>c.call(i,m));let h=l.call(i,u);return c.call(i,h)}:i[a]=(...u)=>{let h=l.apply(i,u);return h===!1&&(h=c.apply(i,u)),h}}o.hooks=i}if(r.walkTokens){let i=this.defaults.walkTokens,s=r.walkTokens;o.walkTokens=function(a){let l=[];return l.push(s.call(this,a)),i&&(l=l.concat(i.call(this,a))),l}}this.defaults=b(b({},this.defaults),o)}),this}setOptions(n){return this.defaults=b(b({},this.defaults),n),this}lexer(n,t){return Pt.lex(n,t??this.defaults)}parser(n,t){return Ft.parse(n,t??this.defaults)}parseMarkdown(n){return(r,o)=>{let i=b({},o),s=b(b({},this.defaults),i),a=this.onError(!!s.silent,!!s.async);if(this.defaults.async===!0&&i.async===!1)return a(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof r>"u"||r===null)return a(new Error("marked(): input parameter is undefined or null"));if(typeof r!="string")return a(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(r)+", string expected"));s.hooks&&(s.hooks.options=s,s.hooks.block=n);let l=s.hooks?s.hooks.provideLexer():n?Pt.lex:Pt.lexInline,c=s.hooks?s.hooks.provideParser():n?Ft.parse:Ft.parseInline;if(s.async)return Promise.resolve(s.hooks?s.hooks.preprocess(r):r).then(u=>l(u,s)).then(u=>s.hooks?s.hooks.processAllTokens(u):u).then(u=>s.walkTokens?Promise.all(this.walkTokens(u,s.walkTokens)).then(()=>u):u).then(u=>c(u,s)).then(u=>s.hooks?s.hooks.postprocess(u):u).catch(a);try{s.hooks&&(r=s.hooks.preprocess(r));let u=l(r,s);s.hooks&&(u=s.hooks.processAllTokens(u)),s.walkTokens&&this.walkTokens(u,s.walkTokens);let h=c(u,s);return s.hooks&&(h=s.hooks.postprocess(h)),h}catch(u){return a(u)}}}onError(n,t){return r=>{if(r.message+=`
Please report this to https://github.com/markedjs/marked.`,n){let o="<p>An error occurred:</p><pre>"+Qt(r.message+"",!0)+"</pre>";return t?Promise.resolve(o):o}if(t)return Promise.reject(r);throw r}}},Er=new Qf;function ee(e,n){return Er.parse(e,n)}ee.options=ee.setOptions=function(e){return Er.setOptions(e),ee.defaults=Er.defaults,eb(ee.defaults),ee};ee.getDefaults=Jf;ee.defaults=xr;ee.use=function(...e){return Er.use(...e),ee.defaults=Er.defaults,eb(ee.defaults),ee};ee.walkTokens=function(e,n){return Er.walkTokens(e,n)};ee.parseInline=Er.parseInline;ee.Parser=Ft;ee.parser=Ft.parse;ee.Renderer=Po;ee.TextRenderer=os;ee.Lexer=Pt;ee.lexer=Pt.lex;ee.Tokenizer=Oo;ee.Hooks=Ro;ee.parse=ee;var FV=ee.options,LV=ee.setOptions,VV=ee.use,jV=ee.walkTokens,BV=ee.parseInline;var $V=Ft.parse,UV=Pt.lex;var ok=(e,n)=>n.original;function ik(e,n){if(e&1){let t=Ve();d(0,"div",2)(1,"button",4),V("click",function(){let o=Q(t).$implicit,i=N(2);return J(i.openModal(o.original))})("keydown.enter",function(){let o=Q(t).$implicit,i=N(2);return J(i.openModal(o.original))})("keydown.space",function(){let o=Q(t).$implicit,i=N(2);return J(i.openModal(o.original))}),p(2),f()()}if(e&2){let t=n.$implicit;C(),te("title",t.title),Gt("aria-label","View source document: "+t.title),C(),ke(" ",t.displayLabel," ")}}function sk(e,n){if(e&1&&(d(0,"div",20)(1,"span",21),p(2),f(),d(3,"span"),p(4),f()()),e&2){let t=n.$implicit,r=N(4);C(2),ke("",t,":"),C(2),fe(r.selectedDocument.formattedMetadata[t])}}function ak(e,n){if(e&1&&(d(0,"div",13)(1,"h4",15),p(2,"Document Metadata"),f(),d(3,"div",19),qt(4,sk,5,2,"div",20,Pa),f()()),e&2){let t=N(3);C(4),Wt(t.metadataKeys)}}function lk(e,n){if(e&1){let t=Ve();d(0,"div",5),V("click",function(){Q(t);let o=N(2);return J(o.closeModal())})("keydown.escape",function(){Q(t);let o=N(2);return J(o.closeModal())}),d(1,"div",6),V("click",function(o){return Q(t),J(o.stopPropagation())})("keydown",function(o){return Q(t),J(o.stopPropagation())}),d(2,"div",7)(3,"h3",8),p(4),f(),d(5,"button",9),V("click",function(){Q(t);let o=N(2);return J(o.closeModal())})("keydown.enter",function(){Q(t);let o=N(2);return J(o.closeModal())}),k(),d(6,"svg",10),D(7,"path",11),f()()(),P(),d(8,"div",12),W(9,ak,6,0,"div",13),d(10,"div",14)(11,"h4",15),p(12,"Content"),f(),d(13,"p",16),p(14),f()()(),d(15,"div",17)(16,"button",18),V("click",function(){Q(t);let o=N(2);return J(o.closeModal())})("keydown.enter",function(){Q(t);let o=N(2);return J(o.closeModal())})("keydown.space",function(){Q(t);let o=N(2);return J(o.closeModal())}),p(17," Close "),f()()()()}if(e&2){let t=N(2);C(4),fe(t.selectedDocument.source),C(),Gt("aria-label","Close modal"),C(4),O(t.selectedDocument.formattedMetadata&&t.metadataKeys.length>0?9:-1),C(5),fe(t.selectedDocument.pageContent)}}function ck(e,n){if(e&1&&(d(0,"div",0)(1,"div",1),qt(2,ik,3,3,"div",2,ok),f(),W(4,lk,18,4,"div",3),f()),e&2){let t=N();C(2),Wt(t.sourceDocumentsWithProps),C(2),O(t.selectedDocument?4:-1)}}var db=(()=>{class e{chatService;cdr;sourceDocuments=[];sourceDocumentsWithProps=[];selectedDocument=null;selectedDocumentTitle="";selectedDocumentAuthor="";selectedDocumentSource="";selectedDocumentProducer="";selectedDocumentCreationDate="";selectedDocumentMetadata=null;selectedDocumentLineRange=null;hasMetadata=!1;hasLineRange=!1;metadataKeys=[];subscription=new ie;constructor(t,r){this.chatService=t,this.cdr=r}ngOnInit(){this.subscription.add(this.chatService.sourceDocuments$.subscribe(r=>{r&&r.length>0&&(console.log("Received source documents:",r),this.sourceDocuments=r,this.prepareDocumentData(),this.cdr.markForCheck())}));let t=this.chatService.currentChat();t&&t.sourceDocuments&&t.sourceDocuments.length>0&&(console.log("Loaded source documents from current chat:",t.sourceDocuments),this.sourceDocuments=[...t.sourceDocuments],this.prepareDocumentData(),this.cdr.markForCheck())}ngOnDestroy(){this.subscription.unsubscribe()}openModal(t){console.log("Opening modal for document:",t);let r=this.getFullTitle(t),o=this.getAuthor(t),i=this.getSource(t),s=this.getProducer(t),a=this.getCreationDate(t),l=this.getLineRange(t),c=this.extractFormattedMetadata(t);this.metadataKeys=Object.keys(c),this.selectedDocument={pageContent:t.pageContent,metadata:t.metadata||{},title:r,author:o,source:i,producer:s||"",creationDate:a||"",lineRange:l,formattedMetadata:c},this.selectedDocumentTitle=r,this.selectedDocumentAuthor=o,this.selectedDocumentSource=i,this.selectedDocumentProducer=s||"",this.selectedDocumentCreationDate=a||"";let u=this.getMetadataDisplay(t);this.selectedDocumentMetadata=u,this.hasMetadata=!!u,this.selectedDocumentLineRange=l,this.hasLineRange=!!l,this.cdr.markForCheck()}closeModal(){this.selectedDocument=null,this.cdr.markForCheck()}prepareDocumentData(){this.sourceDocumentsWithProps=this.sourceDocuments.map(t=>{let r=this.getFullTitle(t),o=this.getShortTitle(t),i=this.getAuthor(t),s=this.getSource(t),a="Source";if(s&&s!=="Unknown Source"){let l=s.split(" ");a=l.length>2?l.slice(0,2).join(" "):s}else if(i&&i!=="Unknown Author")a=i.split(" ")[0];else if(r&&r!=="Source Document"){let l=r.split(" ");a=l.length>2?l.slice(0,2).join(" "):r}return a.length>20&&(a=a.substring(0,18)+"..."),{original:t,title:r,shortTitle:o,displayLabel:a}})}getShortTitle(t){let r=Object.keys(t.metadata||{});if(r.length>0){let o=r[0],i=o.split(" ");return i.length>3?i.slice(0,3).join(" ")+"...":o}return"Source Document"}getFullTitle(t){let r=Object.keys(t.metadata||{});return r.length>0?r[0]:"Source Document"}getAuthor(t){if(t.metadata?.pdf?.info?.Author)return t.metadata.pdf.info.Author;let r=Object.keys(t.metadata||{});if(r.length>0){let o=r[0];if(t.metadata&&t.metadata[o]&&typeof t.metadata[o]=="string")return t.metadata[o]}return"Unknown Author"}getSource(t){return t.metadata?.source&&typeof t.metadata.source=="string"?t.metadata.source:"Unknown Source"}getProducer(t){if(t.metadata?.pdf?.info?.Producer)return t.metadata.pdf.info.Producer}getCreationDate(t){if(t.metadata?.pdf?.info?.CreationDate)return t.metadata.pdf.info.CreationDate}getLineRange(t){if(!t.metadata)return null;if(t.metadata.loc&&t.metadata.loc.lines&&t.metadata.loc.lines.from!==void 0&&t.metadata.loc.lines.to!==void 0){let r=t.metadata.loc.lines.from,o=t.metadata.loc.lines.to;return r===o?`Line ${r}`:`Lines ${r} - ${o}`}else if(t.metadata["loc.lines.from"]!==void 0&&t.metadata["loc.lines.to"]!==void 0){let r=t.metadata["loc.lines.from"],o=t.metadata["loc.lines.to"];return r===o?`Line ${r}`:`Lines ${r} - ${o}`}return null}getMetadataDisplay(t){if(!t.metadata)return null;let r=Object.keys(t.metadata);if(r.length<=1)return null;let o={};for(let i=1;i<r.length;i++){let s=r[i];s!=="loc.lines.from"&&s!=="loc.lines.to"&&!(s==="loc"&&t.metadata[s]&&t.metadata[s].lines)&&(o[s]=t.metadata[s])}return Object.keys(o).length===0?null:JSON.stringify(o,null,2)}extractFormattedMetadata(t){let r={};if(t.metadata?.pdf?.info){let o=t.metadata.pdf.info;o.Title&&(r.Title=o.Title),o.Author&&(r.Author=o.Author),o.Producer&&(r.Producer=o.Producer),o.CreationDate&&(r["Creation Date"]=this.formatPdfDate(o.CreationDate)),o.ModDate&&(r["Modified Date"]=this.formatPdfDate(o.ModDate)),o.PDFFormatVersion&&(r["PDF Version"]=o.PDFFormatVersion)}return t.metadata?.source&&(r.Source=t.metadata.source),r}formatPdfDate(t){if(t.startsWith("D:"))try{let r=t.substring(2,6),o=t.substring(6,8),i=t.substring(8,10),s=t.substring(10,12),a=t.substring(12,14);return`${r}-${o}-${i} ${s}:${a}`}catch{return t}return t}static \u0275fac=function(r){return new(r||e)(E(Yt),E(ye))};static \u0275cmp=q({type:e,selectors:[["app-source-documents"]],decls:1,vars:1,consts:[[1,"mt-4"],[1,"flex","flex-wrap","gap-2"],[1,"relative","inline-block"],["tabindex","-1","role","dialog","aria-modal","true","aria-labelledby","modalTitle",1,"fixed","inset-0","bg-black","bg-opacity-50","flex","items-center","justify-center","z-50","p-4"],["tabindex","0",1,"px-3","py-1.5","bg-amber-100","dark:bg-amber-900","text-amber-900","dark:text-amber-100","rounded-full","text-sm","font-medium","hover:bg-amber-200","dark:hover:bg-amber-800","transition-colors",3,"click","keydown.enter","keydown.space","title"],["tabindex","-1","role","dialog","aria-modal","true","aria-labelledby","modalTitle",1,"fixed","inset-0","bg-black","bg-opacity-50","flex","items-center","justify-center","z-50","p-4",3,"click","keydown.escape"],["role","document","tabindex","0",1,"bg-white","dark:bg-gray-800","rounded-lg","shadow-xl","max-w-2xl","w-full","max-h-[80vh]","overflow-hidden",3,"click","keydown"],[1,"p-4","border-b","border-gray-200","dark:border-gray-700","flex","justify-between","items-center"],["id","modalTitle",1,"text-lg","font-semibold","text-gray-900","dark:text-white","truncate"],["tabindex","0",1,"text-gray-500","hover:text-gray-700","dark:text-gray-400","dark:hover:text-gray-200",3,"click","keydown.enter"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-6","w-6"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M6 18L18 6M6 6l12 12"],[1,"p-4","overflow-y-auto","max-h-[calc(80vh-8rem)]"],[1,"mb-6"],[1,"mb-4"],[1,"text-sm","font-medium","text-gray-500","dark:text-gray-400"],[1,"text-gray-900","dark:text-white","whitespace-pre-line","bg-gray-50","dark:bg-gray-900","p-3","rounded-md","max-h-60","overflow-y-auto","mt-2"],[1,"p-4","border-t","border-gray-200","dark:border-gray-700","flex","justify-end"],["tabindex","0",1,"px-4","py-2","bg-gray-200","dark:bg-gray-700","text-gray-800","dark:text-gray-200","rounded","hover:bg-gray-300","dark:hover:bg-gray-600","transition-colors",3,"click","keydown.enter","keydown.space"],[1,"bg-gray-50","dark:bg-gray-900","p-3","rounded-md","text-sm","mt-2"],[1,"mb-1"],[1,"font-medium"]],template:function(r,o){r&1&&W(0,ck,5,1,"div",0),r&2&&O(o.sourceDocuments.length>0?0:-1)},dependencies:[Z],styles:["[_nghost-%COMP%]{display:block}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0}to{opacity:1}}@keyframes _ngcontent-%COMP%_slideIn{0%{transform:translateY(20px);opacity:0}to{transform:translateY(0);opacity:1}}.fixed[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .2s ease-out}.fixed[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideIn .3s ease-out}pre[_ngcontent-%COMP%]{white-space:pre-wrap;word-break:break-word}button[_ngcontent-%COMP%]:focus-visible{outline:2px solid #4f46e5;outline-offset:2px}"],changeDetection:0})}return e})();function uk(e,n){e&1&&(d(0,"span",3),p(1,"U"),f())}function dk(e,n){e&1&&(d(0,"span",3),p(1,"A"),f())}function fk(e,n){e&1&&(d(0,"span",3),p(1,"S"),f())}function hk(e,n){if(e&1&&D(0,"div",8),e&2){let t=N();te("innerHTML",t.renderedContent,uo)}}function pk(e,n){e&1&&D(0,"app-source-documents")}function gk(e,n){if(e&1&&(d(0,"div",9)(1,"div",12),k(),d(2,"svg",13),D(3,"path",14),f(),P(),d(4,"div")(5,"p",15),p(6,"Connection Error"),f(),D(7,"p",16),f()()()),e&2){let t=N();C(7),te("innerHTML",t.renderedContent,uo)}}function mk(e,n){e&1&&(d(0,"div",17),D(1,"span")(2,"span")(3,"span"),f())}function vk(e,n){if(e&1&&(D(0,"div",8),W(1,mk,4,0,"div",17)),e&2){let t=N();te("innerHTML",t.renderedContent,uo),C(),O(t.isLastMessage?1:-1)}}function yk(e,n){if(e&1&&(d(0,"div",10)(1,"div",12),k(),d(2,"svg",13),D(3,"path",18),f(),P(),d(4,"div")(5,"p",15),p(6,"Agent Reasoning"),f(),D(7,"div",16),f()()()),e&2){let t=N();C(7),te("innerHTML",t.renderedContent,uo)}}function bk(e,n){if(e&1&&(d(0,"div",11)(1,"div",12),k(),d(2,"svg",13),D(3,"path",19),f(),P(),d(4,"div")(5,"p",15),p(6,"Next Agent"),f(),D(7,"div",16),f()()()),e&2){let t=N();C(7),te("innerHTML",t.renderedContent,uo)}}var fb=(()=>{class e{sanitizer;cdr;chatService;message;isLastMessage=!1;_cachedContent=null;_lastContent="";constructor(t,r,o){this.sanitizer=t,this.cdr=r,this.chatService=o,ee.setOptions({gfm:!0,breaks:!0})}ngOnChanges(t){t.message&&this.message&&this._lastContent!==this.message.content&&(console.log("Message content changed:",this.message.content),console.log("Message isComplete:",this.message.isComplete),this._lastContent=this.message.content,this._cachedContent=null,this.cdr.detectChanges()),t.message&&this.message&&t.message.previousValue&&t.message.previousValue.isComplete!==this.message.isComplete&&(console.log("Message isComplete changed:",this.message.isComplete),this.cdr.detectChanges())}get messageClasses(){let t="";switch(this.message.role){case we.User:return`${t} bg-[var(--techwave-user-msg-bg-color)]`;case we.Assistant:return`${t} bg-[var(--techwave-assistant-msg-bg-color)]`;default:return`${t} bg-[var(--techwave-system-msg-bg-color)]`}}get avatarClasses(){let t="text-white";if(this.message.role===we.Assistant&&this.isErrorMessage)return`${t} bg-red-600`;switch(this.message.role){case we.User:return`${t} bg-blue-600`;case we.Assistant:return`${t} bg-purple-600`;default:return`${t} bg-gray-600`}}get roleName(){if(this.message.role===we.Assistant&&this.isErrorMessage)return"Error";switch(this.message.role){case we.User:return"You";case we.Assistant:return"Assistant";case we.System:return"System";case we.Thinking:return"Thinking";default:return"Unknown"}}get isErrorMessage(){return this.message.content?this.message.role===we.Assistant&&(this.message.content.includes("Unable to connect to the AI service")||this.message.content.includes("Error:")||this.message.content.includes("ENOTFOUND")||this.message.content.includes("Network error")||this.message.content.includes("timed out")||this.message.content.includes("AI service error")):!1}get hasAgentReasoning(){return this.message.content?this.message.role===we.Assistant&&(this.message.content.includes("**Agent:")||this.message.content.includes("**Thought Process:**")||this.message.content.includes("**Action:**")||this.message.content.includes("**Observation:**")):!1}get hasNextAgent(){return this.message.content?this.message.role===we.Assistant&&this.message.content.includes("Processing with"):!1}get renderedContent(){if(!this.message.content||this.message.content.trim()==="")return this.sanitizer.bypassSecurityTrustHtml("");if(this._cachedContent)return this._cachedContent;let t="";try{return ee.setOptions({gfm:!0,breaks:!0}),t=ee.parse(this.message.content),t=t.replace(/<pre><code class="language-(\w+)">([\s\S]*?)<\/code><\/pre>/g,(r,o,i)=>{try{return`<pre><code class="language-${o} hljs">${i}</code></pre>`}catch{return r}}),t=t.replace(/<table>/g,'<div class="table-responsive"><table class="table-auto">').replace(/<\/table>/g,"</table></div>"),this._cachedContent=this.sanitizer.bypassSecurityTrustHtml(t),this._cachedContent}catch(r){return console.error("Error parsing markdown:",r),this.sanitizer.bypassSecurityTrustHtml(`<p>${this.message.content}</p>`)}}static \u0275fac=function(r){return new(r||e)(E(Kd),E(ye),E(Yt))};static \u0275cmp=q({type:e,selectors:[["app-message"]],inputs:{message:"message",isLastMessage:"isLastMessage"},features:[Ht],decls:19,vars:14,consts:[[1,"py-5","px-5","sm:px-8","w-full",3,"ngClass"],[1,"flex","items-start","max-w-3xl","mx-auto"],[1,"flex-shrink-0","h-10","w-10","rounded-full","flex","items-center","justify-center","mr-4","mt-1",3,"ngClass"],[1,"text-sm","font-semibold"],[1,"flex-1","space-y-2"],[1,"flex","items-center"],[1,"font-medium","text-[var(--techwave-heading-color)]"],[1,"text-xs","text-[var(--techwave-muted-color)]","ml-2"],[1,"prose","prose-sm","max-w-none","text-[var(--techwave-body-color)]",3,"innerHTML"],[1,"prose","prose-sm","max-w-none","bg-red-50","dark:bg-red-900/20","border","border-red-200","dark:border-red-800","rounded-md","p-4","text-red-700","dark:text-red-300"],[1,"prose","prose-sm","max-w-none","bg-indigo-50","dark:bg-indigo-900/20","border","border-indigo-200","dark:border-indigo-800","rounded-md","p-4","text-indigo-700","dark:text-indigo-300"],[1,"prose","prose-sm","max-w-none","bg-purple-50","dark:bg-purple-900/20","border","border-purple-200","dark:border-purple-800","rounded-md","p-4","text-purple-700","dark:text-purple-300"],[1,"flex","items-start"],["xmlns","http://www.w3.org/2000/svg","viewBox","0 0 20 20","fill","currentColor",1,"h-5","w-5","mr-2","flex-shrink-0","mt-0.5"],["fill-rule","evenodd","d","M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule","evenodd"],[1,"font-medium","mb-1"],[1,"text-sm",3,"innerHTML"],[1,"typing-animation","mt-1"],["fill-rule","evenodd","d","M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule","evenodd"],["fill-rule","evenodd","d","M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule","evenodd"]],template:function(r,o){if(r&1&&(d(0,"div",0)(1,"div",1)(2,"div",2),W(3,uk,2,0,"span",3)(4,dk,2,0,"span",3)(5,fk,2,0,"span",3),f(),d(6,"div",4)(7,"div",5)(8,"span",6),p(9),f(),d(10,"span",7),p(11),Va(12,"date"),f()(),W(13,hk,1,1,"div",8)(14,pk,1,0,"app-source-documents")(15,gk,8,1,"div",9)(16,vk,2,2)(17,yk,8,1,"div",10)(18,bk,8,1,"div",11),f()()()),r&2){let i;te("ngClass",o.messageClasses),C(2),te("ngClass",o.avatarClasses),C(),O((i=o.message.role)==="user"?3:i==="assistant"?4:5),C(6),fe(o.roleName),C(2),ke(" ",ja(12,11,o.message.timestamp,"short")," "),C(2),O(o.message.role==="user"||o.message.isComplete&&!o.isErrorMessage&&!o.hasAgentReasoning&&!o.hasNextAgent?13:-1),C(),O(o.message.role==="assistant"&&o.message.isComplete&&!o.isErrorMessage?14:-1),C(),O(o.message.role==="assistant"&&o.isErrorMessage?15:-1),C(),O(o.message.role==="assistant"&&!o.message.isComplete&&!o.isErrorMessage&&!o.hasAgentReasoning&&!o.hasNextAgent?16:-1),C(),O(o.hasAgentReasoning?17:-1),C(),O(o.hasNextAgent?18:-1)}},dependencies:[Z,el,tl,db],styles:["[_nghost-%COMP%]{display:block}.animation-delay-200[_ngcontent-%COMP%]{animation-delay:.2s}.animation-delay-400[_ngcontent-%COMP%]{animation-delay:.4s}[_nghost-%COMP%]     pre{background-color:var(--techwave-code-bg-color);border-radius:.375rem;padding:1rem;overflow-x:auto}[_nghost-%COMP%]     code{font-family:monospace;background-color:var(--techwave-code-bg-color);padding:.125rem .25rem;border-radius:.25rem}[_nghost-%COMP%]     pre code{padding:0;background-color:transparent}[_nghost-%COMP%]     table{width:100%;border-collapse:collapse;margin:1rem 0;overflow-x:auto;display:block}[_nghost-%COMP%]     table th{background-color:var(--techwave-table-header-bg-color, var(--techwave-code-bg-color));color:var(--techwave-table-header-text-color, var(--techwave-heading-color));font-weight:600;text-align:left;padding:.75rem 1rem;border:1px solid var(--techwave-table-border-color, var(--techwave-border-color))}[_nghost-%COMP%]     table td{padding:.75rem 1rem;border:1px solid var(--techwave-table-border-color, var(--techwave-border-color));vertical-align:top}[_nghost-%COMP%]     table tr:nth-child(2n){background-color:var(--techwave-table-row-alt-bg-color, rgba(0, 0, 0, .03))}[_nghost-%COMP%]     table tr:hover{background-color:var(--techwave-table-row-hover-bg-color, rgba(0, 0, 0, .05))}.typing-animation[_ngcontent-%COMP%]{display:flex;justify-content:space-between;width:60px}.typing-animation[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{width:10px;height:10px;border-radius:50%;background-color:var(--techwave-body-color);animation:_ngcontent-%COMP%_typing 1.5s infinite}.typing-animation[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2){animation-delay:.2s}.typing-animation[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3){animation-delay:.4s}@keyframes _ngcontent-%COMP%_typing{0%{opacity:.2}50%{opacity:1}to{opacity:.2}}"],changeDetection:0})}return e})();var Ck=["messagesContainer"];function wk(e,n){e&1&&(d(0,"div",5)(1,"h2",14),p(2,"Start a new conversation"),f(),d(3,"p",15),p(4,"Ask a question to begin chatting with the AI assistant"),f()())}function Dk(e,n){if(e&1&&D(0,"app-message",16),e&2){let t=n.$implicit,r=N(2);te("message",t)("isLastMessage",r.isLastMessage(t))}}function _k(e,n){if(e&1&&(d(0,"div",6),qt(1,Dk,1,2,"app-message",16,Pa),f()),e&2){let t=N();C(),Wt(t.currentChat==null?null:t.currentChat.messages)}}function Ek(e,n){e&1&&(d(0,"div",13),p(1," AI is thinking... "),f())}var hb=(()=>{class e{chatService;cdr;themeService;currentChat=null;newMessage="";shouldScrollToBottom=!1;isLoading=!1;subscription=new ie;messagesContainer;constructor(t,r,o){this.chatService=t,this.cdr=r,this.themeService=o,wn(()=>{this.currentChat=this.chatService.currentChat(),this.currentChat&&(this.shouldScrollToBottom=!0),this.cdr.markForCheck()})}ngOnInit(){this.subscription.add(this.chatService.messageChunks$.subscribe(r=>{if(console.log("Received message chunk:",r.type,r),r.type==="start")console.log("Setting isLoading to true"),this.isLoading=!0;else if(r.type==="end"||r.type==="error"){console.log("Stream ended or error occurred, setting isLoading to false"),this.isLoading=!1;let o=this.chatService.currentChat();o&&(this.currentChat=b({},o)),this.shouldScrollToBottom=!0,this.cdr.markForCheck()}else r.type==="content"?(this.shouldScrollToBottom=!0,this.cdr.markForCheck()):r.type==="agentReasoning"?(console.log("Received agent reasoning:",r.content),this.shouldScrollToBottom=!0,this.cdr.markForCheck()):r.type==="nextAgent"&&(console.log("Received next agent:",r.content),this.shouldScrollToBottom=!0,this.cdr.markForCheck())}));let t=setInterval(()=>{if(this.isLoading){let r=this.getLastMessageTime(),o=new Date().getTime();r&&o-r>3e4&&(console.log("Loading state appears to be stuck, resetting..."),this.isLoading=!1,this.cdr.markForCheck())}},1e4);this.subscription.add(()=>{clearInterval(t)})}getLastMessageTime(){if(!this.currentChat?.messages||this.currentChat.messages.length===0)return null;let t=this.currentChat.messages[this.currentChat.messages.length-1];return t.timestamp?new Date(t.timestamp).getTime():null}ngOnDestroy(){if(this.subscription.unsubscribe(),this.isLoading&&(console.log("Component being destroyed while loading, resetting loading state"),this.isLoading=!1,this.currentChat&&this.currentChat.messages&&this.currentChat.messages.length>0)){let t=this.currentChat.messages[this.currentChat.messages.length-1];t&&!t.isComplete&&(console.log("Marking incomplete message as complete on component destroy"),this.sendMessage(""))}}scrollToBottom(){try{this.messagesContainer.nativeElement.scrollTop=this.messagesContainer.nativeElement.scrollHeight}catch(t){console.error("Error scrolling to bottom:",t)}}ngAfterViewChecked(){this.shouldScrollToBottom&&(this.scrollToBottom(),this.shouldScrollToBottom=!1)}isLastMessage(t){return t===this.currentChat?.messages[this.currentChat.messages.length-1]}onEnterKey(t){t.shiftKey||(t.preventDefault(),this.newMessage.trim()&&(this.sendMessage(this.newMessage),this.newMessage=""))}sendMessage(t){t.trim()&&(this.chatService.sendMessage(t),this.shouldScrollToBottom=!0)}static \u0275fac=function(r){return new(r||e)(E(Yt),E(ye),E(No))};static \u0275cmp=q({type:e,selectors:[["app-chat-area"]],viewQuery:function(r,o){if(r&1&&Iv(Ck,5),r&2){let i;Fa(i=La())&&(o.messagesContainer=i.first)}},decls:15,vars:6,consts:[["messagesContainer",""],["messageInput",""],[1,"h-full","flex","flex-col"],[1,"flex-1","overflow-y-auto","p-4","scrollbar-thin"],[1,"max-w-4xl","mx-auto"],[1,"flex","flex-col","items-center","justify-center","h-full","text-center","p-4"],[1,"space-y-6"],[1,"p-4","border-t","border-[var(--techwave-border-color)]"],[1,"relative"],["placeholder","Type your message here...","rows","2",1,"w-full","p-3","pr-12","bg-[var(--techwave-input-bg-color)]","border","border-[var(--techwave-border-color)]","rounded-lg","text-[var(--techwave-input-text-color)]","placeholder-[var(--techwave-placeholder-color)]","focus:outline-none","focus:ring-2","focus:ring-[var(--techwave-primary-color)]","focus:border-transparent","resize-none",3,"keydown.enter","ngModelChange","disabled","ngModel"],[1,"absolute","right-3","bottom-3","text-[var(--techwave-primary-color)]","hover:text-[var(--techwave-primary-hover-color)]","disabled:opacity-50","disabled:cursor-not-allowed",3,"click","disabled"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-6","w-6"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 19l9 2-9-18-9 18 9-2zm0 0v-8"],[1,"mt-2","text-sm","text-[var(--techwave-body-color)]"],[1,"text-xl","font-bold","mb-2","text-[var(--techwave-heading-color)]"],[1,"text-[var(--techwave-body-color)]","mb-6"],[3,"message","isLastMessage"]],template:function(r,o){if(r&1){let i=Ve();d(0,"div",2)(1,"div",3,0)(3,"div",4),W(4,wk,5,0,"div",5)(5,_k,3,0,"div",6),f()(),d(6,"div",7)(7,"div",4)(8,"div",8)(9,"textarea",9,1),V("keydown.enter",function(a){return Q(i),J(o.onEnterKey(a))}),bn("ngModelChange",function(a){return Q(i),Ln(o.newMessage,a)||(o.newMessage=a),J(a)}),f(),d(11,"button",10),V("click",function(){Q(i);let a=ot(10);return o.sendMessage(o.newMessage),o.newMessage="",J(a.focus())}),k(),d(12,"svg",11),D(13,"path",12),f()()(),W(14,Ek,2,0,"div",13),f()()()}r&2&&(C(4),O(!(o.currentChat==null||o.currentChat.messages==null)&&o.currentChat.messages.length?-1:4),C(),O(!(o.currentChat==null||o.currentChat.messages==null)&&o.currentChat.messages.length?5:-1),C(4),te("disabled",o.isLoading),yn("ngModel",o.newMessage),C(2),te("disabled",o.isLoading||!o.newMessage.trim()),C(3),O(o.isLoading?14:-1))},dependencies:[Z,Ao,qn,ko,Dr,fb],styles:["[_nghost-%COMP%]{display:block;height:100%}.animation-delay-200[_ngcontent-%COMP%]{animation-delay:.2s}.animation-delay-400[_ngcontent-%COMP%]{animation-delay:.4s}"],changeDetection:0})}return e})();var xk=["*"],Sk=(e,n,t)=>({"danger-hover":e,"outline-hover":n,"primary-hover":t}),pb=(()=>{class e{variant="primary";size="md";disabled=!1;type="button";fullWidth=!1;clicked=new me;buttonClick=new me;get buttonClasses(){let t="inline-flex items-center justify-center",r={primary:"bg-primary-600 text-white",secondary:"bg-[var(--techwave-some-r-bg-color)] text-[var(--techwave-heading-color)]",ghost:"bg-transparent text-[var(--techwave-heading-color)]",outline:"bg-transparent border-2 border-[var(--techwave-border-color)] text-[var(--techwave-heading-color)]",danger:"bg-red-600 text-white"},o={sm:"px-3 py-1 h-8 text-xs",md:"px-6 py-2 h-10 text-sm",lg:"px-8 py-3 h-12 text-base"},i=this.fullWidth?"w-full":"";return`${t} ${r[this.variant]} ${o[this.size]} ${i}`}static \u0275fac=function(r){return new(r||e)};static \u0275cmp=q({type:e,selectors:[["app-button"]],inputs:{variant:"variant",size:"size",disabled:"disabled",type:"type",fullWidth:"fullWidth"},outputs:{clicked:"clicked",buttonClick:"buttonClick"},ngContentSelectors:xk,decls:2,vars:9,consts:[[3,"click","disabled","type","ngClass"]],template:function(r,o){r&1&&(xv(),d(0,"button",0),V("click",function(s){return o.clicked.emit(s),o.buttonClick.emit(s)}),Sv(1),f()),r&2&&(bv(o.buttonClasses),te("disabled",o.disabled)("type",o.type)("ngClass",kv(5,Sk,o.variant==="danger",o.variant==="outline",o.variant==="primary")))},dependencies:[Z,el],styles:['[_nghost-%COMP%]{display:inline-block}button[_ngcontent-%COMP%]{font-family:var(--techwave-heading-font-family);font-size:14px;letter-spacing:.5px;font-weight:500;cursor:pointer;transition:all .3s ease;border-radius:20px;display:inline-flex;align-items:center;justify-content:center;gap:.5rem;position:relative;overflow:hidden}button[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}button[_ngcontent-%COMP%]:focus{outline:none}button[_ngcontent-%COMP%]:hover{box-shadow:0 5px 15px var(--techwave-main-color1);opacity:.95}button[_ngcontent-%COMP%]:before{inset:0;position:absolute;color:var(--techwave-main-color1);content:"";opacity:.1;border-radius:20px;box-shadow:0 5px 15px}button[_ngcontent-%COMP%]:after{content:"";position:absolute;inset:0;border-radius:20px;border:2px solid transparent;background:linear-gradient(270deg,var(--techwave-main-color1),var(--techwave-main-color2),var(--techwave-main-color1),var(--techwave-main-color2));background-size:300% 300%;animation:_ngcontent-%COMP%_animatedgradient 4s ease alternate infinite;opacity:0;transition:opacity .3s ease}button[_ngcontent-%COMP%]:hover:after{opacity:1}.primary-hover[_ngcontent-%COMP%]:hover{opacity:.7!important}.primary-hover[_ngcontent-%COMP%]:hover:after{opacity:.5!important}.outline-hover[_ngcontent-%COMP%]:hover{opacity:.7!important}.outline-hover[_ngcontent-%COMP%]:before{opacity:.05!important}.outline-hover[_ngcontent-%COMP%]:hover:after{opacity:.4!important}.danger-hover[_ngcontent-%COMP%]:hover{box-shadow:0 5px 15px #dc262699!important;opacity:.7!important}.danger-hover[_ngcontent-%COMP%]:before{color:#dc2626cc!important}.danger-hover[_ngcontent-%COMP%]:hover:after{background:linear-gradient(270deg,#dc2626,#ef4444,#dc2626,#ef4444)!important;opacity:.4!important}@keyframes _ngcontent-%COMP%_animatedgradient{0%{background-position:0% 50%}50%{background-position:100% 50%}to{background-position:0% 50%}}'],changeDetection:0})}return e})();var Mk=(e,n)=>n.id;function Ik(e,n){e&1&&(d(0,"div",6)(1,"p"),p(2,"No previous chats"),f(),d(3,"p",12),p(4,"Start a new conversation"),f()())}function Tk(e,n){e&1&&(d(0,"span",19),p(1," Synced "),f())}function kk(e,n){if(e&1&&(d(0,"div",20),p(1),f()),e&2){let t=N().$implicit;C(),ke(' "',t.firstUserMessage,'" ')}}function Ak(e,n){if(e&1){let t=Ve();d(0,"button",14),V("click",function(){let o=Q(t).$implicit,i=N(2);return J(i.onSelectChat(o.id))}),d(1,"div",15)(2,"span",16),p(3),f(),d(4,"span",17),p(5),f()(),d(6,"div",18)(7,"span"),p(8),Va(9,"date"),f(),W(10,Tk,2,0,"span",19),f(),W(11,kk,2,1,"div",20),f()}if(e&2){let t=n.$implicit,r=N(2);dt("bg-[var(--techwave-some-a-bg-color)",r.currentChatId===t.id)("border-[var(--techwave-main-color)",r.currentChatId===t.id)("text-[var(--techwave-heading-color)",r.currentChatId===t.id)("font-medium",r.currentChatId===t.id),C(3),fe(t.title),C(2),fe(t.messages.length),C(3),fe(ja(9,13,t.updatedAt,"short")),C(2),O(t.flowChatId?10:-1),C(),O(t.firstUserMessage?11:-1)}}function Nk(e,n){if(e&1&&(d(0,"div",7),qt(1,Ak,12,16,"button",13,Mk),f()),e&2){let t=N();C(),Wt(t.chatHistory)}}var gb=(()=>{class e{chatService;themeService;cdr;chatHistory=[];currentChatId=null;subscription=new ie;constructor(t,r,o){this.chatService=t,this.themeService=r,this.cdr=o}ngOnInit(){this.refreshChatHistory(),this.subscription.add(this.chatService.messageChunks$.subscribe(()=>{this.refreshChatHistory()}))}ngOnDestroy(){this.subscription.unsubscribe()}refreshChatHistory(){let t=this.chatService.chatHistory();this.chatHistory=t.map(o=>{let i=this.getFirstUserMessage(o);return I(b({},o),{firstUserMessage:i})});let r=this.chatService.currentChat();this.currentChatId=r?r.id:null,this.cdr.markForCheck()}onNewChat(){console.log("Creating new chat"),this.chatService.createNewChat(),setTimeout(()=>{this.refreshChatHistory()},0)}onSelectChat(t){t!==this.currentChatId&&(console.log("Selecting chat:",t),this.chatService.setActiveChat(t),this.refreshChatHistory())}onClearChat(){console.log("Clearing current chat"),this.chatService.clearCurrentChat(),setTimeout(()=>{this.refreshChatHistory()},0)}onDeleteChat(){this.currentChatId&&(console.log("Deleting chat:",this.currentChatId),this.chatService.deleteChat(this.currentChatId),setTimeout(()=>{this.refreshChatHistory()},0))}getFirstUserMessage(t){let r=t.messages.find(o=>o.role===we.User);return r?r.content:null}static \u0275fac=function(r){return new(r||e)(E(Yt),E(No),E(ye))};static \u0275cmp=q({type:e,selectors:[["app-chat-history"]],decls:18,vars:7,consts:[[1,"h-full","flex","flex-col","bg-[var(--techwave-some-r-bg-color)]"],[1,"p-4","border-b","border-[var(--techwave-border-color)]"],[1,"text-lg","font-medium","text-[var(--techwave-heading-color)]"],[1,"text-sm","text-[var(--techwave-body-color)]"],["variant","primary",3,"buttonClick","fullWidth"],[1,"flex-1","overflow-y-auto","scrollbar-thin"],[1,"text-center","py-8","text-[var(--techwave-body-color)]","text-sm"],[1,"space-y-2","p-3"],[1,"p-4","border-t","border-[var(--techwave-border-color)]"],[1,"grid","grid-cols-2","gap-2"],["variant","outline","size","sm",1,"text-xs","whitespace-nowrap","flex","items-center","justify-center","h-9","px-1",3,"buttonClick","fullWidth","disabled"],["variant","danger","size","sm",1,"text-xs","whitespace-nowrap","flex","items-center","justify-center","h-9","px-1",3,"buttonClick","fullWidth","disabled"],[1,"mt-2"],[1,"w-full","text-left","py-3","px-4","rounded-lg","hover:bg-[var(--techwave-some-a-bg-color)]","text-sm","transition-colors","border","border-transparent",3,"bg-[var(--techwave-some-a-bg-color)","border-[var(--techwave-main-color)","text-[var(--techwave-heading-color)","font-medium"],[1,"w-full","text-left","py-3","px-4","rounded-lg","hover:bg-[var(--techwave-some-a-bg-color)]","text-sm","transition-colors","border","border-transparent",3,"click"],[1,"flex","items-center","justify-between"],[1,"truncate","max-w-[180px]"],[1,"text-xs","text-[var(--techwave-body-color)]","bg-[var(--techwave-button-bg-color)]","px-2","py-1","rounded-full"],[1,"flex","items-center","justify-between","text-xs","text-[var(--techwave-body-color)]","mt-1"],[1,"px-2","py-0.5","rounded-full","bg-[var(--techwave-main-color)]","bg-opacity-20","text-[var(--techwave-main-color)]"],[1,"mt-2","text-xs","text-[var(--techwave-body-color)]","italic","truncate"]],template:function(r,o){r&1&&(d(0,"div",0)(1,"div",1)(2,"h2",2),p(3,"Previous Chats"),f(),d(4,"p",3),p(5," Past conversations and queries "),f()(),d(6,"div",1)(7,"app-button",4),V("buttonClick",function(){return o.onNewChat()}),p(8," New Chat "),f()(),d(9,"div",5),W(10,Ik,5,0,"div",6)(11,Nk,3,0,"div",7),f(),d(12,"div",8)(13,"div",9)(14,"app-button",10),V("buttonClick",function(){return o.onClearChat()}),p(15," Clear Chat "),f(),d(16,"app-button",11),V("buttonClick",function(){return o.onDeleteChat()}),p(17," Delete Chat "),f()()()()),r&2&&(C(7),te("fullWidth",!0),C(3),O(o.chatHistory.length===0?10:-1),C(),O(o.chatHistory.length>0?11:-1),C(3),te("fullWidth",!0)("disabled",o.chatHistory.length===0),C(2),te("fullWidth",!0)("disabled",!o.currentChatId))},dependencies:[Z,tl,pb],styles:["[_nghost-%COMP%]{display:block;height:100%}"],changeDetection:0})}return e})();function Rk(e,n){if(e&1){let t=Ve();d(0,"div",16)(1,"div",17)(2,"div",18)(3,"h2",19),p(4,"Thinking Process"),f(),d(5,"button",20),V("click",function(){Q(t);let o=N();return J(o.closeMobileView())}),p(6," \u2715 "),f()(),d(7,"div",21),D(8,"app-thinking-panel"),f()()()}}function Ok(e,n){if(e&1){let t=Ve();d(0,"div",16)(1,"div",22)(2,"div",18)(3,"h2",19),p(4,"Previous Chats"),f(),d(5,"button",20),V("click",function(){Q(t);let o=N();return J(o.closeMobileView())}),p(6," \u2715 "),f()(),d(7,"div",21),D(8,"app-chat-history"),f()()()}}var mb=(()=>{class e{authService;mobileView="chat";userEmail=null;constructor(t){this.authService=t,this.userEmail=this.authService.getUserEmail()}toggleMobileView(t){this.mobileView=this.mobileView===t?"chat":t}closeMobileView(){this.mobileView="chat"}logout(){this.authService.logout()}static \u0275fac=function(r){return new(r||e)(E(In))};static \u0275cmp=q({type:e,selectors:[["app-chat-layout"]],decls:28,vars:3,consts:[[1,"flex","flex-col","h-full","overflow-hidden"],[1,"bg-[var(--techwave-header-bg-color)]","border-b","border-[var(--techwave-border-color)]","p-3","flex","justify-between","items-center"],[1,"flex","items-center"],[1,"text-xl","font-semibold","text-[var(--techwave-heading-color)]"],[1,"flex","items-center","gap-4"],[1,"text-[var(--techwave-text-color)]"],[1,"px-4","py-2","rounded-md","bg-red-500","hover:bg-red-600","text-white","transition-colors",3,"click"],[1,"flex","flex-1","overflow-hidden"],[1,"w-[25%]","hidden","lg:block","border-r","border-[var(--techwave-border-color)]","bg-[var(--techwave-some-r-bg-color)]"],[1,"flex-1","flex","flex-col","bg-[var(--techwave-site-bg-color)]"],[1,"flex","items-center","justify-between","p-3","border-b","border-[var(--techwave-border-color)]","bg-[var(--techwave-header-bg-color)]","lg:hidden"],[1,"text-lg","font-medium","text-[var(--techwave-heading-color)]","px-2"],[1,"flex","gap-3"],[1,"px-4","py-2","text-sm","rounded-full","bg-[var(--techwave-some-a-bg-color)]","text-[var(--techwave-heading-color)]","border","border-[var(--techwave-border-color)]",3,"click"],[1,"flex-1","overflow-hidden"],[1,"w-72","hidden","lg:block","border-l","border-[var(--techwave-border-color)]","bg-[var(--techwave-some-r-bg-color)]"],[1,"fixed","inset-0","z-50","lg:hidden","bg-black","bg-opacity-50"],[1,"absolute","left-0","top-0","h-full","w-[80%]","max-w-md","bg-[var(--techwave-some-r-bg-color)]","transform","transition-transform","duration-300","ease-in-out","shadow-xl"],[1,"flex","justify-between","items-center","p-4","border-b","border-[var(--techwave-border-color)]"],[1,"text-lg","font-medium","text-[var(--techwave-heading-color)]"],[1,"p-2","rounded-full","text-[var(--techwave-heading-color)]","hover:bg-[var(--techwave-some-a-bg-color)]",3,"click"],[1,"h-[calc(100%-65px)]","overflow-auto"],[1,"absolute","right-0","top-0","h-full","w-5/6","max-w-sm","bg-[var(--techwave-some-r-bg-color)]","transform","transition-transform","duration-300","ease-in-out","shadow-xl"]],template:function(r,o){r&1&&(d(0,"div",0)(1,"nav",1)(2,"div",2)(3,"h1",3),p(4,"AI Chat"),f()(),d(5,"div",4)(6,"span",5),p(7),f(),d(8,"button",6),V("click",function(){return o.logout()}),p(9," Logout "),f()()(),d(10,"div",7)(11,"div",8),D(12,"app-thinking-panel"),f(),d(13,"div",9)(14,"div",10)(15,"h2",11),p(16,"AI Chat"),f(),d(17,"div",12)(18,"button",13),V("click",function(){return o.toggleMobileView("thinking")}),p(19," \u{1F4AD} Thinking "),f(),d(20,"button",13),V("click",function(){return o.toggleMobileView("history")}),p(21," \u{1F4DC} History "),f()()(),d(22,"div",14),D(23,"app-chat-area"),f()(),d(24,"div",15),D(25,"app-chat-history"),f(),W(26,Rk,9,0,"div",16)(27,Ok,9,0,"div",16),f()()),r&2&&(C(7),fe(o.userEmail),C(19),O(o.mobileView==="thinking"?26:-1),C(),O(o.mobileView==="history"?27:-1))},dependencies:[Z,Zy,hb,gb],styles:["[_nghost-%COMP%]{display:block;height:100%}"],changeDetection:0})}return e})();var vb=()=>({exact:!0});function Pk(e,n){e&1&&(k(),D(0,"path",18))}function Fk(e,n){e&1&&(k(),D(0,"path",19))}function Lk(e,n){if(e&1){let t=Ve();d(0,"div",20)(1,"div",21)(2,"a",22),V("click",function(){Q(t);let o=N();return J(o.closeMobileMenu())}),p(3," Home "),f(),d(4,"a",23),V("click",function(){Q(t);let o=N();return J(o.closeMobileMenu())}),p(5," Pricing "),f(),d(6,"a",24),V("click",function(){Q(t);let o=N();return J(o.closeMobileMenu())}),p(7," About "),f(),d(8,"a",25),V("click",function(){Q(t);let o=N();return J(o.closeMobileMenu())}),p(9," Start Chat "),f()()()}e&2&&(C(2),te("routerLinkActiveOptions",xd(1,vb)))}var Fo=(()=>{class e{isMobileMenuOpen=!1;toggleMobileMenu(){this.isMobileMenuOpen=!this.isMobileMenuOpen}closeMobileMenu(){this.isMobileMenuOpen=!1}static \u0275fac=function(r){return new(r||e)};static \u0275cmp=q({type:e,selectors:[["app-navbar"]],decls:24,vars:5,consts:[[1,"bg-white","shadow-lg","fixed","w-full","top-0","z-50"],[1,"max-w-7xl","mx-auto","px-4","sm:px-6","lg:px-8"],[1,"flex","justify-between","h-16"],[1,"flex","items-center"],["routerLink","/",1,"flex-shrink-0","flex","items-center"],["src","/assets/logo.png","alt","Psychology Chat",1,"h-8","w-auto"],[1,"ml-2","text-xl","font-bold","text-gray-900"],[1,"hidden","md:flex","items-center","space-x-8"],["routerLink","/","routerLinkActive","text-blue-600 border-b-2 border-blue-600",1,"text-gray-700","hover:text-blue-600","px-3","py-2","text-sm","font-medium","transition-colors",3,"routerLinkActiveOptions"],["routerLink","/pricing","routerLinkActive","text-blue-600 border-b-2 border-blue-600",1,"text-gray-700","hover:text-blue-600","px-3","py-2","text-sm","font-medium","transition-colors"],["routerLink","/about","routerLinkActive","text-blue-600 border-b-2 border-blue-600",1,"text-gray-700","hover:text-blue-600","px-3","py-2","text-sm","font-medium","transition-colors"],["routerLink","/sign-in",1,"bg-blue-600","hover:bg-blue-700","text-white","px-6","py-2","rounded-lg","text-sm","font-medium","transition-colors"],[1,"md:hidden","flex","items-center"],[1,"text-gray-700","hover:text-blue-600","focus:outline-none","focus:text-blue-600",3,"click"],["fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-6","w-6"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M4 6h16M4 12h16M4 18h16",4,"ngIf"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M6 18L18 6M6 6l12 12",4,"ngIf"],["class","md:hidden",4,"ngIf"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M4 6h16M4 12h16M4 18h16"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M6 18L18 6M6 6l12 12"],[1,"md:hidden"],[1,"px-2","pt-2","pb-3","space-y-1","sm:px-3","bg-white","border-t"],["routerLink","/","routerLinkActive","text-blue-600 bg-blue-50",1,"text-gray-700","hover:text-blue-600","block","px-3","py-2","text-base","font-medium",3,"click","routerLinkActiveOptions"],["routerLink","/pricing","routerLinkActive","text-blue-600 bg-blue-50",1,"text-gray-700","hover:text-blue-600","block","px-3","py-2","text-base","font-medium",3,"click"],["routerLink","/about","routerLinkActive","text-blue-600 bg-blue-50",1,"text-gray-700","hover:text-blue-600","block","px-3","py-2","text-base","font-medium",3,"click"],["routerLink","/sign-in",1,"bg-blue-600","hover:bg-blue-700","text-white","block","px-3","py-2","text-base","font-medium","rounded-lg","mt-4",3,"click"]],template:function(r,o){r&1&&(d(0,"nav",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"a",4),D(5,"img",5),d(6,"span",6),p(7,"Psychology Chat"),f()()(),d(8,"div",7)(9,"a",8),p(10," Home "),f(),d(11,"a",9),p(12," Pricing "),f(),d(13,"a",10),p(14," About "),f()(),d(15,"div",3)(16,"a",11),p(17," Start Chat "),f()(),d(18,"div",12)(19,"button",13),V("click",function(){return o.toggleMobileMenu()}),k(),d(20,"svg",14),W(21,Pk,1,0,"path",15)(22,Fk,1,0,"path",16),f()()()(),W(23,Lk,10,2,"div",17),f()()),r&2&&(C(9),te("routerLinkActiveOptions",xd(4,vb)),C(12),te("ngIf",!o.isMobileMenuOpen),C(),te("ngIf",o.isMobileMenuOpen),C(),te("ngIf",o.isMobileMenuOpen))},dependencies:[Z,Jv,Be,je,my],styles:['nav[_ngcontent-%COMP%]{-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);background-color:#fffffff2}.router-link-active[_ngcontent-%COMP%]{position:relative}.router-link-active[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:-2px;left:0;right:0;height:2px;background-color:#2563eb}']})}return e})();var yb=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=q({type:e,selectors:[["app-hero-banner"]],decls:43,vars:0,consts:[[1,"relative","bg-gradient-to-br","from-blue-600","via-purple-600","to-indigo-700","pt-20","pb-16","overflow-hidden"],[1,"absolute","inset-0","bg-black","opacity-20"],[1,"absolute","inset-0","bg-pattern"],[1,"relative","max-w-7xl","mx-auto","px-4","sm:px-6","lg:px-8"],[1,"text-center"],[1,"text-4xl","md:text-6xl","font-bold","text-white","mb-6","leading-tight"],[1,"block","text-transparent","bg-clip-text","bg-gradient-to-r","from-yellow-400","to-orange-500"],[1,"text-xl","md:text-2xl","text-blue-100","mb-8","max-w-3xl","mx-auto","leading-relaxed"],[1,"flex","flex-col","sm:flex-row","gap-4","justify-center","items-center","mb-12"],["routerLink","/sign-up",1,"bg-white","text-blue-600","hover:bg-gray-100","px-8","py-4","rounded-lg","text-lg","font-semibold","transition-all","duration-300","transform","hover:scale-105","shadow-lg"],["routerLink","/pricing",1,"border-2","border-white","text-white","hover:bg-white","hover:text-blue-600","px-8","py-4","rounded-lg","text-lg","font-semibold","transition-all","duration-300","transform","hover:scale-105"],[1,"grid","grid-cols-1","md:grid-cols-3","gap-8","mt-16"],[1,"bg-white","bg-opacity-20","rounded-full","w-16","h-16","flex","items-center","justify-center","mx-auto","mb-4"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-8","h-8","text-white"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"],[1,"text-lg","font-semibold","text-white","mb-2"],[1,"text-blue-100"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"],[1,"absolute","top-20","left-10","w-20","h-20","bg-white","bg-opacity-10","rounded-full","animate-pulse"],[1,"absolute","bottom-20","right-10","w-32","h-32","bg-white","bg-opacity-5","rounded-full","animate-pulse","delay-1000"]],template:function(r,o){r&1&&(d(0,"section",0),D(1,"div",1)(2,"div",2),d(3,"div",3)(4,"div",4)(5,"h1",5),p(6," Transform Your Mental Health "),d(7,"span",6),p(8," with AI-Powered Psychology "),f()(),d(9,"p",7),p(10," Get personalized psychological support 24/7 with our advanced AI chatbot. Professional guidance, instant responses, and complete privacy. "),f(),d(11,"div",8)(12,"a",9),p(13," Start Free Trial "),f(),d(14,"a",10),p(15," View Pricing "),f()(),d(16,"div",11)(17,"div",4)(18,"div",12),k(),d(19,"svg",13),D(20,"path",14),f()(),P(),d(21,"h3",15),p(22,"24/7 Support"),f(),d(23,"p",16),p(24,"Always available when you need help"),f()(),d(25,"div",4)(26,"div",12),k(),d(27,"svg",13),D(28,"path",17),f()(),P(),d(29,"h3",15),p(30,"Private & Secure"),f(),d(31,"p",16),p(32,"Your conversations are completely confidential"),f()(),d(33,"div",4)(34,"div",12),k(),d(35,"svg",13),D(36,"path",18),f()(),P(),d(37,"h3",15),p(38,"AI-Powered"),f(),d(39,"p",16),p(40,"Advanced psychology insights and guidance"),f()()()()(),D(41,"div",19)(42,"div",20),f())},dependencies:[Z,Be,je],styles:[`@keyframes _ngcontent-%COMP%_float{0%,to{transform:translateY(0)}50%{transform:translateY(-20px)}}.animate-float[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_float 6s ease-in-out infinite}@keyframes _ngcontent-%COMP%_gradient{0%{background-position:0% 50%}50%{background-position:100% 50%}to{background-position:0% 50%}}.animate-gradient[_ngcontent-%COMP%]{background-size:200% 200%;animation:_ngcontent-%COMP%_gradient 3s ease infinite}.bg-pattern[_ngcontent-%COMP%]{background-image:url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%239C92AC" fill-opacity="0.1"><circle cx="30" cy="30" r="4"/></g></g></svg>')}`]})}return e})();var bb=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=q({type:e,selectors:[["app-why-choose-us"]],decls:78,vars:0,consts:[[1,"py-20","bg-white"],[1,"max-w-7xl","mx-auto","px-4","sm:px-6","lg:px-8"],[1,"text-center","mb-16"],[1,"text-4xl","md:text-5xl","font-bold","text-gray-900","mb-6"],[1,"text-xl","text-gray-600","max-w-3xl","mx-auto"],[1,"grid","grid-cols-1","md:grid-cols-2","lg:grid-cols-3","gap-8"],[1,"bg-gradient-to-br","from-blue-50","to-indigo-100","p-8","rounded-xl","hover:shadow-lg","transition-all","duration-300","transform","hover:-translate-y-2"],[1,"bg-blue-600","rounded-lg","w-12","h-12","flex","items-center","justify-center","mb-6"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6","text-white"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M13 10V3L4 14h7v7l9-11h-7z"],[1,"text-xl","font-semibold","text-gray-900","mb-4"],[1,"text-gray-600"],[1,"bg-gradient-to-br","from-green-50","to-emerald-100","p-8","rounded-xl","hover:shadow-lg","transition-all","duration-300","transform","hover:-translate-y-2"],[1,"bg-green-600","rounded-lg","w-12","h-12","flex","items-center","justify-center","mb-6"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z"],[1,"bg-gradient-to-br","from-purple-50","to-violet-100","p-8","rounded-xl","hover:shadow-lg","transition-all","duration-300","transform","hover:-translate-y-2"],[1,"bg-purple-600","rounded-lg","w-12","h-12","flex","items-center","justify-center","mb-6"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"],[1,"bg-gradient-to-br","from-orange-50","to-red-100","p-8","rounded-xl","hover:shadow-lg","transition-all","duration-300","transform","hover:-translate-y-2"],[1,"bg-orange-600","rounded-lg","w-12","h-12","flex","items-center","justify-center","mb-6"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"bg-gradient-to-br","from-teal-50","to-cyan-100","p-8","rounded-xl","hover:shadow-lg","transition-all","duration-300","transform","hover:-translate-y-2"],[1,"bg-teal-600","rounded-lg","w-12","h-12","flex","items-center","justify-center","mb-6"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"],[1,"bg-gradient-to-br","from-pink-50","to-rose-100","p-8","rounded-xl","hover:shadow-lg","transition-all","duration-300","transform","hover:-translate-y-2"],[1,"bg-pink-600","rounded-lg","w-12","h-12","flex","items-center","justify-center","mb-6"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"],[1,"mt-20","bg-gradient-to-r","from-blue-600","to-purple-600","rounded-2xl","p-8","md:p-12"],[1,"grid","grid-cols-1","md:grid-cols-4","gap-8","text-center","text-white"],[1,"text-4xl","font-bold","mb-2"],[1,"text-blue-100"]],template:function(r,o){r&1&&(d(0,"section",0)(1,"div",1)(2,"div",2)(3,"h2",3),p(4," Why Choose Our Platform? "),f(),d(5,"p",4),p(6," Experience the future of mental health support with our cutting-edge AI technology and evidence-based psychological approaches. "),f()(),d(7,"div",5)(8,"div",6)(9,"div",7),k(),d(10,"svg",8),D(11,"path",9),f()(),P(),d(12,"h3",10),p(13,"Instant Response"),f(),d(14,"p",11),p(15," Get immediate psychological support without waiting for appointments. Our AI responds instantly to your concerns. "),f()(),d(16,"div",12)(17,"div",13),k(),d(18,"svg",8),D(19,"path",14),f()(),P(),d(20,"h3",10),p(21,"Evidence-Based"),f(),d(22,"p",11),p(23," Built on proven psychological frameworks including CBT, DBT, and mindfulness techniques for effective mental health support. "),f()(),d(24,"div",15)(25,"div",16),k(),d(26,"svg",8),D(27,"path",17),f()(),P(),d(28,"h3",10),p(29,"Personalized Care"),f(),d(30,"p",11),p(31," Tailored responses based on your unique situation, personality, and mental health goals for maximum effectiveness. "),f()(),d(32,"div",18)(33,"div",19),k(),d(34,"svg",8),D(35,"path",20),f()(),P(),d(36,"h3",10),p(37,"Available 24/7"),f(),d(38,"p",11),p(39," Mental health doesn't follow business hours. Access support whenever you need it, day or night, from anywhere in the world. "),f()(),d(40,"div",21)(41,"div",22),k(),d(42,"svg",8),D(43,"path",23),f()(),P(),d(44,"h3",10),p(45,"Progress Tracking"),f(),d(46,"p",11),p(47," Monitor your mental health journey with detailed insights, mood tracking, and progress reports to see your improvement. "),f()(),d(48,"div",24)(49,"div",25),k(),d(50,"svg",8),D(51,"path",26),f()(),P(),d(52,"h3",10),p(53,"Compassionate AI"),f(),d(54,"p",11),p(55," Our AI is trained to provide empathetic, non-judgmental support that feels natural and understanding of your emotional needs. "),f()()(),d(56,"div",27)(57,"div",28)(58,"div")(59,"div",29),p(60,"10K+"),f(),d(61,"div",30),p(62,"Active Users"),f()(),d(63,"div")(64,"div",29),p(65,"95%"),f(),d(66,"div",30),p(67,"Satisfaction Rate"),f()(),d(68,"div")(69,"div",29),p(70,"24/7"),f(),d(71,"div",30),p(72,"Availability"),f()(),d(73,"div")(74,"div",29),p(75,"50+"),f(),d(76,"div",30),p(77,"Countries Served"),f()()()()()())},dependencies:[Z],styles:[".feature-card[_ngcontent-%COMP%]{transition:all .3s ease}.feature-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);box-shadow:0 20px 40px #0000001a}@keyframes _ngcontent-%COMP%_countUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.stat-number[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_countUp .6s ease-out}"]})}return e})();var Cb=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=q({type:e,selectors:[["app-transform-conversation"]],decls:80,vars:0,consts:[[1,"py-20","bg-gradient-to-br","from-gray-50","to-blue-50"],[1,"max-w-7xl","mx-auto","px-4","sm:px-6","lg:px-8"],[1,"text-center","mb-16"],[1,"text-4xl","md:text-5xl","font-bold","text-gray-900","mb-6"],[1,"text-transparent","bg-clip-text","bg-gradient-to-r","from-blue-600","to-purple-600"],[1,"text-xl","text-gray-600","max-w-3xl","mx-auto","mb-8"],[1,"grid","grid-cols-1","lg:grid-cols-2","gap-12","items-center"],[1,"space-y-8"],[1,"flex","items-start","space-x-4"],[1,"bg-green-100","rounded-full","p-2","flex-shrink-0"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6","text-green-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M5 13l4 4L19 7"],[1,"text-xl","font-semibold","text-gray-900","mb-2"],[1,"text-gray-600"],[1,"bg-blue-100","rounded-full","p-2","flex-shrink-0"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6","text-blue-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"],[1,"bg-purple-100","rounded-full","p-2","flex-shrink-0"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6","text-purple-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"],[1,"bg-orange-100","rounded-full","p-2","flex-shrink-0"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6","text-orange-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"],[1,"bg-white","rounded-2xl","shadow-xl","p-8","border","border-gray-100"],[1,"text-center"],[1,"bg-gradient-to-r","from-blue-600","to-purple-600","rounded-full","w-20","h-20","flex","items-center","justify-center","mx-auto","mb-6"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-10","h-10","text-white"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"],[1,"text-2xl","font-bold","text-gray-900","mb-4"],[1,"text-gray-600","mb-8"],[1,"bg-gray-50","rounded-lg","p-6","mb-8"],[1,"flex","items-center","justify-center","mb-4"],[1,"flex","text-yellow-400"],["fill","currentColor","viewBox","0 0 20 20",1,"w-5","h-5"],["d","M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"],[1,"text-sm","text-gray-600","italic"],[1,"text-xs","text-gray-500","mt-2"],[1,"space-y-4"],["routerLink","/sign-up",1,"w-full","bg-gradient-to-r","from-blue-600","to-purple-600","hover:from-blue-700","hover:to-purple-700","text-white","px-8","py-4","rounded-lg","text-lg","font-semibold","transition-all","duration-300","transform","hover:scale-105","shadow-lg","block","text-center"],["routerLink","/pricing",1,"w-full","border-2","border-gray-300","hover:border-blue-600","text-gray-700","hover:text-blue-600","px-8","py-4","rounded-lg","text-lg","font-semibold","transition-all","duration-300","block","text-center"],[1,"text-xs","text-gray-500","mt-4"]],template:function(r,o){r&1&&(d(0,"section",0)(1,"div",1)(2,"div",2)(3,"h2",3),p(4," Ready to Transform Your "),d(5,"span",4),p(6," Conversations? "),f()(),d(7,"p",5),p(8," Join thousands of users who have already transformed their mental health journey with our AI-powered psychology platform. Start your path to better mental wellness today. "),f()(),d(9,"div",6)(10,"div",7)(11,"div",8)(12,"div",9),k(),d(13,"svg",10),D(14,"path",11),f()(),P(),d(15,"div")(16,"h3",12),p(17,"Immediate Support"),f(),d(18,"p",13),p(19," No more waiting weeks for appointments. Get professional psychological guidance the moment you need it, available 24/7. "),f()()(),d(20,"div",8)(21,"div",14),k(),d(22,"svg",15),D(23,"path",16),f()(),P(),d(24,"div")(25,"h3",12),p(26,"Complete Privacy"),f(),d(27,"p",13),p(28," Your conversations are encrypted and completely confidential. Share your thoughts without fear of judgment or privacy concerns. "),f()()(),d(29,"div",8)(30,"div",17),k(),d(31,"svg",18),D(32,"path",19),f()(),P(),d(33,"div")(34,"h3",12),p(35,"Track Your Progress"),f(),d(36,"p",13),p(37," Monitor your mental health journey with detailed insights and progress tracking to see how you're improving over time. "),f()()(),d(38,"div",8)(39,"div",20),k(),d(40,"svg",21),D(41,"path",22),f()(),P(),d(42,"div")(43,"h3",12),p(44,"Affordable Care"),f(),d(45,"p",13),p(46," Get professional-level psychological support at a fraction of the cost of traditional therapy sessions. "),f()()()(),d(47,"div",23)(48,"div",24)(49,"div",25),k(),d(50,"svg",26),D(51,"path",27),f()(),P(),d(52,"h3",28),p(53," Start Your Journey Today "),f(),d(54,"p",29),p(55," Join over 10,000 users who have already transformed their mental health with our AI-powered psychology platform. "),f(),d(56,"div",30)(57,"div",31)(58,"div",32),k(),d(59,"svg",33),D(60,"path",34),f(),d(61,"svg",33),D(62,"path",34),f(),d(63,"svg",33),D(64,"path",34),f(),d(65,"svg",33),D(66,"path",34),f(),d(67,"svg",33),D(68,"path",34),f()()(),P(),d(69,"p",35),p(70,' "This platform has been a game-changer for my mental health. The AI is incredibly understanding and helpful." '),f(),d(71,"p",36),p(72,"- Sarah M., Verified User"),f()(),d(73,"div",37)(74,"a",38),p(75," Start Free Trial "),f(),d(76,"a",39),p(77," View Pricing Plans "),f()(),d(78,"p",40),p(79," No credit card required \u2022 Cancel anytime \u2022 7-day free trial "),f()()()()()())},dependencies:[Z,Be,je],styles:['.cta-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff,#f8fafc);border:1px solid #e2e8f0}.cta-card[_ngcontent-%COMP%]:hover{box-shadow:0 25px 50px #0000001a;transform:translateY(-2px);transition:all .3s ease}.cta-button[_ngcontent-%COMP%]{position:relative;overflow:hidden}.cta-button[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.cta-button[_ngcontent-%COMP%]:hover:before{left:100%}']})}return e})();var Lo=(()=>{class e{currentYear=new Date().getFullYear();static \u0275fac=function(r){return new(r||e)};static \u0275cmp=q({type:e,selectors:[["app-footer"]],decls:69,vars:1,consts:[[1,"bg-gray-900","text-white"],[1,"max-w-7xl","mx-auto","px-4","sm:px-6","lg:px-8","py-12"],[1,"grid","grid-cols-1","md:grid-cols-4","gap-8"],[1,"col-span-1","md:col-span-2"],[1,"flex","items-center","mb-4"],["src","/assets/logo-white.png","alt","Psychology Chat",1,"h-8","w-auto"],[1,"ml-2","text-xl","font-bold"],[1,"text-gray-400","mb-6","max-w-md"],[1,"flex","space-x-4"],["href","#",1,"text-gray-400","hover:text-white","transition-colors"],["fill","currentColor","viewBox","0 0 24 24",1,"w-6","h-6"],["d","M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"],["d","M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"],["d","M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"],[1,"text-lg","font-semibold","mb-4"],[1,"space-y-2"],["routerLink","/",1,"text-gray-400","hover:text-white","transition-colors"],["routerLink","/about",1,"text-gray-400","hover:text-white","transition-colors"],["routerLink","/pricing",1,"text-gray-400","hover:text-white","transition-colors"],["routerLink","/sign-in",1,"text-gray-400","hover:text-white","transition-colors"],["routerLink","/sign-up",1,"text-gray-400","hover:text-white","transition-colors"],[1,"border-t","border-gray-800","mt-8","pt-8"],[1,"flex","flex-col","md:flex-row","justify-between","items-center"],[1,"text-gray-400","text-sm"],[1,"flex","items-center","space-x-4","mt-4","md:mt-0"],[1,"flex","items-center","space-x-2"],[1,"w-2","h-2","bg-green-500","rounded-full","animate-pulse"],[1,"text-green-500","text-sm"]],template:function(r,o){r&1&&(d(0,"footer",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4),D(5,"img",5),d(6,"span",6),p(7,"Psychology Chat"),f()(),d(8,"p",7),p(9," Transform your mental health journey with AI-powered psychological support. Available 24/7, completely private, and built on evidence-based practices. "),f(),d(10,"div",8)(11,"a",9),k(),d(12,"svg",10),D(13,"path",11),f()(),P(),d(14,"a",9),k(),d(15,"svg",10),D(16,"path",12),f()(),P(),d(17,"a",9),k(),d(18,"svg",10),D(19,"path",13),f()()()(),P(),d(20,"div")(21,"h3",14),p(22,"Quick Links"),f(),d(23,"ul",15)(24,"li")(25,"a",16),p(26,"Home"),f()(),d(27,"li")(28,"a",17),p(29,"About"),f()(),d(30,"li")(31,"a",18),p(32,"Pricing"),f()(),d(33,"li")(34,"a",19),p(35,"Sign In"),f()(),d(36,"li")(37,"a",20),p(38,"Sign Up"),f()()()(),d(39,"div")(40,"h3",14),p(41,"Support"),f(),d(42,"ul",15)(43,"li")(44,"a",9),p(45,"Help Center"),f()(),d(46,"li")(47,"a",9),p(48,"Privacy Policy"),f()(),d(49,"li")(50,"a",9),p(51,"Terms of Service"),f()(),d(52,"li")(53,"a",9),p(54,"Contact Us"),f()(),d(55,"li")(56,"a",9),p(57,"FAQ"),f()()()()(),d(58,"div",21)(59,"div",22)(60,"p",23),p(61),f(),d(62,"div",24)(63,"span",23),p(64,"Powered by AI"),f(),d(65,"div",25),D(66,"div",26),d(67,"span",27),p(68,"Online"),f()()()()()()()),r&2&&(C(61),ke(" \xA9 ",o.currentYear," Psychology Chat. All rights reserved. "))},dependencies:[Z,Be,je],styles:["footer[_ngcontent-%COMP%]{background:linear-gradient(135deg,#1f2937,#111827)}.social-link[_ngcontent-%COMP%]{transition:all .3s ease}.social-link[_ngcontent-%COMP%]:hover{transform:translateY(-2px)}@keyframes _ngcontent-%COMP%_pulse{0%,to{opacity:1}50%{opacity:.5}}.animate-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}"]})}return e})();var wb=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=q({type:e,selectors:[["app-landing"]],decls:7,vars:0,consts:[[1,"min-h-screen","bg-gray-50"]],template:function(r,o){r&1&&(d(0,"div",0),D(1,"app-navbar"),d(2,"main"),D(3,"app-hero-banner")(4,"app-why-choose-us")(5,"app-transform-conversation"),f(),D(6,"app-footer"),f())},dependencies:[Z,Be,Fo,yb,bb,Cb,Lo],styles:[".landing-container[_ngcontent-%COMP%]{min-height:100vh;background:linear-gradient(135deg,#667eea,#764ba2)}"]})}return e})();var Db=(()=>{class e{http;SAFEPAY_API_URL="https://api.getsafepay.pk/v1";SAFEPAY_API_KEY="your-safepay-api-key";constructor(t){this.http=t}createPayment(t){let r=this.generateOrderId(),o={amount:t.amount*100,currency:t.currency,order_id:r,description:`${t.name} - ${t.interval} subscription`,success_url:`${window.location.origin}/payment/success`,cancel_url:`${window.location.origin}/payment/cancel`,customer:{email:"<EMAIL>",first_name:"User",last_name:"Name"},metadata:{plan_type:t.name,interval:t.interval,user_id:"current-user-id"}};this.initiatePayment(o).subscribe({next:i=>{window.location.href=i.checkout_url},error:i=>{console.error("Payment initiation failed:",i),alert("Payment initiation failed. Please try again.")}})}initiatePayment(t){let r=new it({Authorization:`Bearer ${this.SAFEPAY_API_KEY}`,"Content-Type":"application/json"});return this.http.post(`${this.SAFEPAY_API_URL}/payments`,t,{headers:r})}verifyPayment(t){let r=new it({Authorization:`Bearer ${this.SAFEPAY_API_KEY}`,"Content-Type":"application/json"});return this.http.get(`${this.SAFEPAY_API_URL}/payments/${t}`,{headers:r})}createRecurringPayment(t){let r=new it({Authorization:`Bearer ${this.SAFEPAY_API_KEY}`,"Content-Type":"application/json"}),o={amount:t.amount*100,currency:t.currency,interval:t.interval,description:`${t.name} - Recurring ${t.interval} subscription`,customer:{email:"<EMAIL>",first_name:"User",last_name:"Name"}};return this.http.post(`${this.SAFEPAY_API_URL}/subscriptions`,o,{headers:r})}cancelSubscription(t){let r=new it({Authorization:`Bearer ${this.SAFEPAY_API_KEY}`,"Content-Type":"application/json"});return this.http.delete(`${this.SAFEPAY_API_URL}/subscriptions/${t}`,{headers:r})}generateOrderId(){let t=Date.now(),r=Math.random().toString(36).substring(2,15);return`order_${t}_${r}`}handleWebhook(t){switch(t.event){case"payment.success":this.handlePaymentSuccess(t.data);break;case"payment.failed":this.handlePaymentFailed(t.data);break;case"subscription.created":this.handleSubscriptionCreated(t.data);break;case"subscription.cancelled":this.handleSubscriptionCancelled(t.data);break;default:console.log("Unhandled webhook event:",t.event)}}handlePaymentSuccess(t){console.log("Payment successful:",t)}handlePaymentFailed(t){console.log("Payment failed:",t)}handleSubscriptionCreated(t){console.log("Subscription created:",t)}handleSubscriptionCancelled(t){console.log("Subscription cancelled:",t)}static \u0275fac=function(r){return new(r||e)(T(vr))};static \u0275prov=S({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var _b=(()=>{class e{safepayService;isYearly=!1;basicPlan={monthlyPrice:9,yearlyPrice:72};intermediatePlan={monthlyPrice:19,yearlyPrice:152};advancedPlan={monthlyPrice:39,yearlyPrice:312};constructor(t){this.safepayService=t}toggleBilling(){this.isYearly=!this.isYearly}selectPlan(t){let r=this.getPlanDetails(t);this.safepayService.createPayment(r)}getPlanDetails(t){return{basic:{name:"Basic Plan",amount:this.isYearly?this.basicPlan.yearlyPrice:this.basicPlan.monthlyPrice,currency:"USD",interval:this.isYearly?"yearly":"monthly"},intermediate:{name:"Intermediate Plan",amount:this.isYearly?this.intermediatePlan.yearlyPrice:this.intermediatePlan.monthlyPrice,currency:"USD",interval:this.isYearly?"yearly":"monthly"},advanced:{name:"Advanced Plan",amount:this.isYearly?this.advancedPlan.yearlyPrice:this.advancedPlan.monthlyPrice,currency:"USD",interval:this.isYearly?"yearly":"monthly"}}[t]}static \u0275fac=function(r){return new(r||e)(E(Db))};static \u0275cmp=q({type:e,selectors:[["app-pricing"]],decls:143,vars:18,consts:[[1,"min-h-screen","bg-gray-50"],[1,"pt-20"],[1,"py-16","bg-gradient-to-br","from-blue-600","to-purple-600","text-white"],[1,"max-w-7xl","mx-auto","px-4","sm:px-6","lg:px-8","text-center"],[1,"text-4xl","md:text-5xl","font-bold","mb-6"],[1,"text-xl","text-blue-100","max-w-3xl","mx-auto"],[1,"py-8"],[1,"max-w-7xl","mx-auto","px-4","sm:px-6","lg:px-8"],[1,"flex","justify-center","items-center","space-x-4"],[1,"text-gray-600"],[1,"relative","inline-flex","h-6","w-11","items-center","rounded-full","transition-colors","focus:outline-none","focus:ring-2","focus:ring-blue-500","focus:ring-offset-2",3,"click"],[1,"inline-block","h-4","w-4","transform","rounded-full","bg-white","transition-transform"],[1,"text-green-600","text-sm","font-medium"],[1,"py-16"],[1,"grid","grid-cols-1","md:grid-cols-3","gap-8"],[1,"bg-white","rounded-2xl","shadow-lg","border","border-gray-200","p-8","relative"],[1,"text-center"],[1,"text-2xl","font-bold","text-gray-900","mb-4"],[1,"mb-6"],[1,"text-4xl","font-bold","text-gray-900"],[1,"space-y-4","mb-8","text-left"],[1,"flex","items-center"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-green-500","mr-3"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M5 13l4 4L19 7"],[1,"w-full","bg-gray-900","hover:bg-gray-800","text-white","px-6","py-3","rounded-lg","font-semibold","transition-colors",3,"click"],[1,"bg-white","rounded-2xl","shadow-xl","border-2","border-blue-500","p-8","relative","transform","scale-105"],[1,"absolute","-top-4","left-1/2","transform","-translate-x-1/2"],[1,"bg-blue-500","text-white","px-4","py-1","rounded-full","text-sm","font-medium"],[1,"w-full","bg-blue-600","hover:bg-blue-700","text-white","px-6","py-3","rounded-lg","font-semibold","transition-colors",3,"click"],[1,"w-full","bg-purple-600","hover:bg-purple-700","text-white","px-6","py-3","rounded-lg","font-semibold","transition-colors",3,"click"],[1,"py-16","bg-white"],[1,"max-w-4xl","mx-auto","px-4","sm:px-6","lg:px-8"],[1,"text-3xl","font-bold","text-center","text-gray-900","mb-12"],[1,"space-y-6"],[1,"border","border-gray-200","rounded-lg","p-6"],[1,"text-lg","font-semibold","text-gray-900","mb-2"]],template:function(r,o){r&1&&(d(0,"div",0),D(1,"app-navbar"),d(2,"main",1)(3,"section",2)(4,"div",3)(5,"h1",4),p(6," Choose Your Perfect Plan "),f(),d(7,"p",5),p(8," Get the mental health support you deserve with our flexible pricing options. All plans include 24/7 AI support and complete privacy. "),f()()(),d(9,"section",6)(10,"div",7)(11,"div",8)(12,"span",9),p(13,"Monthly"),f(),d(14,"button",10),V("click",function(){return o.toggleBilling()}),D(15,"span",11),f(),d(16,"span",9),p(17," Yearly "),d(18,"span",12),p(19,"(Save 20%)"),f()()()()(),d(20,"section",13)(21,"div",7)(22,"div",14)(23,"div",15)(24,"div",16)(25,"h3",17),p(26,"Basic"),f(),d(27,"div",18)(28,"span",19),p(29),f(),d(30,"span",9),p(31),f()(),d(32,"ul",20)(33,"li",21),k(),d(34,"svg",22),D(35,"path",23),f(),p(36," 50 AI conversations per month "),f(),P(),d(37,"li",21),k(),d(38,"svg",22),D(39,"path",23),f(),p(40," Basic mood tracking "),f(),P(),d(41,"li",21),k(),d(42,"svg",22),D(43,"path",23),f(),p(44," Email support "),f(),P(),d(45,"li",21),k(),d(46,"svg",22),D(47,"path",23),f(),p(48," Mobile app access "),f()(),P(),d(49,"button",24),V("click",function(){return o.selectPlan("basic")}),p(50," Get Started "),f()()(),d(51,"div",25)(52,"div",26)(53,"span",27),p(54," Most Popular "),f()(),d(55,"div",16)(56,"h3",17),p(57,"Intermediate"),f(),d(58,"div",18)(59,"span",19),p(60),f(),d(61,"span",9),p(62),f()(),d(63,"ul",20)(64,"li",21),k(),d(65,"svg",22),D(66,"path",23),f(),p(67," 200 AI conversations per month "),f(),P(),d(68,"li",21),k(),d(69,"svg",22),D(70,"path",23),f(),p(71," Advanced mood & progress tracking "),f(),P(),d(72,"li",21),k(),d(73,"svg",22),D(74,"path",23),f(),p(75," Priority support "),f(),P(),d(76,"li",21),k(),d(77,"svg",22),D(78,"path",23),f(),p(79," Personalized insights "),f(),P(),d(80,"li",21),k(),d(81,"svg",22),D(82,"path",23),f(),p(83," Export conversation history "),f()(),P(),d(84,"button",28),V("click",function(){return o.selectPlan("intermediate")}),p(85," Get Started "),f()()(),d(86,"div",15)(87,"div",16)(88,"h3",17),p(89,"Advanced"),f(),d(90,"div",18)(91,"span",19),p(92),f(),d(93,"span",9),p(94),f()(),d(95,"ul",20)(96,"li",21),k(),d(97,"svg",22),D(98,"path",23),f(),p(99," Unlimited AI conversations "),f(),P(),d(100,"li",21),k(),d(101,"svg",22),D(102,"path",23),f(),p(103," Advanced analytics & reports "),f(),P(),d(104,"li",21),k(),d(105,"svg",22),D(106,"path",23),f(),p(107," 24/7 priority support "),f(),P(),d(108,"li",21),k(),d(109,"svg",22),D(110,"path",23),f(),p(111," Custom AI personality "),f(),P(),d(112,"li",21),k(),d(113,"svg",22),D(114,"path",23),f(),p(115," API access for integrations "),f(),P(),d(116,"li",21),k(),d(117,"svg",22),D(118,"path",23),f(),p(119," White-label options "),f()(),P(),d(120,"button",29),V("click",function(){return o.selectPlan("advanced")}),p(121," Get Started "),f()()()()()(),d(122,"section",30)(123,"div",31)(124,"h2",32),p(125," Frequently Asked Questions "),f(),d(126,"div",33)(127,"div",34)(128,"h3",35),p(129," Can I change my plan anytime? "),f(),d(130,"p",9),p(131," Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle. "),f()(),d(132,"div",34)(133,"h3",35),p(134," Is there a free trial? "),f(),d(135,"p",9),p(136," Yes, all plans come with a 7-day free trial. No credit card required to start. "),f()(),d(137,"div",34)(138,"h3",35),p(139," How secure is my data? "),f(),d(140,"p",9),p(141," Your data is encrypted end-to-end and stored securely. We never share your personal information with third parties. "),f()()()()()(),D(142,"app-footer"),f()),r&2&&(C(12),dt("text-gray-900",!o.isYearly),C(2),dt("bg-blue-600",o.isYearly)("bg-gray-200",!o.isYearly),C(),dt("translate-x-6",o.isYearly)("translate-x-1",!o.isYearly),C(),dt("text-gray-900",o.isYearly),C(13),ke(" $",o.isYearly?o.basicPlan.yearlyPrice:o.basicPlan.monthlyPrice," "),C(2),fe(o.isYearly?"/year":"/month"),C(29),ke(" $",o.isYearly?o.intermediatePlan.yearlyPrice:o.intermediatePlan.monthlyPrice," "),C(2),fe(o.isYearly?"/year":"/month"),C(30),ke(" $",o.isYearly?o.advancedPlan.yearlyPrice:o.advancedPlan.monthlyPrice," "),C(2),fe(o.isYearly?"/year":"/month"))},dependencies:[Z,Be,Fo,Lo],styles:[".pricing-card[_ngcontent-%COMP%]{transition:all .3s ease}.pricing-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 20px 40px #0000001a}.popular-badge[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3b82f6,#1d4ed8)}.billing-toggle[_ngcontent-%COMP%]{transition:all .3s ease}@keyframes _ngcontent-%COMP%_slideUp{0%{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}.plan-card[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideUp .6s ease-out}.plan-card[_ngcontent-%COMP%]:nth-child(2){animation-delay:.1s}.plan-card[_ngcontent-%COMP%]:nth-child(3){animation-delay:.2s}"]})}return e})();var Eb=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=q({type:e,selectors:[["app-about"]],decls:129,vars:0,consts:[[1,"min-h-screen","bg-gray-50"],[1,"pt-20"],[1,"py-16","bg-gradient-to-br","from-blue-600","to-purple-600","text-white"],[1,"max-w-7xl","mx-auto","px-4","sm:px-6","lg:px-8","text-center"],[1,"text-4xl","md:text-5xl","font-bold","mb-6"],[1,"text-xl","text-blue-100","max-w-3xl","mx-auto"],[1,"py-16","bg-white"],[1,"max-w-7xl","mx-auto","px-4","sm:px-6","lg:px-8"],[1,"grid","grid-cols-1","lg:grid-cols-2","gap-12","items-center"],[1,"text-3xl","font-bold","text-gray-900","mb-6"],[1,"text-lg","text-gray-600","mb-6"],[1,"flex","items-center","space-x-4"],[1,"bg-blue-100","rounded-full","p-3"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6","text-blue-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"],[1,"text-gray-700","font-medium"],[1,"bg-gradient-to-br","from-blue-50","to-purple-50","rounded-2xl","p-8"],[1,"grid","grid-cols-2","gap-6","text-center"],[1,"text-3xl","font-bold","text-blue-600","mb-2"],[1,"text-gray-600"],[1,"text-3xl","font-bold","text-purple-600","mb-2"],[1,"text-3xl","font-bold","text-green-600","mb-2"],[1,"text-3xl","font-bold","text-orange-600","mb-2"],[1,"py-16","bg-gray-50"],[1,"text-center","mb-12"],[1,"text-3xl","font-bold","text-gray-900","mb-4"],[1,"text-lg","text-gray-600","max-w-3xl","mx-auto"],[1,"grid","grid-cols-1","md:grid-cols-3","gap-8"],[1,"bg-white","rounded-xl","p-8","shadow-lg","text-center"],[1,"bg-blue-100","rounded-full","w-16","h-16","flex","items-center","justify-center","mx-auto","mb-6"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-8","h-8","text-blue-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"],[1,"text-xl","font-semibold","text-gray-900","mb-4"],[1,"bg-green-100","rounded-full","w-16","h-16","flex","items-center","justify-center","mx-auto","mb-6"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-8","h-8","text-green-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z"],[1,"bg-purple-100","rounded-full","w-16","h-16","flex","items-center","justify-center","mx-auto","mb-6"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-8","h-8","text-purple-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"],[1,"text-center"],[1,"bg-gradient-to-br","from-blue-400","to-blue-600","rounded-full","w-24","h-24","flex","items-center","justify-center","mx-auto","mb-4"],[1,"text-white","text-2xl","font-bold"],[1,"text-lg","font-semibold","text-gray-900","mb-2"],[1,"text-blue-600","mb-2"],[1,"text-gray-600","text-sm"],[1,"bg-gradient-to-br","from-purple-400","to-purple-600","rounded-full","w-24","h-24","flex","items-center","justify-center","mx-auto","mb-4"],[1,"text-purple-600","mb-2"],[1,"bg-gradient-to-br","from-green-400","to-green-600","rounded-full","w-24","h-24","flex","items-center","justify-center","mx-auto","mb-4"],[1,"text-green-600","mb-2"],[1,"max-w-4xl","mx-auto","px-4","sm:px-6","lg:px-8","text-center"],[1,"text-3xl","font-bold","mb-6"],[1,"text-xl","text-blue-100","mb-8"],[1,"flex","flex-col","sm:flex-row","gap-4","justify-center"],["routerLink","/sign-up",1,"bg-white","text-blue-600","hover:bg-gray-100","px-8","py-4","rounded-lg","text-lg","font-semibold","transition-colors"],["routerLink","/pricing",1,"border-2","border-white","text-white","hover:bg-white","hover:text-blue-600","px-8","py-4","rounded-lg","text-lg","font-semibold","transition-colors"]],template:function(r,o){r&1&&(d(0,"div",0),D(1,"app-navbar"),d(2,"main",1)(3,"section",2)(4,"div",3)(5,"h1",4),p(6," About Psychology Chat "),f(),d(7,"p",5),p(8," We're revolutionizing mental health support through AI-powered psychology, making professional guidance accessible to everyone, everywhere. "),f()()(),d(9,"section",6)(10,"div",7)(11,"div",8)(12,"div")(13,"h2",9),p(14,"Our Mission"),f(),d(15,"p",10),p(16," Mental health support shouldn't be limited by geography, time zones, or financial barriers. Our mission is to democratize access to psychological guidance through cutting-edge AI technology. "),f(),d(17,"p",10),p(18," We believe everyone deserves immediate, professional-quality mental health support when they need it most. Our AI-powered platform provides evidence-based psychological interventions available 24/7. "),f(),d(19,"div",11)(20,"div",12),k(),d(21,"svg",13),D(22,"path",14),f()(),P(),d(23,"span",15),p(24,"Compassionate AI for everyone"),f()()(),d(25,"div",16)(26,"div",17)(27,"div")(28,"div",18),p(29,"10K+"),f(),d(30,"div",19),p(31,"Users Helped"),f()(),d(32,"div")(33,"div",20),p(34,"24/7"),f(),d(35,"div",19),p(36,"Availability"),f()(),d(37,"div")(38,"div",21),p(39,"95%"),f(),d(40,"div",19),p(41,"Satisfaction"),f()(),d(42,"div")(43,"div",22),p(44,"50+"),f(),d(45,"div",19),p(46,"Countries"),f()()()()()()(),d(47,"section",23)(48,"div",7)(49,"div",24)(50,"h2",25),p(51,"Our Values"),f(),d(52,"p",26),p(53," These core values guide everything we do and shape how we approach mental health support. "),f()(),d(54,"div",27)(55,"div",28)(56,"div",29),k(),d(57,"svg",30),D(58,"path",31),f()(),P(),d(59,"h3",32),p(60,"Privacy First"),f(),d(61,"p",19),p(62," Your conversations are completely confidential and encrypted. We never share your personal information. "),f()(),d(63,"div",28)(64,"div",33),k(),d(65,"svg",34),D(66,"path",35),f()(),P(),d(67,"h3",32),p(68,"Evidence-Based"),f(),d(69,"p",19),p(70," Our AI is trained on proven psychological frameworks including CBT, DBT, and mindfulness techniques. "),f()(),d(71,"div",28)(72,"div",36),k(),d(73,"svg",37),D(74,"path",38),f()(),P(),d(75,"h3",32),p(76,"Accessibility"),f(),d(77,"p",19),p(78," Mental health support should be available to everyone, regardless of location, time, or financial situation. "),f()()()()(),d(79,"section",6)(80,"div",7)(81,"div",24)(82,"h2",25),p(83,"Our Team"),f(),d(84,"p",26),p(85," We're a diverse team of psychologists, AI researchers, and engineers passionate about mental health. "),f()(),d(86,"div",27)(87,"div",39)(88,"div",40)(89,"span",41),p(90,"DR"),f()(),d(91,"h3",42),p(92,"Dr. Sarah Johnson"),f(),d(93,"p",43),p(94,"Clinical Psychologist & Co-Founder"),f(),d(95,"p",44),p(96," 15+ years in clinical psychology, specializing in CBT and digital mental health interventions. "),f()(),d(97,"div",39)(98,"div",45)(99,"span",41),p(100,"AM"),f()(),d(101,"h3",42),p(102,"Alex Martinez"),f(),d(103,"p",46),p(104,"AI Research Lead"),f(),d(105,"p",44),p(106," PhD in Machine Learning with focus on natural language processing and conversational AI. "),f()(),d(107,"div",39)(108,"div",47)(109,"span",41),p(110,"MK"),f()(),d(111,"h3",42),p(112,"Maria Kim"),f(),d(113,"p",48),p(114,"Product Manager"),f(),d(115,"p",44),p(116," Former healthcare product manager with expertise in user experience and digital health platforms. "),f()()()()(),d(117,"section",2)(118,"div",49)(119,"h2",50),p(120,"Ready to Start Your Journey?"),f(),d(121,"p",51),p(122," Join thousands of users who have already transformed their mental health with our platform. "),f(),d(123,"div",52)(124,"a",53),p(125," Start Free Trial "),f(),d(126,"a",54),p(127," View Pricing "),f()()()()(),D(128,"app-footer"),f())},dependencies:[Z,Be,je,Fo,Lo],styles:[".team-member[_ngcontent-%COMP%]{transition:all .3s ease}.team-member[_ngcontent-%COMP%]:hover{transform:translateY(-4px)}.value-card[_ngcontent-%COMP%]{transition:all .3s ease}.value-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 10px 30px #0000001a}@keyframes _ngcontent-%COMP%_countUp{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}.stat-number[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_countUp .6s ease-out}"]})}return e})();var xb=()=>{let e=w(In),n=w(Ae);return e.isLoggedIn()?!0:n.parseUrl("/sign-in")},ih=()=>{let e=w(In),n=w(Ae);return e.isLoggedIn()?n.parseUrl("/chat-layout"):!0};var Sb=[{path:"",component:wb},{path:"pricing",component:_b},{path:"about",component:Eb},{path:"sign-in",component:Gy,canActivate:[ih]},{path:"sign-up",component:qy,canActivate:[ih]},{path:"chat-layout",component:mb,canActivate:[xb]},{path:"**",redirectTo:"/"}];var Mb={providers:[Rv({eventCoalescing:!0}),yy(Sb),g0(m0())]};var Gl={timestamp:new Date().toISOString(),buildId:Math.random().toString(36).substring(2,15)};var Ib=(()=>{class e{themeService;title="AI Chat";constructor(t){this.themeService=t}ngOnInit(){console.log(`App Version: ${Gl.buildId}`),console.log(`Build Time: ${Gl.timestamp}`),window.appVersion=Gl}static \u0275fac=function(r){return new(r||e)(E(No))};static \u0275cmp=q({type:e,selectors:[["app-root"]],decls:1,vars:0,template:function(r,o){r&1&&D(0,"router-outlet")},dependencies:[Be,Af,Z],styles:["[_nghost-%COMP%]{display:block;height:100vh}"],changeDetection:0})}return e})();S0(Ib,Mb).catch(e=>console.error(e));
