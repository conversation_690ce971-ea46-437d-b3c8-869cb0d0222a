{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@experiments/be-auth": ["libs/be-auth/src/index.ts"], "@experiments/be-database": ["libs/be-database/src/index.ts"], "@experiments/be-chat": ["libs/be-chat/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}