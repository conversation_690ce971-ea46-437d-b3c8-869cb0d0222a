import { Injectable, Inject } from '@nestjs/common';
import { Knex } from 'knex';
import { User, UserModel } from '../models/be-user.model';

@Injectable()
export class UserRepository {
  constructor(@Inject('KnexConnection') private readonly knex: Knex) {}

  async createUser(user: Partial<User>): Promise<User> {
    const [createdUser] = await this.knex('users').insert(user).returning('*');
    return UserModel.parse(createdUser);
  }

  async findByEmail(email: string): Promise<User | null> {
    const user = await this.knex('users').where({ email }).first();
    return user ? UserModel.parse(user) : null;
  }
}
