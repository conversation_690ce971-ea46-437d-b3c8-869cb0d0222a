#!/bin/bash

# Determine which docker compose command to use
if command -v docker-compose &> /dev/null; then
  DOCKER_COMPOSE="docker-compose"
elif command -v docker &> /dev/null && docker compose version &> /dev/null; then
  DOCKER_COMPOSE="docker compose"
else
  echo "Error: Neither docker-compose nor docker compose is available"
  exit 1
fi

# Function to stop and remove containers
stop_containers() {
  echo "Stopping and removing containers..."
  
  # Stop and remove containers from main docker-compose
  $DOCKER_COMPOSE down -v --rmi all
  
  # Stop and remove containers from database docker-compose
  cd docker/db
  $DOCKER_COMPOSE down -v --rmi all
  cd ../..
  
  # Remove any remaining containers with project-related names
  docker ps -a | grep -E "postgres_db|be_psychology_chat" | awk '{print $1}' | xargs -r docker rm -f
}

# Function to remove images
remove_images() {
  echo "Removing project-related images..."
  
  # Remove images related to the project
  docker images | grep -E "psychology-chat|postgres" | awk '{print $3}' | xargs -r docker rmi -f
}

# Function to remove volumes
remove_volumes() {
  echo "Removing project-related volumes..."
  
  # Remove volumes related to the project
  docker volume ls | grep -E "psychology_chat|postgres" | awk '{print $2}' | xargs -r docker volume rm
}

# Function to remove networks
remove_networks() {
  echo "Removing project-related networks..."
  
  # Remove networks related to the project
  docker network ls | grep -E "db_default|flowise_network" | awk '{print $2}' | xargs -r docker network rm
}

# Main script
main() {
  stop_containers
  remove_images
  remove_volumes
  remove_networks
  
  echo "Cleanup complete."
}

# Run the main function
main
