import { z } from 'zod';
import { createZodDto } from '@anatine/zod-nestjs';
import { MessageModel } from '../models/message.model';
import { AgentReasoningStepModel } from '../models/agent-reasoning-step.model';
import { SourceDocumentModel } from '../models/source-document.model';

// Define Zod Schema for UpdateChatDto
export const UpdateChatSchema = z.object({
  title: z.string().min(1, 'Title is required').optional(),
  messages: z.array(MessageModel).optional(),
  flowChatId: z.string().optional(),
  sessionId: z.string().optional(),
  reasoningSteps: z.array(AgentReasoningStepModel).optional(),
  sourceDocuments: z.array(SourceDocumentModel).optional(),
});

// Convert Zod Schema into NestJS DTO for Validation
export class UpdateChatDto extends createZodDto(UpdateChatSchema) {}

// Infer TypeScript Type from Zod Schema
export type UpdateChatDtoType = z.infer<typeof UpdateChatSchema>;
